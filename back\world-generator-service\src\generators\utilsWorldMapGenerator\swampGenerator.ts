import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { CancellationToken, delay } from '../../utils/asyncUtils';
import { TerrainType, WorldMapDecorations } from 'src/shared';

// Асинхронная генерация болота с поддержкой отмены операций
export async function generateSwampAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.SWAMP;
  let operationCount = 0;
  
  // Генерация основного болота
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Swamp generation was cancelled');
    }
    
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.SWAMP;
            grid[cellKey].terrain = TerrainType.WASTELAND;
          }
        }
        
        // Освобождаем event loop каждые 100 операций
        operationCount++;
        if (operationCount % 100 === 0) {
          await delay(0);
        }
      }
    }
  }
  
  // Добавляем мертвые деревья вокруг болота с шансом 30%
  for (let x = center.x - config.MAX_RADIUS - 1; x <= center.x + config.MAX_RADIUS + 1; x++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Swamp dead trees generation was cancelled');
    }
    
    for (let y = center.y - config.MAX_RADIUS - 1; y <= center.y + config.MAX_RADIUS + 1; y++) {
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
      
      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;
      
      // Мертвые деревья появляются рядом с болотом, но не в самом болоте, с шансом 30%
      if (distance > config.MAX_RADIUS && distance <= config.MAX_RADIUS + 1 && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          rng() * 100 < 30) {
        grid[cellKey].decoration = WorldMapDecorations.FOREST;
        grid[cellKey].terrain = TerrainType.WASTELAND;
      }
      
      // Освобождаем event loop каждые 50 операций для мертвых деревьев
      operationCount++;
      if (operationCount % 50 === 0) {
        await delay(0);
      }
    }
  }
}

// Экспорт для обратной совместимости
export const generateSwamp = generateSwampAsync;