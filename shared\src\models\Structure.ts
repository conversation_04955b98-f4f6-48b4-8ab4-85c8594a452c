import { LocationType } from '../enums'
import { Position } from './Player'

export interface StructureResource {
  type: string
  amount: number
  maxAmount: number
  regenerationRate?: number
}

export interface StructureUpgrade {
  id: string
  name: string
  description: string
  cost: Record<string, number>
  requirements: string[]
  isUnlocked: boolean
  isBuilt: boolean
}

export interface Structure {
  id: string
  name: string
  description: string
  type: LocationType
  
  // Position and size
  position: Position
  size: {
    width: number
    height: number
  }
  
  // Structure properties
  level: number
  maxLevel: number
  durability: number
  maxDurability: number
  
  // Resources and storage
  resources: StructureResource[]
  storageCapacity: number
  currentStorage: number
  
  // Upgrades and improvements
  availableUpgrades: StructureUpgrade[]
  completedUpgrades: string[]
  
  // Population and workers
  maxPopulation: number
  currentPopulation: number
  workers: string[] // NPC IDs
  
  // Defense and security
  defenseRating: number
  securityLevel: number
  isUnderAttack: boolean
  
  // Power and utilities
  powerConsumption: number
  powerGeneration: number
  hasWater: boolean
  hasElectricity: boolean
  
  // Access and ownership
  ownerId?: string // Player ID
  factionId?: string
  isPublic: boolean
  accessLevel: 'public' | 'faction' | 'private'
  
  // Connected structures
  connectedStructures: string[] // Structure IDs
  tradeRoutes: string[] // Route IDs
  
  // Timestamps
  createdAt: Date
  lastUpdatedAt: Date
  lastVisitedAt?: Date
}
