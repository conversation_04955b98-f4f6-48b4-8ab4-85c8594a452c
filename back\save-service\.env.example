# Application
PORT=3004
NODE_ENV=development

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5433
DATABASE_USERNAME=nuclearstory
DATABASE_PASSWORD=password
DATABASE_NAME=saves_db

# CORS
CORS_ORIGIN=http://localhost:3000

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001

# Save Configuration
MAX_SAVES_PER_USER=10
AUTO_SAVE_INTERVAL=300000
SAVE_COMPRESSION=true
BACKUP_RETENTION_DAYS=30

# File Storage (for screenshots, etc.)
STORAGE_TYPE=local
STORAGE_PATH=./uploads
MAX_FILE_SIZE=5242880

# AWS S3 (if using cloud storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=nuclearstory-saves

# Logging
LOG_LEVEL=debug
