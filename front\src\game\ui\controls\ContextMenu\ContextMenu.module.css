/* Контекстное меню в стиле игры */

.contextMenu {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
  padding: 8px 0;
  min-width: 220px;
  font-family: inherit;
  font-size: 14px;
  color: var(--text-primary);
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.menuItem {
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-weight: 500;
}

.menuItem:last-child {
  border-bottom: none;
}

.menuItem:hover {
  
  color: var(--primary-color);
  text-shadow: 0 0 5px var(--shadow-primary);
  transform: translateX(2px);
}

.menuItem:active {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(2px) translateY(1px);
}