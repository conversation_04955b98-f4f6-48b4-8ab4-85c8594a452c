import { LocationSubtype } from '../../shared/enums';
import { TransferLocation } from '../../shared/types/Location';
import { WorldMapCell } from '../../shared/types/World';
import { SUBTYPE_WEIGHTS } from './constants/locationSubtypeWeights';
import { locationConfigs } from './constants/locationConfig';

function weightedRandomSubtype(rng: () => number): LocationSubtype {
  const total = SUBTYPE_WEIGHTS.reduce((sum, [, w]) => sum + w, 0);
  let r = rng() * total;
  for (const [subtype, weight] of SUBTYPE_WEIGHTS) {
    if (r < weight) return subtype;
    r -= weight;
  }
  return SUBTYPE_WEIGHTS[SUBTYPE_WEIGHTS.length - 1][0];
}

export async function assignLocationSubtypesWithGuarantees(worldMap: { worldMapCells: WorldMapCell[] }, rng: () => number): Promise<void> {
  const zones: Record<number, { cell: WorldMapCell, location: TransferLocation }[]> = {};
  
  // Группируем локации по зонам
  for (const cell of worldMap.worldMapCells) {
    if (cell.location) {
      const lvlZone = cell.LVLZone;
      if (!zones[lvlZone]) zones[lvlZone] = [];
      zones[lvlZone].push({ cell, location: cell.location });
    }
  }

  for (const [zoneIdStr, locPairs] of Object.entries(zones)) {
    // 1. Присваиваем случайные подтипы всем локациям
    for (const pair of locPairs) {
      const subtype = weightedRandomSubtype(rng);
      pair.location.subtype = subtype;
      pair.location.type = locationConfigs[subtype].type; // Присваиваем тип из конфига
      pair.location.terrain = locationConfigs[subtype].terrain; // Присваиваем terrain из конфига
      pair.location.morality = Math.floor(rng() * 22) - 1; // -1 to 20
    }

    // 2. В каждой зоне заменяем по 3 локации на магазины с нужной моралью
    if (locPairs.length >= 3) {
      // Перемешиваем массив для случайного выбора
      const shuffled = [...locPairs].sort(() => rng() - 0.5);
      
      // Магазин с моралью 1-6
      shuffled[0].location.subtype = LocationSubtype.SHOP;
      shuffled[0].location.type = locationConfigs[LocationSubtype.SHOP].type;
      shuffled[0].location.terrain = locationConfigs[LocationSubtype.SHOP].terrain;
      shuffled[0].location.morality = 1 + Math.floor(rng() * 6); // 1-6
      
      // Магазин с моралью 7-13
      shuffled[1].location.subtype = LocationSubtype.SHOP;
      shuffled[1].location.type = locationConfigs[LocationSubtype.SHOP].type;
      shuffled[1].location.terrain = locationConfigs[LocationSubtype.SHOP].terrain;
      shuffled[1].location.morality = 7 + Math.floor(rng() * 7); // 7-13
      
      // Магазин с моралью 14-20
      shuffled[2].location.subtype = LocationSubtype.SHOP;
      shuffled[2].location.type = locationConfigs[LocationSubtype.SHOP].type;
      shuffled[2].location.terrain = locationConfigs[LocationSubtype.SHOP].terrain;
      shuffled[2].location.morality = 14 + Math.floor(rng() * 7); // 14-20
    }
  }
}

