import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { WorldMapDecorations, TerrainType } from 'src/shared';
import { CancellationToken, delay } from '../../utils/asyncUtils';

// Генерация руин (радиальная) - синхронная версия для обратной совместимости
export function generateRuins(grid: Record<string, WorldMapCell>, center: Position, worldSize: number, rng: () => number) {
  const config = DECORATION_CONFIG.RUINS;
  
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.RUINS;
            grid[cellKey].terrain = TerrainType.WASTELAND;
            grid[cellKey].height = Math.max(grid[cellKey].height, 2); // Руины повышают высоту
          }
        }
      }
    }
  }
  
  // Добавляем обломки вокруг руин
  for (let x = center.x - config.MAX_RADIUS - 1; x <= center.x + config.MAX_RADIUS + 1; x++) {
    for (let y = center.y - config.MAX_RADIUS - 1; y <= center.y + config.MAX_RADIUS + 1; y++) {
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
      
      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;
      
      // Обломки появляются рядом с руинами, но не в самих руинах
      if (distance > config.MAX_RADIUS && distance <= config.MAX_RADIUS + 1 && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          rng() * 100 < 40) {
        grid[cellKey].decoration = WorldMapDecorations.RUBBLE;
        grid[cellKey].terrain = TerrainType.WASTELAND;
      }
    }
  }
}

// Асинхронная генерация руин с поддержкой отмены операций
export async function generateRuinsAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.RUINS;
  let operationCount = 0;
  
  // Генерация основных руин
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Ruins generation was cancelled');
    }
    
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.RUINS;
            grid[cellKey].terrain = TerrainType.WASTELAND;
            grid[cellKey].height = Math.max(grid[cellKey].height, 2); // Руины повышают высоту
          }
        }
        
        // Освобождаем event loop каждые 100 операций
        operationCount++;
        if (operationCount % 100 === 0) {
          await delay(0);
        }
      }
    }
  }
  
  // Добавляем обломки вокруг руин
  for (let x = center.x - config.MAX_RADIUS - 1; x <= center.x + config.MAX_RADIUS + 1; x++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Ruins rubble generation was cancelled');
    }
    
    for (let y = center.y - config.MAX_RADIUS - 1; y <= center.y + config.MAX_RADIUS + 1; y++) {
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
      
      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;
      
      // Обломки появляются рядом с руинами, но не в самих руинах
      if (distance > config.MAX_RADIUS && distance <= config.MAX_RADIUS + 1 && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          rng() * 100 < 40) {
        grid[cellKey].decoration = WorldMapDecorations.RUBBLE;
        grid[cellKey].terrain = TerrainType.WASTELAND;
      }
      
      // Освобождаем event loop каждые 50 операций для обломков
      operationCount++;
      if (operationCount % 50 === 0) {
        await delay(0);
      }
    }
  }
}