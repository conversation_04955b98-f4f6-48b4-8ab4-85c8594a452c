import { TransferLocation, Point } from "../../../../shared/types/Location";
import { LocationDecorations, DecorationZoneType } from "../../../../shared/enums";
import { isThreeWallJunction } from "../buildingCreator";

// Интерфейс для описания здания
interface BuildingInfo {
  id: string;
  startX: number;
  startY: number;
  size: number;
  hasRooms: boolean;
  walls: Point[];
  rooms?: Array<{x: number, y: number, width: number, height: number}>;
}

/**
 * Добавляет двери в здание
 */
export async function addBuildingDoors(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): Promise<void> {
  // Анализируем здание и определяем наличие комнат
  const buildingInfo = analyzeBuildingStructure(location, startX, startY, size);

  // Выбираем соответствующую ветку обработки
  if (buildingInfo.hasRooms) {
    await processBuildingWithRooms(location, buildingInfo, doorCount, rng);
  } else {
    await processBuildingWithoutRooms(location, buildingInfo, doorCount, rng);
  }
}

/**
 * Анализирует структуру здания и определяет наличие внутренних комнат
 */
function analyzeBuildingStructure(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number
): BuildingInfo {
  const buildingId = `building_${startX}_${startY}_${size}`;

  // Получаем все стены здания
  const allWalls = location.decorations?.[LocationDecorations.WALL] || [];
  const buildingWalls = allWalls.filter(([x, y]) =>
    x >= startX && x < startX + size &&
    y >= startY && y < startY + size
  );

  // Определяем наличие комнат через зоны декораций
  const rooms = findRoomsFromDecorationZones(location);
  const buildingRooms = rooms.filter(room =>
    room.x >= startX && room.x < startX + size &&
    room.y >= startY && room.y < startY + size
  );

  const hasRooms = buildingRooms.length > 0;

  return {
    id: buildingId,
    startX,
    startY,
    size,
    hasRooms,
    walls: buildingWalls,
    rooms: hasRooms ? buildingRooms : undefined
  };
}

/**
 * Находит комнаты из зон декораций
 */
function findRoomsFromDecorationZones(location: TransferLocation): Array<{x: number, y: number, width: number, height: number}> {
  const rooms: Array<{x: number, y: number, width: number, height: number}> = [];

  if (!location.decorationZoneType) return rooms;

  // Группируем зоны по ID
  const zoneGroups = new Map<number, Point[]>();

  for (const zoneType in location.decorationZoneType) {
    const zones = location.decorationZoneType[zoneType as DecorationZoneType];
    if (!zones) continue;

    for (const zone of zones) {
      const [x, y, zoneId] = zone;
      if (!zoneGroups.has(zoneId)) {
        zoneGroups.set(zoneId, []);
      }
      zoneGroups.get(zoneId)!.push([x, y]);
    }
  }

  // Для каждой группы зон определяем границы комнаты
  for (const [, points] of zoneGroups) {
    if (points.length === 0) continue;

    let minX = points[0][0], maxX = points[0][0];
    let minY = points[0][1], maxY = points[0][1];

    for (const [x, y] of points) {
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    // Расширяем границы на 1 клетку для учета стен
    rooms.push({
      x: minX - 1,
      y: minY - 1,
      width: maxX - minX + 3,
      height: maxY - minY + 3
    });
  }

  return rooms;
}

/**
 * Обрабатывает здания с внутренними комнатами
 */
async function processBuildingWithRooms(
  location: TransferLocation,
  buildingInfo: BuildingInfo,
  doorCount: number,
  rng: () => number
): Promise<void> {
  // Инициализируем декорации если их нет
  if (!location.decorations) {
    location.decorations = {};
  }
  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  // TODO: Реализовать полную логику для зданий с комнатами
  // Пока используем упрощенную логику - добавляем одну внешнюю дверь
  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];

  // Находим возможные позиции для внешних дверей
  const externalWallPositions = findExternalWallPositions(buildingInfo, walls);

  if (externalWallPositions.length > 0) {
    const doorIndex = Math.floor(rng() * externalWallPositions.length);
    const doorPosition = externalWallPositions[doorIndex];

    doors.push(doorPosition);

    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall => wall[0] === doorPosition[0] && wall[1] === doorPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }
}

/**
 * Обрабатывает здания без внутренних комнат
 */
async function processBuildingWithoutRooms(
  location: TransferLocation,
  buildingInfo: BuildingInfo,
  doorCount: number,
  rng: () => number
): Promise<void> {
  // Инициализируем декорации если их нет
  if (!location.decorations) {
    location.decorations = {};
  }
  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];

  // Находим возможные позиции для внешних дверей (исключая углы)
  const externalWallPositions = findExternalWallPositions(buildingInfo, walls);

  // Размещаем двери (обычно одну дверь для простых зданий)
  const doorsToPlace = Math.min(doorCount, externalWallPositions.length, 1); // Ограничиваем одной дверью для простых зданий

  for (let i = 0; i < doorsToPlace; i++) {
    if (externalWallPositions.length === 0) break;

    // Выбираем случайную позицию из доступных
    const doorIndex = Math.floor(rng() * externalWallPositions.length);
    const doorPosition = externalWallPositions.splice(doorIndex, 1)[0];

    doors.push(doorPosition);

    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall => wall[0] === doorPosition[0] && wall[1] === doorPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }
}

/**
 * Находит позиции внешних стен здания, исключая углы и стыки трех стен
 */
function findExternalWallPositions(buildingInfo: BuildingInfo, walls: Point[]): Point[] {
  const { startX, startY, size } = buildingInfo;
  const externalPositions: Point[] = [];

  // Проверяем каждую стену здания
  for (const wall of walls) {
    const [x, y] = wall;

    // Проверяем, что это внешняя стена здания
    const isExternalWall = (x === startX || x === startX + size - 1 ||
                           y === startY || y === startY + size - 1);

    if (!isExternalWall) continue;

    // Исключаем углы
    const isCorner = (x === startX || x === startX + size - 1) &&
                     (y === startY || y === startY + size - 1);

    if (isCorner) continue;

    // Проверяем, что это не стык трех стен (внутренний угол)
    const isJunction = isThreeWallJunction(x, y, walls);

    if (isJunction) continue;

    externalPositions.push([x, y]);
  }

  return externalPositions;
}



