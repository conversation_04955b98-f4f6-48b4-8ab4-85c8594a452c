import { TransferLocation, Point } from "../../../../shared/types/Location";
import { LocationDecorations, DecorationZoneType } from "../../../../shared/enums";
import { isThreeWallJunction } from "../buildingCreator";

/**
 * Добавляет двери в здание
 */
export async function addBuildingDoors(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];

  // Собираем все возможные позиции для ВНЕШНИХ дверей (только внешние стены здания)
  const possibleExternalDoorPositions: Point[] = [];

  // Проверяем каждую стену здания
  for (const wall of walls) {
    const [x, y] = wall;

    // Проверяем, что это внешняя стена здания
    const isExternalWall = (x === startX || x === startX + size - 1 ||
                           y === startY || y === startY + size - 1);

    if (!isExternalWall) continue;

    // Исключаем углы
    const isCorner = (x === startX || x === startX + size - 1) &&
                     (y === startY || y === startY + size - 1);

    if (isCorner) continue;

    // Проверяем, что это не стык трех стен (внутренний угол)
    const isJunction = isThreeWallJunction(x, y, walls);

    if (isJunction) continue;

    possibleExternalDoorPositions.push([x, y]);
  }

  // Размещаем ВНЕШНИЕ двери (входы/выходы в здание)
  for (let i = 0; i < Math.min(doorCount, possibleExternalDoorPositions.length); i++) {
    const doorIndex = Math.floor(rng() * possibleExternalDoorPositions.length);
    const doorPosition = possibleExternalDoorPositions.splice(doorIndex, 1)[0];

    doors.push(doorPosition);

    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall => wall[0] === doorPosition[0] && wall[1] === doorPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }

  // Добавляем ВНУТРЕННИЕ двери между комнатами
  await addInternalDoors(location, startX, startY, size, rng);
}

/**
 * Добавляет внутренние двери между комнатами
 */
export async function addInternalDoors(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  rng: () => number
): Promise<void> {
  const walls = location.decorations?.[LocationDecorations.WALL] || [];
  const doors = location.decorations?.[LocationDecorations.DOOR] || [];

  // Находим все комнаты по зонам декораций
  const rooms = findRoomsFromDecorationZones(location);

  if (rooms.length <= 1) return; // Нет смысла добавлять двери если комната одна или их нет

  // Определяем главный выход из здания (ближайший к центру карты)
  const buildingCenterX = buildingStartX + buildingSize / 2;
  const buildingCenterY = buildingStartY + buildingSize / 2;

  let mainExit: Point | null = null;
  let minDistanceToCenter = Infinity;

  for (const door of doors) {
    const [x, y] = door;
    // Проверяем, что это внешняя дверь
    const isExternalDoor = (x === buildingStartX || x === buildingStartX + buildingSize - 1 ||
                           y === buildingStartY || y === buildingStartY + buildingSize - 1);

    if (isExternalDoor) {
      const distance = Math.sqrt(Math.pow(x - buildingCenterX, 2) + Math.pow(y - buildingCenterY, 2));
      if (distance < minDistanceToCenter) {
        minDistanceToCenter = distance;
        mainExit = [x, y];
      }
    }
  }

  // Для каждой комнаты добавляем одну дверь, направленную к выходу
  const usedWalls = new Set<string>(); // Отслеживаем использованные стены

  for (const room of rooms) {
    // Находим стены комнаты
    const roomWalls = findRoomWalls(room, walls, buildingStartX, buildingStartY, buildingSize);

    if (roomWalls.length === 0) continue;

    // Определяем направление к выходу
    const roomCenterX = room.x + room.width / 2;
    const roomCenterY = room.y + room.height / 2;

    let targetDirection: 'north' | 'south' | 'east' | 'west' = 'south';

    if (mainExit) {
      const [exitX, exitY] = mainExit;
      const deltaX = exitX - roomCenterX;
      const deltaY = exitY - roomCenterY;

      // Определяем основное направление к выходу
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        targetDirection = deltaX > 0 ? 'east' : 'west';
      } else {
        targetDirection = deltaY > 0 ? 'south' : 'north';
      }
    }

    // Группируем стены по направлениям
    const wallsByDirection = {
      north: roomWalls.filter(([x, y]) => y === room.y),
      south: roomWalls.filter(([x, y]) => y === room.y + room.height - 1),
      west: roomWalls.filter(([x, y]) => x === room.x),
      east: roomWalls.filter(([x, y]) => x === room.x + room.width - 1)
    };

    // Пытаемся разместить дверь в предпочтительном направлении
    const directions = [targetDirection, 'north', 'south', 'east', 'west'];

    for (const direction of directions) {
      // Если на отрезке стены комнаты уже есть дверь, пропускаем направление
      const existingDoorOnSide = doors.some(([dx, dy]) => {
        if (direction === 'north') {
          return dy === room.y && dx >= room.x && dx < room.x + room.width;
        } else if (direction === 'south') {
          return dy === room.y + room.height - 1 && dx >= room.x && dx < room.x + room.width;
        } else if (direction === 'west') {
          return dx === room.x && dy >= room.y && dy < room.y + room.height;
        } else { // east
          return dx === room.x + room.width - 1 && dy >= room.y && dy < room.y + room.height;
        }
      });

      if (existingDoorOnSide) continue;

      const availableWalls = wallsByDirection[direction].filter(wall => {
        const wallKey = `${wall[0]},${wall[1]}`;
        return !usedWalls.has(wallKey);
      });

      if (availableWalls.length > 0) {
        // Выбираем случайную стену из доступных
        const wallIndex = Math.floor(rng() * availableWalls.length);
        const doorPosition = availableWalls[wallIndex];

        doors.push(doorPosition);

        // Удаляем стену в позиции двери
        const wallArrayIndex = walls.findIndex(wall =>
          wall[0] === doorPosition[0] && wall[1] === doorPosition[1]
        );
        if (wallArrayIndex !== -1) {
          walls.splice(wallArrayIndex, 1);
        }

        // Отмечаем стену как использованную
        usedWalls.add(`${doorPosition[0]},${doorPosition[1]}`);
        break;
      }
    }
  }
}


/**
 * Находит комнаты из зон декораций
 */
function findRoomsFromDecorationZones(location: TransferLocation): Array<{x: number, y: number, width: number, height: number}> {
  const rooms: Array<{x: number, y: number, width: number, height: number}> = [];

  if (!location.decorationZoneType) return rooms;

  // Группируем зоны по ID
  const zoneGroups = new Map<number, Point[]>();

  for (const zoneType in location.decorationZoneType) {
    const zones = location.decorationZoneType[zoneType as DecorationZoneType];
    if (!zones) continue;

    for (const zone of zones) {
      const [x, y, zoneId] = zone;
      if (!zoneGroups.has(zoneId)) {
        zoneGroups.set(zoneId, []);
      }
      zoneGroups.get(zoneId)!.push([x, y]);
    }
  }

  // Для каждой группы зон определяем границы комнаты
  for (const [zoneId, points] of zoneGroups) {
    if (points.length === 0) continue;

    let minX = points[0][0], maxX = points[0][0];
    let minY = points[0][1], maxY = points[0][1];

    for (const [x, y] of points) {
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    // Расширяем границы на 1 клетку для учета стен
    rooms.push({
      x: minX - 1,
      y: minY - 1,
      width: maxX - minX + 3,
      height: maxY - minY + 3
    });
  }

  return rooms;
}

/**
 * Находит стены комнаты (только внутренние стены, не внешние стены здания)
 */
function findRoomWalls(
  room: {x: number, y: number, width: number, height: number},
  allWalls: Point[],
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number
): Point[] {
  const roomWalls: Point[] = [];

  for (const wall of allWalls) {
    const [x, y] = wall;

    // Проверяем, что стена принадлежит периметру комнаты
    const isRoomPerimeter = (
      (x === room.x || x === room.x + room.width - 1) && y >= room.y && y < room.y + room.height
    ) || (
      (y === room.y || y === room.y + room.height - 1) && x >= room.x && x < room.x + room.width
    );

    if (!isRoomPerimeter) continue;

    // Исключаем внешние стены здания
    const isBuildingExternalWall = (
      x === buildingStartX || x === buildingStartX + buildingSize - 1 ||
      y === buildingStartY || y === buildingStartY + buildingSize - 1
    );

    if (isBuildingExternalWall) continue;

    // Исключаем углы комнаты
    const isRoomCorner = (
      (x === room.x || x === room.x + room.width - 1) &&
      (y === room.y || y === room.y + room.height - 1)
    );

    if (isRoomCorner) continue;

    roomWalls.push([x, y]);
  }

  return roomWalls;
}