import { TransferLocation, Point } from "../../../../shared/types/Location";
import { HouseRoomSettings, houseRoomSettings } from "../../constants/houseRoomSettings";
import { LocationDecorations, DecorationZoneType } from "../../../../shared/enums";

/**
 * Дробит здание на комнаты 
 */
export async function subdivideBuilding(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  roomCount: number,
  rng: () => number
): Promise<void> {
  // Внутренняя область здания (исключая стены)
  const innerStartX = buildingStartX + 1;
  const innerStartY = buildingStartY + 1;
  const innerSize = buildingSize - 2;

  if (innerSize < 4) return; // Слишком маленькое здание для комнат

  // Создаем начальную область для деления
  const initialArea = {
    x: innerStartX,
    y: innerStartY,
    width: innerSize,
    height: innerSize
  };

  // Рекурсивно делим область на комнаты
  const rooms = subdivideArea(initialArea, roomCount, rng, []);

  // Создаем стены и зоны декораций для каждой комнаты
  // Сначала устанавливаем зоны декораций для каждой комнаты
  for (const room of rooms) {
    const roomType = determineRoomTypeBySize(room.width, room.height);
    setRoomDecorationZone(location, room, roomType);
  }

  // Добавляем внутренние стены один раз по границам между соседними комнатами
  if (!location.decorations) location.decorations = {};
  if (!location.decorations[LocationDecorations.WALL]) location.decorations[LocationDecorations.WALL] = [];
  const walls = location.decorations[LocationDecorations.WALL];
  const wallSet = new Set(walls.map(w => `${w[0]},${w[1]}`));

  // Для каждой пары комнат проверяем, соприкасаются ли они по вертикали или горизонтали
  for (let i = 0; i < rooms.length; i++) {
    const A = rooms[i];
    for (let j = i + 1; j < rooms.length; j++) {
      const B = rooms[j];

      // Вертикальная стыковка: A справа от B или B справа от A
      if (A.x + A.width === B.x || B.x + B.width === A.x) {
        // Найдём вертикальный диапазон пересечения
        const top = Math.max(A.y, B.y);
        const bottom = Math.min(A.y + A.height, B.y + B.height);

        // Если есть пересечение по Y
        if (top < bottom) {
          // выберем координату X для стены: используем правую границу левой комнаты (A.x + A.width - 1)
          const wallX = (A.x + A.width === B.x) ? (A.x + A.width - 1) : (B.x + B.width - 1);
          for (let yy = top; yy < bottom; yy++) {
            const key = `${wallX},${yy}`;
            if (!wallSet.has(key)) { walls.push([wallX, yy]); wallSet.add(key); }
          }
        }
      }

      // Горизонтальная стыковка: A снизу от B или B снизу от A
      if (A.y + A.height === B.y || B.y + B.height === A.y) {
        const left = Math.max(A.x, B.x);
        const right = Math.min(A.x + A.width, B.x + B.width);

        // Если есть пересечение по X
        if (left < right) {
          const wallY = (A.y + A.height === B.y) ? (A.y + A.height - 1) : (B.y + B.height - 1);
          for (let xx = left; xx < right; xx++) {
            const key = `${xx},${wallY}`;
            if (!wallSet.has(key)) { walls.push([xx, wallY]); wallSet.add(key); }
          }
        }
      }
    }
  }
}

/**
 * Рекурсивно делит область на комнаты
 */
export function subdivideArea(
  area: { x: number; y: number; width: number; height: number },
  targetRoomCount: number,
  rng: () => number,
  currentRooms: { x: number; y: number; width: number; height: number }[]
): { x: number; y: number; width: number; height: number }[] {
  // Если достигли нужного количества комнат или область слишком мала для деления
  if (currentRooms.length >= targetRoomCount ||
      (area.width < 6 && area.height < 6) ||
      (area.width < 4 || area.height < 4)) {
    return [...currentRooms, area];
  }

  // Определяем направление деления (горизонтальное или вертикальное)
  const canSplitHorizontally = area.height >= 6;
  const canSplitVertically = area.width >= 6;

  if (!canSplitHorizontally && !canSplitVertically) {
    return [...currentRooms, area];
  }

  let splitHorizontally: boolean;
  if (canSplitHorizontally && canSplitVertically) {
    // Предпочитаем делить по большей стороне
    splitHorizontally = area.height > area.width ? true : rng() > 0.5;
  } else {
    splitHorizontally = canSplitHorizontally;
  }

  if (splitHorizontally) {
    // Горизонтальное деление
    const minSplit = 3;
    const maxSplit = area.height - 3;
    const splitPoint = Math.floor(rng() * (maxSplit - minSplit + 1)) + minSplit;

    const room1 = { x: area.x, y: area.y, width: area.width, height: splitPoint };
    const room2 = { x: area.x, y: area.y + splitPoint, width: area.width, height: area.height - splitPoint };

    // Рекурсивно делим каждую часть
    const rooms1 = subdivideArea(room1, Math.ceil(targetRoomCount / 2), rng, []);
    const rooms2 = subdivideArea(room2, targetRoomCount - rooms1.length, rng, []);

    return [...currentRooms, ...rooms1, ...rooms2];
  } else {
    // Вертикальное деление
    const minSplit = 3;
    const maxSplit = area.width - 3;
    const splitPoint = Math.floor(rng() * (maxSplit - minSplit + 1)) + minSplit;

    const room1 = { x: area.x, y: area.y, width: splitPoint, height: area.height };
    const room2 = { x: area.x + splitPoint, y: area.y, width: area.width - splitPoint, height: area.height };

    // Рекурсивно делим каждую часть
    const rooms1 = subdivideArea(room1, Math.ceil(targetRoomCount / 2), rng, []);
    const rooms2 = subdivideArea(room2, targetRoomCount - rooms1.length, rng, []);

    return [...currentRooms, ...rooms1, ...rooms2];
  }
}

/**
 * Определяет тип комнаты по её размеру
 */
export function determineRoomTypeBySize(width: number, height: number): HouseRoomSettings {
  const area = width * height;
  const availableRoomTypes = houseRoomSettings.filter(room =>
    room.room && !room.building && room.canBeUsed === 'all'
  );

  // Определяем размер комнаты: маленькая (до 12), средняя (12-25), большая (25+)
  let sizeCategory: 1 | 2 | 3;
  if (area <= 12) {
    sizeCategory = 1; // маленькая
  } else if (area <= 25) {
    sizeCategory = 2; // средняя
  } else {
    sizeCategory = 3; // большая
  }

  // Фильтруем комнаты по размеру
  const suitableRooms = availableRoomTypes.filter(room => room.size === sizeCategory);

  // Если нет подходящих комнат, берем любую доступную
  const roomsToChooseFrom = suitableRooms.length > 0 ? suitableRooms : availableRoomTypes;

  // Возвращаем случайную подходящую комнату
  return roomsToChooseFrom[Math.floor(Math.random() * roomsToChooseFrom.length)];
}

/**
 * Устанавливает зону декораций для комнаты
 */
export function setRoomDecorationZone(
  location: TransferLocation,
  room: { x: number; y: number; width: number; height: number },
  roomType: HouseRoomSettings
): void {
  if (!location.decorationZoneType) {
    location.decorationZoneType = {};
  }

  if (!location.decorationZoneType[roomType.decorationZoneType]) {
    location.decorationZoneType[roomType.decorationZoneType] = [];
  }

  const zoneId = Math.floor(Math.random() * 1000000);

  // Добавляем все внутренние клетки комнаты в зону декораций (исключая стены)
  for (let x = room.x + 1; x < room.x + room.width - 1; x++) {
    for (let y = room.y + 1; y < room.y + room.height - 1; y++) {
      location.decorationZoneType[roomType.decorationZoneType].push([x, y, zoneId]);
    }
  }
}

/**
 * Создает одну комнату на все здание (для маленьких зданий)
 */
export async function createSingleRoomInBuilding(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  buildingConfig: HouseRoomSettings
): Promise<void> {
  // Внутренняя область здания (исключая стены)
  const innerStartX = buildingStartX + 1;
  const innerStartY = buildingStartY + 1;
  const innerSize = buildingSize - 2;

  if (innerSize <= 0) return; // Слишком маленькое здание

  // Устанавливаем зону декораций для всей внутренней области
  if (!location.decorationZoneType) {
    location.decorationZoneType = {};
  }

  if (!location.decorationZoneType[buildingConfig.decorationZoneType]) {
    location.decorationZoneType[buildingConfig.decorationZoneType] = [];
  }

  const zoneId = Math.floor(Math.random() * 1000000);

  // Добавляем все внутренние клетки здания в зону декораций
  for (let x = innerStartX; x < innerStartX + innerSize; x++) {
    for (let y = innerStartY; y < innerStartY + innerSize; y++) {
      location.decorationZoneType[buildingConfig.decorationZoneType].push([x, y, zoneId]);
    }
  }
}
