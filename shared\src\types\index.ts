import { GameStatus, EventType } from '../enums'

// Экспортируем общие типы
export * from './Common'

export { Position as Coordinates } from './Common'

// === ИГРОВЫЕ СТАТИСТИКИ === //
export interface GameStats {
  health: number
  maxHealth: number
  energy: number
  maxEnergy: number
  hunger: number
  thirst: number
  radiation: number
  sanity?: number
}

// === ИГРОВЫЕ СОБЫТИЯ === //
export interface Choice {
  id: string
  text: string
  consequences?: {
    stats?: Partial<GameStats>
    items?: string[]
    unlockQuests?: string[]
    unlockLocations?: string[]
  }
}

export interface StoryEvent {
  id: string
  type: EventType
  title: string
  description: string
  choices?: Choice[]
  requirements?: {
    level?: number;
    items?: string[];
    completedQuests?: string[];
    stats?: Partial<GameStats>;
  };
}

export interface GameSession {
  id: string;
  userId: string;
  status: GameStatus;
  currentLocation: string;
  gameStats: GameStats;
  startedAt: Date;
  lastPlayedAt: Date;
  playtimeMinutes: number;
}

// Export new type modules
export * from './NPC'
export * from './Location'
export * from './Unit'
export * from './Weapon'
export * from './Armor'
export * from './Faction'
export * from './Event'
export * from './World'
export * from './OpenClose'
export * from './Container'
export * from './Auth'
export * from './Api'
