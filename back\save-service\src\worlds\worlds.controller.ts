import { Controller, Post, Get, Delete, Put, Body, Param, Query } from '@nestjs/common';
import { WorldsService } from './worlds.service';
import { SaveWorldDto } from './dto/worlds.dto';

@Controller('worlds')
export class WorldsController {
  @Get()
  async getUserWorlds(
    @Query('userId') userId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '100'
  ) {
    const pageNum = Number(page) || 1;
    const limitNum = Number(limit) || 100;
    const result = await this.worldsService.getUserWorlds(userId, pageNum, limitNum);
    return {
      success: true,
      ...result
    };
  }
  constructor(private readonly worldsService: WorldsService) {}

  @Post()
  async saveWorld(@Body() saveWorldDto: SaveWorldDto) {
    const savedWorld = await this.worldsService.saveWorld(
      saveWorldDto.userId,
      saveWorldDto as any,
      saveWorldDto.generationTime,
      saveWorldDto.generatedWithAI
    );
    return {
      success: true,
      worldId: savedWorld.id,
      message: 'World saved successfully',
    };
  }

  @Get(':worldId')
  async getWorld(
    @Param('worldId') worldId: string,
    @Query('userId') userId?: string
  ): Promise<any> {
    const world = await this.worldsService.getWorldById(worldId, userId);
    return {
      success: true,
      world,
    };
  }

  @Put(':worldId')
  async updateWorld(
    @Param('worldId') worldId: string,
    @Body() saveWorldDto: SaveWorldDto
  ) {
    const updatedWorld = await this.worldsService.updateWorld(
      worldId,
      saveWorldDto.userId,
      saveWorldDto as any
    );
    return {
      success: true,
      worldId: updatedWorld.id,
      message: 'World updated successfully',
    };
  }

  @Delete(':worldId')
  async deleteWorld(
    @Param('worldId') worldId: string,
    @Query('userId') userId: string
  ) {
    await this.worldsService.deleteWorld(worldId, userId);
    return {
      success: true,
      message: 'World deleted successfully',
    };
  }
}
