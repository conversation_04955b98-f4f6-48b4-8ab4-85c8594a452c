# NuclearStory - TypeScript Fix Script (PowerShell)
Write-Host "🔧 Fixing TypeScript issues..." -ForegroundColor Cyan

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running." -ForegroundColor Red
    exit 1
}

Write-Host "📦 Installing TypeScript dependencies..." -ForegroundColor Yellow
docker compose -f docker-compose.dev.yml exec frontend npm install

Write-Host "🔍 Running TypeScript check..." -ForegroundColor Yellow
$tsResult = docker compose -f docker-compose.dev.yml exec frontend npx tsc --noEmit

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ TypeScript check passed!" -ForegroundColor Green
} else {
    Write-Host "⚠️  TypeScript issues found, but project should still work." -ForegroundColor Yellow
}

Write-Host "🔄 Restarting frontend to apply changes..." -ForegroundColor Yellow
docker compose -f docker-compose.dev.yml restart frontend

Write-Host ""
Write-Host "✅ TypeScript fix completed!" -ForegroundColor Green
Write-Host "🌐 Frontend available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 If you still see TypeScript errors in your IDE:" -ForegroundColor Yellow
Write-Host "   1. Restart your IDE/VS Code" -ForegroundColor White
Write-Host "   2. Reload TypeScript service (Ctrl+Shift+P -> TypeScript: Restart TS Server)" -ForegroundColor White
Write-Host "   3. Clear IDE cache if needed" -ForegroundColor White
