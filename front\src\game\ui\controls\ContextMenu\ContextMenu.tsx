/**
 * Контекстное меню для тайлов карты
 */

import * as React from 'react'
import { useState, useEffect } from 'react'
import styles from './ContextMenu.module.css'

interface ContextMenuProps {
  x: number
  y: number
  visible: boolean
  onClose: () => void
  onInspect: () => void
  onMovementCost: () => void
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  x,
  y,
  visible,
  onClose,
  onInspect,
  onMovementCost
}) => {
  useEffect(() => {
    if (visible) {
      const handleClickOutside = () => onClose()
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') onClose()
      }

      document.addEventListener('click', handleClickOutside)
      document.addEventListener('keydown', handleEscape)

      return () => {
        document.removeEventListener('click', handleClickOutside)
        document.removeEventListener('keydown', handleEscape)
      }
    }
  }, [visible, onClose])

  if (!visible) return null

  return (
    <div 
      className={styles.contextMenu}
      style={{ 
        left: x, 
        top: y,
        position: 'fixed',
        zIndex: 1000
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className={styles.menuItem} onClick={onInspect}>
        Осмотреть
      </div>
      <div className={styles.menuItem} onClick={onMovementCost}>
        Стоимость перемещения
      </div>
    </div>
  )
}