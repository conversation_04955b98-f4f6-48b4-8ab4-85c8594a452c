import { Injectable, Logger } from '@nestjs/common';
import { ProgressGateway } from './progress.gateway';
import { GenerationProgress, CancellationToken } from '../utils/asyncUtils';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ProgressService {
  private readonly logger = new Logger(ProgressService.name);
  private cancellationTokens = new Map<string, CancellationToken>();

  constructor(private readonly progressGateway: ProgressGateway) {}

  /**
   * Создает новую сессию для отслеживания прогресса
   */
  createProgressSession(userId?: string, worldName?: string): string {
    const sessionId = uuidv4();
    
    this.logger.log(`Created progress session ${sessionId} for user ${userId}`);
    
    return sessionId;
  }

  /**
   * Создает токен отмены для сессии
   */
  createCancellationToken(sessionId: string): CancellationToken {
    const token: CancellationToken = {
      isCancelled: false,
      cancel: () => {
        token.isCancelled = true;
        this.logger.log(`Cancellation token activated for session ${sessionId}`);
      }
    };

    this.cancellationTokens.set(sessionId, token);
    return token;
  }

  /**
   * Отменяет генерацию для сессии
   */
  cancelGeneration(sessionId: string): boolean {
    const token = this.cancellationTokens.get(sessionId);
    if (token) {
      token.cancel();
      this.cancellationTokens.delete(sessionId);
      this.logger.log(`Generation cancelled for session ${sessionId}`);
      return true;
    }
    return false;
  }

  /**
   * Создает callback функцию для отправки прогресса через WebSocket
   */
  createProgressCallback(sessionId: string): (progress: GenerationProgress) => void {
    return (progress: GenerationProgress) => {
      // Проверяем, активна ли сессия
      if (this.progressGateway.isSessionActive(sessionId)) {
        this.progressGateway.sendProgressUpdate(sessionId, progress);
      } else {
        this.logger.warn(`Attempted to send progress to inactive session ${sessionId}`);
      }
    };
  }

  /**
   * Уведомляет о завершении генерации
   */
  notifyGenerationComplete(sessionId: string, result: { success: boolean; worldId?: string; error?: string }) {
    this.progressGateway.sendGenerationComplete(sessionId, result);
    
    // Очищаем токен отмены
    this.cancellationTokens.delete(sessionId);
    
    this.logger.log(`Generation completed notification sent for session ${sessionId}`);
  }

  /**
   * Уведомляет об ошибке генерации
   */
  notifyGenerationError(sessionId: string, error: string) {
    this.progressGateway.sendGenerationError(sessionId, error);
    
    // Очищаем токен отмены
    this.cancellationTokens.delete(sessionId);
    
    this.logger.error(`Generation error notification sent for session ${sessionId}: ${error}`);
  }

  /**
   * Получает статистику активных сессий
   */
  getSessionStats() {
    return {
      activeSessions: this.progressGateway.getActiveSessions().length,
      activeTokens: this.cancellationTokens.size,
      sessions: this.progressGateway.getActiveSessions(),
    };
  }

  /**
   * Очищает неактивные токены отмены (вызывается периодически)
   */
  cleanupInactiveTokens() {
    const activeSessions = new Set(
      this.progressGateway.getActiveSessions().map(s => s.sessionId)
    );

    for (const [sessionId] of this.cancellationTokens) {
      if (!activeSessions.has(sessionId)) {
        this.cancellationTokens.delete(sessionId);
        this.logger.debug(`Cleaned up inactive token for session ${sessionId}`);
      }
    }
  }
}