import * as fs from 'fs';
import * as path from 'path';
import { PatternCollection, DecorationPattern, PatternMatrix } from '../../../shared/types/PatternTypes';

/**
 * Утилита для загрузки шаблонов декораций из JSON файлов
 */
export class PatternLoader {
  private static instance: PatternLoader;
  private patternsCache: Map<string, PatternCollection> = new Map();
  private readonly patternsPath: string;

  private constructor() {
    this.patternsPath = path.join(__dirname, '..', '..', '..', 'shablons');
  }

  public static getInstance(): PatternLoader {
    if (!PatternLoader.instance) {
      PatternLoader.instance = new PatternLoader();
    }
    return PatternLoader.instance;
  }

  /**
   * Загружает коллекцию шаблонов из JSON файла
   */
  public async loadPatternCollection(filename: string): Promise<PatternCollection> {
    const cacheKey = filename;
    
    // Проверяем кеш
    if (this.patternsCache.has(cacheKey)) {
      return this.patternsCache.get(cacheKey)!;
    }

    try {
      const filePath = path.join(this.patternsPath, filename);
      
      // Проверяем существование файла
      if (!fs.existsSync(filePath)) {
        throw new Error(`Pattern file not found: ${filePath}`);
      }

      // Читаем и парсим JSON
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      const patterns = JSON.parse(fileContent);

      // Создаем коллекцию
      const collection: PatternCollection = {
        type: path.basename(filename, '.json'),
        patterns: patterns
      };

      // Кешируем результат
      this.patternsCache.set(cacheKey, collection);
      
      
      return collection;

    } catch (error) {
      throw new Error(`Failed to load pattern collection: ${error.message}`);
    }
  }

  /**
   * Загружает все доступные коллекции ша��лонов
   */
  public async loadAllPatternCollections(): Promise<Map<string, PatternCollection>> {
    const collections = new Map<string, PatternCollection>();
    
    try {
      // Получаем список всех JSON файлов в папке шаблонов
      const files = fs.readdirSync(this.patternsPath)
        .filter(file => file.endsWith('.json'));


      // Загружаем каждую коллекцию
      for (const file of files) {
        try {
          const collection = await this.loadPatternCollection(file);
          const collectionName = path.basename(file, '.json');
          collections.set(collectionName, collection);
        } catch (error) {
        }
      }

      return collections;

    } catch (error) {
      throw new Error(`Failed to load pattern collections: ${error.message}`);
    }
  }

  /**
   * Преобразует матрицу из JSON в объ��кт DecorationPattern
   */
  public createPatternFromMatrix(
    id: string, 
    name: string, 
    matrix: PatternMatrix,
    description?: string
  ): DecorationPattern {
    const height = matrix.length;
    const width = height > 0 ? matrix[0].length : 0;
    
    // Вычисляем центр шаблона
    const centerX = Math.floor(width / 2);
    const centerY = Math.floor(height / 2);

    return {
      id,
      name,
      matrix,
      width,
      height,
      centerX,
      centerY,
      description
    };
  }

  /**
   * Получает случайный шаблон из коллекции
   */
  public getRandomPattern(collection: PatternCollection, rng: () => number): DecorationPattern | null {
    const patternKeys = Object.keys(collection.patterns);
    
    if (patternKeys.length === 0) {
      return null;
    }

    const randomKey = patternKeys[Math.floor(rng() * patternKeys.length)];
    const matrix = collection.patterns[randomKey];
    
    return this.createPatternFromMatrix(
      randomKey,
      randomKey.replace(/-/g, ' '),
      matrix,
      `Шаблон ${collection.type}`
    );
  }

  /**
   * Получает шаблон по ID из коллекции
   */
  public getPatternById(collection: PatternCollection, patternId: string): DecorationPattern | null {
    const matrix = collection.patterns[patternId];
    
    if (!matrix) {
      return null;
    }

    return this.createPatternFromMatrix(
      patternId,
      patternId.replace(/-/g, ' '),
      matrix,
      `Шаблон ${collection.type}`
    );
  }

  /**
   * Очищает кеш шаблонов
   */
  public clearCache(): void {
    this.patternsCache.clear();
  }

  /**
   * Получает статистику загруженных шаблонов
   */
  public getStats(): { collections: number; totalPatterns: number; cacheSize: number } {
    let totalPatterns = 0;
    
    for (const collection of this.patternsCache.values()) {
      totalPatterns += Object.keys(collection.patterns).length;
    }

    return {
      collections: this.patternsCache.size,
      totalPatterns,
      cacheSize: this.patternsCache.size
    };
  }
}