import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { WorldMapDecorations, TerrainType } from 'src/shared';
import { CancellationToken, delay } from '../../utils/asyncUtils';

// Генерация обломков (радиальная) - синхронная версия для обратной совместимости
export function generateRubble(grid: Record<string, WorldMapCell>, center: Position, worldSize: number, rng: () => number) {
  const config = DECORATION_CONFIG.RUBBLE;
  
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.RUBBLE;
            grid[cellKey].terrain = TerrainType.WASTELAND;
          }
        }
      }
    }
  }
}

// Асинхронная генерация обломков с поддержкой отмены операций
export async function generateRubbleAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.RUBBLE;
  let operationCount = 0;
  
  // Генерация обломков
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Rubble generation was cancelled');
    }
    
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.RUBBLE;
            grid[cellKey].terrain = TerrainType.WASTELAND;
          }
        }
        
        // Освобождаем event loop каждые 100 операций
        operationCount++;
        if (operationCount % 100 === 0) {
          await delay(0);
        }
      }
    }
  }
}