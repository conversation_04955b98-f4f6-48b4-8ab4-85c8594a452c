class TextureLoader {
  private textureCache: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();

  async loadTexture(path: string): Promise<HTMLImageElement> {
    if (this.textureCache.has(path)) {
      return this.textureCache.get(path)!;
    }

    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path)!;
    }

    const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.textureCache.set(path, img);
        resolve(img);
      };
      img.onerror = () => reject(new Error(`Failed to load texture: ${path}`));
      img.src = path;
    });

    this.loadingPromises.set(path, loadPromise);
    return loadPromise;
  }

  async preloadTextures(textures: Record<string, string[]>): Promise<void> {
    const allPromises: Promise<HTMLImageElement>[] = [];
    for (const textureList of Object.values(textures)) {
      for (const texturePath of textureList) {
        allPromises.push(this.loadTexture(texturePath));
      }
    }
    await Promise.all(allPromises);
  }

  getTexture(path: string): HTMLImageElement | undefined {
    return this.textureCache.get(path);
  }
}

export const textureLoader = new TextureLoader();