# Database Configuration
POSTGRES_AUTH_DB=auth_db
POSTGRES_SAVES_DB=saves_db
POSTGRES_USER=nuclearstory
POSTGRES_PASSWORD=password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# AI Configuration (using g4f - no API key needed)
# OpenAI API key is optional, g4f provides free alternatives
OPENAI_API_KEY=optional-openai-api-key

# Application Ports
AUTH_SERVICE_PORT=3001
GAME_ENGINE_SERVICE_PORT=3002
STORY_SERVICE_PORT=3003
SAVE_SERVICE_PORT=3004
AI_SERVICE_PORT=3005
FRONTEND_PORT=3000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Environment
NODE_ENV=development
