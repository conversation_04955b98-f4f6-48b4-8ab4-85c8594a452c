# ⚠️ КРИТИЧЕСКИ ВАЖНОЕ ПРЕДУПРЕЖДЕНИЕ ⚠️

## О МОДУЛЕ SHARED И КОПИРОВАНИИ ТИПОВ

### 🚫 НИКОГДА НЕ ИМПОРТИРУЙТЕ ТИПЫ ИЗ КОРНЕВОГО МОДУЛЯ SHARED! 🚫

Модуль shared в корне проекта **НЕ ДОСТУПЕН** в рабочем окружении Docker.
При сборке проекта модуль shared **КОПИРУЕТСЯ** в каждый сервис.

### Правильное расположение shared модулей:

```
back/auth-service/src/shared/
back/game-engine-service/src/shared/
back/save-service/src/shared/
back/world-generator-service/src/shared/
front/src/shared/
```

### Правильный способ импорта:

```typescript
// ✅ ПРАВИЛЬНО
import { Type } from '../shared/types';
import { Enum } from '../shared/enums';

// ❌ НЕПРАВИЛЬНО - ПРИВЕДЕТ К ОШИБКАМ
import { Type } from '@shared/types'; 
import { Enum } from '../../../shared/enums';
```

### Почему это важно?

В Docker контейнерах доступна ТОЛЬКО локальная копия shared модуля в директории `src/shared` каждого сервиса. 
Корневой модуль shared не монтируется в контейнеры.

### Что делать при обновлении shared?

При изменении модуля shared необходимо пересобрать все контейнеры, 
чтобы обновить локальные копии в каждом сервисе.

## ☠️ ИГНОРИРОВАНИЕ ЭТОГО ПРЕДУПРЕЖДЕНИЯ ПРИВЕДЕТ К ОШИБКАМ В PRODUCTION! ☠️
