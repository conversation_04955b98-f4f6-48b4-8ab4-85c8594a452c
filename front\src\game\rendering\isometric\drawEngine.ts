/**
 * Основной движок отрисовки изометрической карты
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { isoToScreen } from '../../utils/coordinates/isometric';
import { drawTile, drawTileDecorations } from './renderUtils';
import { CameraRef } from '../../systems/interaction/eventHandlers';
import { FOG_TILE_OPACITY } from '../../utils/constants/rendering';
import { getCurrentPath, highlightPathCell } from '../../systems/movement/playerMovement';
import { drawPlayerSkeleton } from '../animations/animationSystem';
import { useGameStore } from '@/game/store/gameStore'
import { GameTime, getLightColor, getLightLevel } from '../../utils/time/gameTime';

// Расширяем Window для хранения коррекции угла скелета
declare global {
  interface Window {
    __playerSkewYCorrection?: number;
  }
}

/**
 * Основная функция отрисовки карты
 */
export const createDrawFunction = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  zoom: number = 1.0,
  gameTime?: GameTime
) => {
  return () => {
    // Проверяем, находится ли игрок в локации - если да, НЕ рендерим world map
    const { playerLocationPresent } = useGameStore.getState();
    if (playerLocationPresent) {
      return; // Выходим, не рендерим world map
    }
    
    const canvas = canvasRef.current;
    if (!canvas || !cameraRef.current) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Сохраняем состояние контекста
    ctx.save();

    ctx.imageSmoothingEnabled = false; // отключаем сглаживание

    // Очищаем канвас
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // ПРОСТОЙ ЗУМ ЧЕРЕЗ CANVAS SCALE! 🎯
    // Устанавливаем точку масштабирования в центр канваса
    ctx.translate(canvasWidth / 2, canvasHeight / 2);
    ctx.scale(zoom, zoom);
    ctx.translate(-canvasWidth / 2, -canvasHeight / 2);

    const mapSize = currentWorld?.settings?.worldSize || 20;

    // Центр экрана
    const centerX = cameraRef.current.x;
    const centerY = cameraRef.current.y;

    // Туман войны теперь реализован через простую прозрачность - как в Fallout!

    // Собираем видимые тайлы для оптимизации
    const visibleTileKeys = new Set<string>();

    // ЭТАП 1: ОТРИСОВЫВАЕМ ВСЕ ТАЙЛЫ БЕЗ ДЕКОРАЦИЙ (ФОН + МЕСТНОСТЬ)
    // Простой двойной цикл - никаких сложностей с сортировкой
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);

        const worldX = screenX + canvasWidth / 2 - centerX;
        const worldY = screenY + canvasHeight / 2 - centerY;

        // Проверяем, находится ли тайл в видимой области (грубая проверка)
        if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
          const tileKey = `${isoX},${isoY}`;
          visibleTileKeys.add(tileKey);

          // Проверяем, есть ли туман войны на этом тайле
          const tileData = currentWorld?.worldMap?.[tileKey];
          const hasFogOfWar = tileData?.fogOfWar;

          // Устанавливаем opacity для клеток с туманом войны
          if (hasFogOfWar) {
            ctx.globalAlpha = FOG_TILE_OPACITY; // Почти прозрачные, но кликабельные
          } else {
            ctx.globalAlpha = 1.0; // Обычные клетки полностью видимы
          }

          // Отрисовываем тайл БЕЗ персонажа И БЕЗ декораций - только фон и местность
          drawTile(ctx, screenX, screenY, isoX, isoY, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, currentWorld, cellTarget, false, false);

          // Сбрасываем opacity
          ctx.globalAlpha = 1.0;
        }
      }
    }

    // Получаем позицию игрока и определяем анимацию
    const playerPos = currentWorld?.player?.position;
    let animationName = 'scratching-right';
    
    if (playerPos && window.__playerIsMoving && window.__playerMoveDirection && currentWorld?.player?.position) {
      // Используем РЕАЛЬНОЕ направление движения, а не cellTarget
      const { dx, dy } = window.__playerMoveDirection;
      
      // ПРЕОБРАЗУЕМ ИЗОМЕТРИЧЕСКИЕ КООРДИНАТЫ В ЭКРАННЫЕ ДЯ ПРАВИЛЬНОГО НАПРАВЛЕНИЯ!
      const playerX = currentWorld.player.position.x;
      const playerY = currentWorld.player.position.y;

      const targetX = playerX + dx;
      const targetY = playerY + dy;
      

      const playerScreen = isoToScreen(playerX, playerY, tileWidth, tileHeight);
      const targetScreen = isoToScreen(targetX, targetY, tileWidth, tileHeight);
      
      const screenDeltaX = targetScreen.x - playerScreen.x;
      const screenDeltaY = targetScreen.y - playerScreen.y;
      
      // Вычисляем угол движения в экранных координатах на основе РЕАЛЬНОГО движения

      const angle = Math.atan2(screenDeltaY, screenDeltaX);
      
      // Нормализуем угол в диапазон [0, 2π]
      let normalizedAngle = angle < 0 ? angle + 2 * Math.PI : angle;
      
      // Преобразуем угол: Math.atan2 дает 0° справа, а нам нужно 0° сверху
      // Поворачиваем на -90° и нормализуем
      let adjustedAngle = normalizedAngle - Math.PI / 2;
      if (adjustedAngle < 0) adjustedAngle += 2 * Math.PI;
      
      // Переводим в градусы для удобства понимания
      const degrees = (adjustedAngle * 180) / Math.PI;
      
      // Вычисляем коррекцию угла скелета на основе направления движения
      let skewYCorrection = 0;
      
      // Получаем горизонтальную составляющую движения
      const horizontalComponent = Math.cos(adjustedAngle);
      
      // Базовая интенсивность эффекта для изометрии
      const maxSkewIntensity = 0.14;
      
      if ((degrees >= 335 && degrees <= 360) || (degrees >= 0 && degrees < 25)) {
        animationName = 'walk_south';
        window.__playerDirection = 'south';
        skewYCorrection = 0; // Прямо на юг - без коррекции
      } else if (degrees >= 25 && degrees < 90) {
        animationName = 'walk_south_west';
        window.__playerDirection = 'south';
        // Чем дальше от юга (25°-90°), тем ближе к максимальной коррекции 0.18
        const distanceFromSouth = Math.min(degrees - 25, 65) / 65; // нормализуем 0-1
        skewYCorrection = distanceFromSouth * maxSkewIntensity;
      } else if (degrees >= 90 && degrees < 155) {
        animationName = 'walk_north_west';
        window.__playerDirection = 'west';
        skewYCorrection = maxSkewIntensity; // Максимальная коррекция для запада
      } else if (degrees >= 155 && degrees < 205) {
        animationName = 'walk_north';
        window.__playerDirection = 'north';
        skewYCorrection = 0; // Прямо на север - без коррекции
      } else if (degrees >= 205 && degrees < 270) {
        animationName = 'walk_north_east';
        window.__playerDirection = 'north';
        // Чем дальше от севера (205°-270°), тем ближе к максимальной коррекции -0.18
        const distanceFromNorth = Math.min(degrees - 205, 65) / 65; // нормализуем 0-1
        skewYCorrection = -distanceFromNorth * maxSkewIntensity;
      } else if (degrees >= 270 && degrees < 335) {
        animationName = 'walk_south_east';
        window.__playerDirection = 'east';
        skewYCorrection = -maxSkewIntensity; // Максимальная коррекция для востока
      }
      
      // Сохраняем коррекцию угла в глобальной переменной для использования в анимации
      window.__playerSkewYCorrection = skewYCorrection;
    } else {
      window.__playerDirection = 'idle';
    }

    // ЭТАП 2-3: ОТРИСОВЫВАЕМ ДЕКОРАЦИИ И ПЕРСОНАЖЕЙ В ПРАВИЛЬНОМ ПОРЯДКЕ ДЛЯ ПСЕВДО-3D
    // Рендерим по строкам сверху вниз для правильного Z-порядка в изометрии
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        const tileKey = `${isoX},${isoY}`;
        
        // Рендерим только видимые тайлы с декорациями
        if (visibleTileKeys.has(tileKey)) {
          const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
          
          // Проверяем, есть ли ту��ан войны на этом тайле
          const tileData = currentWorld?.worldMap?.[tileKey];
          const hasFogOfWar = tileData?.fogOfWar;

          // Устанавливаем opacity для клеток с туманом войны
          if (hasFogOfWar) {
            ctx.globalAlpha = FOG_TILE_OPACITY; // Почти прозрачные, но кликабельные
          } else {
            ctx.globalAlpha = 1.0; // Обычные клетки полностью видимы
          }

          // Отрисовываем ТОЛЬКО декорации (без фона, без персонажа)
          drawTileDecorations(ctx, screenX, screenY, isoX, isoY, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, currentWorld, cellTarget);

          // Сбрасываем opacity
          ctx.globalAlpha = 1.0;

          // КЛЮЧЕВОЕ ИЗМЕНЕНИЕ: Если игрок находится на этом тайле, рисуем его ПОСЛЕ декораций этого тайла
          if (playerPos && playerPos.x === isoX && playerPos.y === isoY) {
            if (typeof drawPlayerSkeleton === 'function') {
              drawPlayerSkeleton(ctx, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, currentWorld, animationName);
            }
          }
        }
      }
    }

    // ЭТАП 4: ОТРИСОВЫВАЕМ UI ЭЛЕМЕНТЫ (пути, маркеры и т.д.)
    
    // Рисуем центральную точку для ориентации (скрыта по умолчанию)
    ctx.fillStyle = '#ff353500';
    ctx.beginPath();
    ctx.arc(canvasWidth / 2, canvasHeight / 2, 3, 0, 2 * Math.PI);
    ctx.fill();

    // Рисуем крестики для пути персонажа
    if (currentWorld?.settings?.showPath) {
      const currentPath = getCurrentPath();
      for (const pathPos of currentPath) {
        const { x: screenX, y: screenY } = isoToScreen(pathPos.x, pathPos.y, tileWidth, tileHeight);
        const worldX = screenX + canvasWidth / 2 - centerX;
        const worldY = screenY + canvasHeight / 2 - centerY;

        // Рисуем крестик только если тайл видим
        if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
          highlightPathCell(ctx, worldX, worldY, tileWidth, tileHeight);
        }
      }
    }

    // Восстанавливаем состояние контекста
    ctx.restore();

    // ЭТАП 5: ПРИМЕНЯЕМ ЦВЕТОВОЙ ФИЛЬТР ВРЕМЕНИ ДНЯ (В САМОМ КОНЦЕ!)
    if (gameTime) {
      const lightColor = getLightColor(gameTime);
      
      // Применяем цветовой фильтр только если есть альфа (прозрачность)
      if (lightColor.a > 0) {
        // Сохраняем состояние для фильтра
        ctx.save();
        
        // Пробуем разные режимы в зависимости от времени дня
        const lightLevel = getLightLevel(gameTime);
        
        if (lightLevel > 0.6) {
          // Дневные цвета - используем soft-light для тонкого эффекта
          ctx.globalCompositeOperation = 'soft-light';
        } else {
          // Ночные цвета - используем multiply для более сильного эффекта
          ctx.globalCompositeOperation = 'multiply';
        }
        
        ctx.fillStyle = `rgba(${lightColor.r}, ${lightColor.g}, ${lightColor.b}, ${lightColor.a})`;
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        
        // Восстанавливаем состояние
        ctx.restore();
      }
    }
  };
};