/**
 * Система игрового времени
 * 24 игровых часа = 24 реальных минуты (1 игровой час = 1 реальная минута)
 */

import { TIME_CONSTANTS, LIGHT_CONSTANTS, TIME_PERIODS, LIGHT_COLORS } from '../constants/timeLight';

export interface GameTime {
  day: number;
  hour: number; // 0-23
  minute: number; // 0-59
  second?: number; // 0-59 для более плавных переходов (опционально для совместимости)
  season: 'spring' | 'summer' | 'autumn' | 'winter';
}

// Экспортируем константы для совместимости
export { TIME_CONSTANTS };

/**
 * Определяет, является ли текущее время дневным (достаточно света для неограниченной видимости)
 */
export function isDaytime(gameTime: GameTime): boolean {
  const lightLevel = getLightLevel(gameTime);
  return lightLevel >= LIGHT_CONSTANTS.DAYTIME_THRESHOLD;
}

/**
 * Определяет, является ли текущее время сумеречным (рассвет/закат)
 */
export function isTwilight(gameTime: GameTime): boolean {
  const { hour } = gameTime;
  // Сумерки: 5-7 утра и 17-19 вечера
  return (hour >= 5 && hour <= 7) || (hour >= 17 && hour <= 19);
}

/**
 * Получает уровень освещенности от 0 (полная тьма) до 1 (полный день)
 * Плавные переходы как в реальной жизни с точностью до секунд
 */
export function getLightLevel(gameTime: GameTime): number {
  const { hour, minute, second = 0 } = gameTime;
  const currentTimeInSeconds = hour * 3600 + minute * 60 + second;
  
  // Глубокая ночь (22:00-5:00) - полная темнота
  if ((hour >= TIME_PERIODS.DEEP_NIGHT.start) || (hour < TIME_PERIODS.DEEP_NIGHT.end)) {
    return LIGHT_CONSTANTS.DEEP_NIGHT_LEVEL;
  }
  
  // Предрассветные сумерки (5:00-6:00) - очень медленное осветление
  if (hour >= TIME_PERIODS.PREDAWN.start && hour < TIME_PERIODS.PREDAWN.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.PREDAWN.start * 3600) / (LIGHT_CONSTANTS.PREDAWN_DURATION * 3600);
    return LIGHT_CONSTANTS.PREDAWN_START + ((LIGHT_CONSTANTS.PREDAWN_END - LIGHT_CONSTANTS.PREDAWN_START) * progress);
  }
  
  // Рассвет (6:00-8:00) - плавный подъем света
  if (hour >= TIME_PERIODS.DAWN.start && hour < TIME_PERIODS.DAWN.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.DAWN.start * 3600) / (LIGHT_CONSTANTS.DAWN_DURATION * 3600);
    // Используем синусоидальную кривую для более естественного перехода
    const smoothProgress = Math.sin(progress * Math.PI / 2);
    return LIGHT_CONSTANTS.DAWN_START + ((LIGHT_CONSTANTS.DAWN_END - LIGHT_CONSTANTS.DAWN_START) * smoothProgress);
  }
  
  // Полный день (8:00-17:00) - максимальное освещение
  if (hour >= TIME_PERIODS.FULL_DAY.start && hour < TIME_PERIODS.FULL_DAY.end) {
    return LIGHT_CONSTANTS.FULL_DAY_LEVEL;
  }
  
  // Начало заката (17:00-19:00) - медленное угасание
  if (hour >= TIME_PERIODS.SUNSET.start && hour < TIME_PERIODS.SUNSET.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.SUNSET.start * 3600) / (LIGHT_CONSTANTS.SUNSET_DURATION * 3600);
    // Обратная синусоидальная кривая
    const smoothProgress = Math.cos(progress * Math.PI / 2);
    return LIGHT_CONSTANTS.SUNSET_END + ((LIGHT_CONSTANTS.SUNSET_START - LIGHT_CONSTANTS.SUNSET_END) * smoothProgress);
  }
  
  // Сумерки (19:00-21:00) - быстрое затемнение
  if (hour >= TIME_PERIODS.TWILIGHT.start && hour < TIME_PERIODS.TWILIGHT.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.TWILIGHT.start * 3600) / (LIGHT_CONSTANTS.TWILIGHT_DURATION * 3600);
    const smoothProgress = Math.cos(progress * Math.PI / 2);
    return LIGHT_CONSTANTS.TWILIGHT_END + ((LIGHT_CONSTANTS.TWILIGHT_START - LIGHT_CONSTANTS.TWILIGHT_END) * smoothProgress);
  }
  
  // Поздние сумерки (21:00-22:00) - финальное затемнение
  if (hour >= TIME_PERIODS.LATE_TWILIGHT.start && hour < TIME_PERIODS.LATE_TWILIGHT.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.LATE_TWILIGHT.start * 3600) / (LIGHT_CONSTANTS.LATE_TWILIGHT_DURATION * 3600);
    const smoothProgress = Math.cos(progress * Math.PI / 2);
    return LIGHT_CONSTANTS.LATE_TWILIGHT_END + ((LIGHT_CONSTANTS.LATE_TWILIGHT_START - LIGHT_CONSTANTS.LATE_TWILIGHT_END) * smoothProgress);
  }
  
  return LIGHT_CONSTANTS.DEEP_NIGHT_LEVEL; // Fallback
}

/**
 * Получает цвет освещения в зависимости от времени дня с точностью до секунд
 */
export function getLightColor(gameTime: GameTime): { r: number; g: number; b: number; a: number } {
  const { hour, minute, second = 0 } = gameTime;
  const currentTimeInSeconds = hour * 3600 + minute * 60 + second;
  
  // Глубокая ночь - темно-синий
  if ((hour >= TIME_PERIODS.DEEP_NIGHT.start) || (hour < TIME_PERIODS.DEEP_NIGHT.end)) {
    return LIGHT_COLORS.NIGHT;
  }
  
  // Предрассветные сумерки - переход к рассвету
  if (hour >= TIME_PERIODS.PREDAWN.start && hour < TIME_PERIODS.PREDAWN.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.PREDAWN.start * 3600) / (LIGHT_CONSTANTS.PREDAWN_DURATION * 3600);
    // Плавный переход от ночного к рассветному цвету
    return {
      r: Math.round(LIGHT_COLORS.NIGHT.r + (LIGHT_COLORS.DAWN.r - LIGHT_COLORS.NIGHT.r) * progress),
      g: Math.round(LIGHT_COLORS.NIGHT.g + (LIGHT_COLORS.DAWN.g - LIGHT_COLORS.NIGHT.g) * progress),
      b: Math.round(LIGHT_COLORS.NIGHT.b + (LIGHT_COLORS.DAWN.b - LIGHT_COLORS.NIGHT.b) * progress),
      a: LIGHT_COLORS.NIGHT.a + (LIGHT_COLORS.DAWN.a - LIGHT_COLORS.NIGHT.a) * progress
    };
  }
  
  // Рассвет - оранжевый оттенок
  if (hour >= TIME_PERIODS.DAWN.start && hour < TIME_PERIODS.DAWN.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.DAWN.start * 3600) / (LIGHT_CONSTANTS.DAWN_DURATION * 3600);
    // Переход от рассветного к дневному
    return {
      r: Math.round(LIGHT_COLORS.DAWN.r + (LIGHT_COLORS.DAY.r - LIGHT_COLORS.DAWN.r) * progress),
      g: Math.round(LIGHT_COLORS.DAWN.g + (LIGHT_COLORS.DAY.g - LIGHT_COLORS.DAWN.g) * progress),
      b: Math.round(LIGHT_COLORS.DAWN.b + (LIGHT_COLORS.DAY.b - LIGHT_COLORS.DAWN.b) * progress),
      a: LIGHT_COLORS.DAWN.a + (LIGHT_COLORS.DAY.a - LIGHT_COLORS.DAWN.a) * progress
    };
  }
  
  // Полный день - нейтральное освещение (без фильтра)
  if (hour >= TIME_PERIODS.FULL_DAY.start && hour < TIME_PERIODS.FULL_DAY.end) {
    return LIGHT_COLORS.DAY;
  }
  
  // Закат - красно-оранжевый
  if (hour >= TIME_PERIODS.SUNSET.start && hour < TIME_PERIODS.SUNSET.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.SUNSET.start * 3600) / (LIGHT_CONSTANTS.SUNSET_DURATION * 3600);
    // Переход от дневного к закатному
    return {
      r: Math.round(LIGHT_COLORS.DAY.r + (LIGHT_COLORS.SUNSET.r - LIGHT_COLORS.DAY.r) * progress),
      g: Math.round(LIGHT_COLORS.DAY.g + (LIGHT_COLORS.SUNSET.g - LIGHT_COLORS.DAY.g) * progress),
      b: Math.round(LIGHT_COLORS.DAY.b + (LIGHT_COLORS.SUNSET.b - LIGHT_COLORS.DAY.b) * progress),
      a: LIGHT_COLORS.DAY.a + (LIGHT_COLORS.SUNSET.a - LIGHT_COLORS.DAY.a) * progress
    };
  }
  
  // Сумерки - синий оттенок
  if (hour >= TIME_PERIODS.TWILIGHT.start && hour < TIME_PERIODS.TWILIGHT.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.TWILIGHT.start * 3600) / (LIGHT_CONSTANTS.TWILIGHT_DURATION * 3600);
    // Переход от закатного к сумеречному
    return {
      r: Math.round(LIGHT_COLORS.SUNSET.r + (LIGHT_COLORS.TWILIGHT.r - LIGHT_COLORS.SUNSET.r) * progress),
      g: Math.round(LIGHT_COLORS.SUNSET.g + (LIGHT_COLORS.TWILIGHT.g - LIGHT_COLORS.SUNSET.g) * progress),
      b: Math.round(LIGHT_COLORS.SUNSET.b + (LIGHT_COLORS.TWILIGHT.b - LIGHT_COLORS.SUNSET.b) * progress),
      a: LIGHT_COLORS.SUNSET.a + (LIGHT_COLORS.TWILIGHT.a - LIGHT_COLORS.SUNSET.a) * progress
    };
  }
  
  // Поздние сумерки - переход к ночи
  if (hour >= TIME_PERIODS.LATE_TWILIGHT.start && hour < TIME_PERIODS.LATE_TWILIGHT.end) {
    const progress = (currentTimeInSeconds - TIME_PERIODS.LATE_TWILIGHT.start * 3600) / (LIGHT_CONSTANTS.LATE_TWILIGHT_DURATION * 3600);
    // Переход от сумеречного к ночному
    return {
      r: Math.round(LIGHT_COLORS.TWILIGHT.r + (LIGHT_COLORS.NIGHT.r - LIGHT_COLORS.TWILIGHT.r) * progress),
      g: Math.round(LIGHT_COLORS.TWILIGHT.g + (LIGHT_COLORS.NIGHT.g - LIGHT_COLORS.TWILIGHT.g) * progress),
      b: Math.round(LIGHT_COLORS.TWILIGHT.b + (LIGHT_COLORS.NIGHT.b - LIGHT_COLORS.TWILIGHT.b) * progress),
      a: LIGHT_COLORS.TWILIGHT.a + (LIGHT_COLORS.NIGHT.a - LIGHT_COLORS.TWILIGHT.a) * progress
    };
  }
  
  // Fallback - ночной цвет
  return LIGHT_COLORS.NIGHT;
}

/**
 * Создает новое игровое время
 */
export function createGameTime(day: number = 1, hour: number = 12, minute: number = 0, second: number = 0): GameTime {
  return {
    day,
    hour: Math.max(0, Math.min(23, hour)),
    minute: Math.max(0, Math.min(59, minute)),
    second: Math.max(0, Math.min(59, second)),
    season: getSeason(day)
  };
}

/**
 * Определяет сезон по дню
 */
function getSeason(day: number): 'spring' | 'summer' | 'autumn' | 'winter' {
  const dayInYear = day % (TIME_CONSTANTS.DAYS_PER_SEASON * 4);
  
  if (dayInYear < TIME_CONSTANTS.DAYS_PER_SEASON) return 'spring';
  if (dayInYear < TIME_CONSTANTS.DAYS_PER_SEASON * 2) return 'summer';
  if (dayInYear < TIME_CONSTANTS.DAYS_PER_SEASON * 3) return 'autumn';
  return 'winter';
}

/**
 * Форматирует время для отображения
 */
export function formatGameTime(gameTime: GameTime): string {
  const { day, hour, minute } = gameTime;
  const formattedHour = hour.toString().padStart(2, '0');
  const formattedMinute = minute.toString().padStart(2, '0');
  return `День ${day}, ${formattedHour}:${formattedMinute}`;
}

/**
 * Добавляет минуты к игровому времени
 */
export function addMinutes(gameTime: GameTime, minutes: number): GameTime {
  let { day, hour, minute, second = 0 } = gameTime;
  
  minute += minutes;
  
  while (minute >= TIME_CONSTANTS.MINUTES_PER_HOUR) {
    minute -= TIME_CONSTANTS.MINUTES_PER_HOUR;
    hour++;
  }
  
  while (hour >= TIME_CONSTANTS.HOURS_PER_DAY) {
    hour -= TIME_CONSTANTS.HOURS_PER_DAY;
    day++;
  }
  
  return {
    day,
    hour,
    minute,
    second,
    season: getSeason(day)
  };
}

/**
 * Добавляет секунды к игровому времени для плавных переходов
 */
export function addSeconds(gameTime: GameTime, seconds: number): GameTime {
  let { day, hour, minute, second = 0 } = gameTime;
  
  second += seconds;
  
  while (second >= TIME_CONSTANTS.SECONDS_PER_MINUTE) {
    second -= TIME_CONSTANTS.SECONDS_PER_MINUTE;
    minute++;
  }
  
  while (minute >= TIME_CONSTANTS.MINUTES_PER_HOUR) {
    minute -= TIME_CONSTANTS.MINUTES_PER_HOUR;
    hour++;
  }
  
  while (hour >= TIME_CONSTANTS.HOURS_PER_DAY) {
    hour -= TIME_CONSTANTS.HOURS_PER_DAY;
    day++;
  }
  
  return {
    day,
    hour,
    minute,
    second,
    season: getSeason(day)
  };
}
