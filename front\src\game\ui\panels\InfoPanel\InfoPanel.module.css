
.panel_img{
  position: fixed;
  bottom: 0px;
  right: 0;
  width: 1550px;
  height: 180px;
  background: linear-gradient(135deg, var(--bg-overlay) 0%, rgba(30, 30, 30, 0.9) 100%);
  border: 1px solid var(--border-color);
  border-radius: 8px 8px 0 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.5);
  
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark_layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 8px 8px 0 0;
  background: rgba(0, 0, 0, 0.2);
}

.controlsContainer {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 10;
}