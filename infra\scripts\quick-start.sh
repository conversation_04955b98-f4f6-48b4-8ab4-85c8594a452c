#!/bin/bash

# NuclearStory - Quick Start Script
echo "🚀 Quick Start NuclearStory..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

echo "🔧 Building and starting all services..."

# Build and start everything at once
docker compose -f docker-compose.dev.yml up --build -d

echo ""
echo "✅ NuclearStory is starting up!"
echo ""
echo "🌐 Access points:"
echo "   🎮 Game: http://localhost:3000"
echo "   🌍 API: http://localhost:80"
echo ""
echo "📊 Check status: ./scripts/status.sh"
echo "📝 View logs: ./scripts/dev-logs.sh all"
echo "🛑 Stop: ./scripts/dev-stop.sh"
