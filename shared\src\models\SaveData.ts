import { IsString, Is<PERSON>num, IsNumber, IsOptional, IsArray, IsDate, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { GameStatus } from '../enums';
import { GameStats, Coordinates } from '../types';
import { Quest } from './Quest';
import { PlayerInventoryItem } from './InventoryItem';

export class GameLocation {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  coordinates?: Coordinates;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  discoveredAreas?: string[];

  constructor(partial: Partial<GameLocation> = {}) {
    Object.assign(this, partial);
  }
}

export class GameProgress {
  @IsNumber()
  level: number;

  @IsNumber()
  experience: number;

  @IsNumber()
  experienceToNext: number;

  @IsArray()
  @IsString({ each: true })
  completedQuests: string[];

  @IsArray()
  @IsString({ each: true })
  discoveredLocations: string[];

  @IsArray()
  @IsString({ each: true })
  unlockedAchievements: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  storyFlags?: string[];

  constructor(partial: Partial<GameProgress> = {}) {
    Object.assign(this, partial);
  }
}

export class SaveData {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  saveName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  worldId: string; // ID мира

  @IsEnum(GameStatus)
  gameStatus: GameStatus;

  @ValidateNested()
  @Type(() => Object)
  gameStats: GameStats;

  @ValidateNested()
  @Type(() => GameProgress)
  progress: GameProgress;

  @IsString()
  currentLocationId: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  currentPosition?: Coordinates;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Quest)
  activeQuests: Quest[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  inventory: PlayerInventoryItem[];

  @IsNumber()
  playtimeMinutes: number;

  @IsDate()
  createdAt: Date;

  @IsDate()
  updatedAt: Date;

  @IsOptional()
  @IsDate()
  lastPlayedAt?: Date;

  @IsOptional()
  @IsString()
  screenshot?: string; // base64 or URL

  constructor(partial: Partial<SaveData> = {}) {
    Object.assign(this, partial);
  }
}

export class CreateSaveDataDto {
  @IsString()
  saveName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  gameStats?: GameStats;

  @IsOptional()
  @IsString()
  currentLocationId?: string;
}

export class UpdateSaveDataDto {
  @IsOptional()
  @IsString()
  saveName?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(GameStatus)
  gameStatus?: GameStatus;

  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  gameStats?: GameStats;

  @IsOptional()
  @ValidateNested()
  @Type(() => GameProgress)
  progress?: GameProgress;

  @IsOptional()
  @IsString()
  currentLocationId?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  currentPosition?: Coordinates;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Quest)
  activeQuests?: Quest[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  inventory?: PlayerInventoryItem[];

  @IsOptional()
  @IsNumber()
  playtimeMinutes?: number;

  @IsOptional()
  @IsString()
  screenshot?: string;
}
