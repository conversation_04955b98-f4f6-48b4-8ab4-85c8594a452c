{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "jsxImportSource": "react",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Additional options for better React support */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "shared/*": ["src/shared/src/*"]
    }
  },
  "include": [
    "src",
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/vite-env.d.ts",
    "src/types/global.d.ts",
    "src/types/react.d.ts"
, "clean.cjs"  ],
  "exclude": [
    "node_modules",
    "dist"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
