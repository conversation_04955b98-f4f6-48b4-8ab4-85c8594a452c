/**
 * Простые аналоговые часы в стиле постапокалиптического терминала
 */

import React, { useState, useEffect } from 'react';
import { TimeWaitModal } from '../dialogs/TimeWaitModal';
import { useGameTime } from '../../utils/time/useGameTime';
import { isDaytime } from '../../utils/time/gameTime';

export const TimeControls: React.FC = () => {
  const [isWaitModalOpen, setIsWaitModalOpen] = useState(false);
  const gameTime = useGameTime();
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }
      
      if (event.which === 84 && !event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        setIsWaitModalOpen(true);
      }
      
      if (event.key === 'Escape') {
        setIsWaitModalOpen(false);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  const isDay = isDaytime(gameTime);
  
  return (
    <>
      {/* Терминальный дисплей с постапокалиптическим дизайном */}
      <div style={{
        cursor: 'pointer'
      }} onClick={() => setIsWaitModalOpen(true)}>
        <div style={{
          background: 'linear-gradient(135deg, rgba(15, 15, 15, 0.95) 0%, rgba(30, 30, 30, 0.9) 100%)',
          borderRadius: '8px',
          width: '140px',
          height: '90px',
          border: '2px solid #494721',
          position: 'relative',
          boxShadow: '0 0 20px rgba(148, 196, 125, 0.25)',
          fontFamily: '"Courier New", monospace',
          padding: '8px',
          backdropFilter: 'blur(10px)'
        }}>
          {/* Верхняя строка */}
          <div style={{
            color: '#b7e49d',
            fontSize: '10px',
            textAlign: 'center',
            textShadow: '0 0 5px rgba(148, 196, 125, 0.5)',
            marginBottom: '2px',
            letterSpacing: '1px'
          }}>
            DAY {gameTime.day.toString().padStart(3, '0')} • {isDay ? 'DAY' : 'NIGHT'}
          </div>
          
          {/* Время крупно */}
          <div style={{
            color: '#b7e49d',
            fontSize: '24px',
            textAlign: 'center',
            textShadow: '0 0 8px rgba(148, 196, 125, 0.7)',
            fontWeight: 'bold',
            letterSpacing: '2px',
            marginBottom: '4px'
          }}>
            {gameTime.hour.toString().padStart(2, '0')}:{gameTime.minute.toString().padStart(2, '0')}
          </div>
          
          {/* Нижняя строка */}
          <div style={{
            color: '#9bb891',
            fontSize: '8px',
            textAlign: 'center',
            textShadow: '0 0 3px rgba(148, 196, 125, 0.3)',
            letterSpacing: '1px',
            opacity: 0.8
          }}>
            {gameTime.season?.toUpperCase()}
          </div>
          
          {/* Индикатор в углу */}
          <div style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            width: '8px',
            height: '8px',
            backgroundColor: isDay ? '#494721' : '#6b8f5c',
            borderRadius: '50%',
            boxShadow: `0 0 6px ${isDay ? '#494721' : '#6b8f5c'}`
          }} />
        </div>
      </div>
      
      <TimeWaitModal 
        isOpen={isWaitModalOpen}
        onClose={() => setIsWaitModalOpen(false)}
      />
    </>
  );
};
