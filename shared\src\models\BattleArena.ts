import { BattlePhase, TerrainType } from '../enums'
import { Position } from './Player'

export interface BattleCell {
  position: Position
  terrain: TerrainType
  isBlocked: boolean
  hasObstacle: boolean
  obstacleType?: string
  movementCost: number
  coverBonus: number
}

export interface BattleUnit {
  id: string
  name: string
  type: 'player' | 'npc' | 'enemy'
  
  // Position and movement
  position: Position
  movementRange: number
  hasActed: boolean
  hasMoved: boolean
  
  // Combat stats
  currentHP: number
  maxHP: number
  currentAP: number
  maxAP: number
  initiative: number
  
  // Combat abilities
  attackRange: number
  damage: number
  accuracy: number
  criticalChance: number
  armor: number
  
  // Status
  isAlive: boolean
  isStunned: boolean
  statusEffects: string[]
  
  // Equipment
  equippedWeapon?: string
  equippedArmor?: string
  
  // AI behavior (for NPCs)
  aiType?: 'aggressive' | 'defensive' | 'support' | 'flee'
  targetId?: string
}

export interface BattleAction {
  id: string
  unitId: string
  type: 'move' | 'attack' | 'use_item' | 'wait' | 'reload'
  target?: Position | string // Position for move, Unit ID for attack
  itemId?: string
  apCost: number
  timestamp: Date
}

export interface BattleArena {
  id: string
  name: string
  description: string
  
  // Grid configuration
  gridSize: {
    width: number
    height: number
  }
  cells: BattleCell[][] // 2D array of cells
  
  // Battle state
  phase: BattlePhase
  currentTurn: number
  maxTurns: number
  timeLimit?: number // seconds
  
  // Participants
  units: BattleUnit[]
  playerUnits: string[] // Unit IDs
  enemyUnits: string[] // Unit IDs
  
  // Turn order
  initiativeOrder: string[] // Unit IDs sorted by initiative
  currentUnitIndex: number
  currentUnitId: string
  
  // Battle history
  actions: BattleAction[]
  battleLog: string[]
  
  // Victory conditions
  victoryConditions: {
    eliminateAllEnemies: boolean
    surviveTurns?: number
    protectTarget?: string // Unit ID
    reachPosition?: Position
  }
  
  // Battle result
  isCompleted: boolean
  winner?: 'player' | 'enemy' | 'draw'
  rewards?: {
    experience: number
    items: string[] // Item IDs
    currency: number
  }
  
  // Environment effects
  environmentEffects: {
    radiation: number
    visibility: number
    weather: string
    timeOfDay: 'day' | 'night' | 'dawn' | 'dusk'
  }
  
  // Timestamps
  startedAt: Date
  endedAt?: Date
  lastActionAt: Date
}
