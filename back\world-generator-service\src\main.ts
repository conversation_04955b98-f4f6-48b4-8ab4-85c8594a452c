import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Включаем CORS для разработки
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // Глобальная валидация
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: false,
  }));

  // Global prefix
  app.setGlobalPrefix('api');

  // Настройка Swagger документации
  const config = new DocumentBuilder()
    .setTitle('World Generator Service')
    .setDescription('API для генерации игрового мира в NuclearStory')
    .setVersion('1.0')
    .addTag('world-generator')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({ status: 'healthy', service: 'world-generator-service' });
  });

  // Порт из переменной окружения или 3003 по умолчанию
  const port = process.env.PORT || 3003;
  
  await app.listen(port);
}

bootstrap();
