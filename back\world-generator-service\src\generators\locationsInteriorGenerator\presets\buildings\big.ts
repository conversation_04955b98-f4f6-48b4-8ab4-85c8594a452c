import { PresetLocationMap } from "../presetType";


export const locationConfigs: Record<string, PresetLocationMap > = {

'mediumBuilding_1': {
  name: 'bigBuilding_1',
  width: 20,
  height: 20,
  tokenMap: 
  [
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "R", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "x", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "w", "w", "w", "w", "w", "w", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "C", "C", "0", "0", "0", "w", "0", "0", "0", "0", "0", "x", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "0", "0", "0", "0", "C", "w", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "0", "0", "0", "0", "C", "w", "0", "R", "X", "X", "R", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "w", "w", "w", "D", "w", "w", "w", "w", "w", "w", "w", "v", "v", "v", "v", "w", "w", "D", "w", "w", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "R", "w", "0", "L", "0", "0", "C", "w", "0", "C", "C", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "w", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "R", "w", "0", "0", "0", "0", "C", "w", "0", "0", "0", "0", "c", "0", "c", "0", "0", "0", "0", "0", "0", "v", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "v", "0", "0", "0", "x", "0", "w", "0", "x", "0", "0", "t", "L", "t", "0", "x", "0", "0", "0", "t", "v", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "v", "0", "0", "0", "0", "0", "D", "0", "0", "0", "0", "t", "t", "t", "c", "0", "0", "0", "L", "s", "v", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "B", "0", "0", "0", "0", "w", "0", "0", "0", "0", "0", "c", "0", "0", "0", "0", "0", "0", "B", "v", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "B", "0", "0", "0", "h", "w", "0", "0", "x", "0", "0", "0", "0", "0", "0", "t", "t", "f", "t", "w", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "w", "0", "h", "B", "B", "0", "w", "0", "0", "0", "0", "w", "w", "w", "w", "w", "w", "w", "w", "w", "w", "X", "0", "0", "x", "0"],
    ["0", "w", "w", "w", "w", "w", "w", "w", "w", "w", "w", "w", "w", "0", "0", "0", "0", "w", "X", "X", "X", "i", "R", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "w", "0", "s", "0", "0", "w", "0", "0", "0", "0", "L", "0", "0", "0", "0", "0", "w", "0", "0", "0", "0", "0", "0", "0", "0", "F", "0", "0", "0", "0", "0"],
    ["0", "w", "0", "0", "0", "0", "D", "0", "x", "S", "S", "0", "0", "0", "x", "0", "0", "w", "0", "0", "0", "0", "0", "0", "0", "0", "F", "0", "0", "0", "0", "0"],
    ["0", "w", "a", "0", "x", "x", "w", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "D", "0", "0", "0", "0", "0", "x", "0", "0", "F", "0", "0", "0", "0", "0"],
    ["0", "w", "a", "0", "0", "0", "w", "0", "0", "t", "V", "0", "0", "0", "0", "0", "0", "w", "0", "0", "0", "0", "0", "x", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["X", "w", "0", "0", "0", "L", "w", "0", "0", "x", "0", "0", "0", "0", "x", "x", "0", "w", "L", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["X", "w", "0", "o", "0", "0", "w", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "w", "i", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "w", "w", "w", "w", "w", "w", "w", "v", "v", "v", "v", "v", "w", "w", "w", "w", "w", "F", "0", "0", "0", "F", "F", "F", "F", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "x", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "R", "0", "i", "0", "0", "0", "0", "x", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "K", "K", "K", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "K", "K", "K", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]
  ],
  anchor: { x: 20, y: 20 }, //смещение относительно центра локации  в любую сторону
  rotations: false,
  mirror: true,
  weight: 1,



}
}