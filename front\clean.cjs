const fs = require('fs');
const file = 'src/game/comp/IMComp/animations.json';

const data = JSON.parse(fs.readFileSync(file, 'utf8'));

// Очистка полей
function deepClean(obj) {
  if (Array.isArray(obj)) {
    return obj.map(deepClean);
  } else if (obj && typeof obj === 'object') {
    const res = {};
    for (const [k, v] of Object.entries(obj)) {
      if (k !== 'rightFoot' && k !== 'leftFoot') {
        res[k] = deepClean(v);
      }
    }
    return res;
  }
  return obj;
}

for (const key in data) {
  if (Array.isArray(data[key])) {
    data[key] = data[key].map(deepClean);
  }
}

// ===== Умный форматтер =====
function smartStringify(obj, maxLen = 180, indent = 0) {
  const compact = JSON.stringify(obj, null, 0);
  if (compact.length <= maxLen) {
    return compact;
  }

  if (Array.isArray(obj)) {
    const items = obj.map(i => smartStringify(i, maxLen, indent + 2));
    const pad = ' '.repeat(indent + 2);
    return '[\n' + items.map(i => pad + i).join(',\n') + '\n' + ' '.repeat(indent) + ']';
  }

  if (obj && typeof obj === 'object') {
    const entries = Object.entries(obj).map(([k, v]) => {
      const key = JSON.stringify(k);
      const val = smartStringify(v, maxLen, indent + 2);
      return ' '.repeat(indent + 2) + key + ': ' + val;
    });
    return '{\n' + entries.join(',\n') + '\n' + ' '.repeat(indent) + '}';
  }

  return JSON.stringify(obj);
}

// Получаем максимальную длину строки из аргументов командной строки
const argLen = parseInt(process.argv[2], 10);
const maxLen = (!isNaN(argLen) && argLen > 0) ? argLen : 150;

fs.writeFileSync(file, smartStringify(data, maxLen, 0), 'utf8');

// ===== Новый постобработчик: объединение {} в одну строку с ограничением по длине =====
function joinEmptyObjectsLines(filePath, maxLineLength = 200) {
  const lines = fs.readFileSync(filePath, 'utf8').split('\n');
  const result = [];
  let buffer = [];

  function flushBuffer(nextLine) {
    if (buffer.length === 0) return;

    // Убираем запятые в конце отдельных {}, если есть
    let joined = buffer.map(obj => obj.trim().replace(/,$/, '')).join(', ');

    // Если длина превышает maxLineLength, разбиваем на строки
    if (joined.length > maxLineLength) {
      let currentLine = '';
      const parts = [];
      for (const obj of buffer.map(o => o.trim().replace(/,$/, ''))) {
        const candidate = currentLine ? currentLine + ', ' + obj : obj;
        if (candidate.length <= maxLineLength) {
          currentLine = candidate;
        } else {
          parts.push(currentLine);
          currentLine = obj;
        }
      }
      if (currentLine) parts.push(currentLine);
      // Добавляем запятую после каждой строки, кроме последней
      for (let i = 0; i < parts.length; i++) {
        // Запятая, если это не последняя строка или следующий символ не ]
        const addComma = (i < parts.length - 1) || (nextLine && !nextLine.trim().startsWith(']'));
        result.push(parts[i] + (addComma ? ',' : ''));
      }
    } else {
      // Если строка маленькая — просто добавляем и проверяем запятую
      const addComma = nextLine && !nextLine.trim().startsWith(']');
      result.push(joined + (addComma ? ',' : ''));
    }

    buffer = [];
  }

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (/^\s*\{\},?\s*$/.test(line)) {
      buffer.push(line);
    } else {
      flushBuffer(line);
      result.push(line);
    }
  }
  flushBuffer(null);

  fs.writeFileSync(filePath, result.join('\n'), 'utf8');
}





module.exports = { joinEmptyObjectsLines };
joinEmptyObjectsLines(file, maxLen);

console.log('done');
