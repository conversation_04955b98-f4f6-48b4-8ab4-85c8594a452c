// Типы для аутентификации и авторизации

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    username: string
    role: string
  }
  token: string
}

export interface UserProfile {
  id: string
  email: string
  username: string
  role: string
  isActive: boolean
  createdAt: string
  lastLoginAt?: string
}

export interface UpdateProfileRequest {
  username: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface UpdateProfileResponse {
  id: string
  username: string
  email: string
  role: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ChangePasswordResponse {
  message: string
}