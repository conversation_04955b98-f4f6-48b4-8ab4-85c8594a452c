/**
 * Системное меню с выпадающими опциями
 */

import React, { useState, useRef, useEffect } from 'react';
import { Settings, Save, Upload, LogOut, Volume2, VolumeX, Menu } from 'lucide-react';
import styles from './SystemMenu.module.css';

interface SystemMenuProps {
  onSave: () => void;
  onLoad: () => void;
  onSettings: () => void;
  onExit: () => void;
  onToggleMute: () => void;
  isMuted: boolean;
}

export const SystemMenu: React.FC<SystemMenuProps> = ({
  onSave,
  onLoad,
  onSettings,
  onExit,
  onToggleMute,
  isMuted
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Закрываем меню при клике вне его
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Закрываем меню при нажатии Escape
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleMenuAction = (action: () => void) => {
    action();
    setIsMenuOpen(false);
  };

  return (
    <div className={styles.systemMenu} ref={menuRef}>
      {/* Главная кнопка меню */}
      <button 
        className={`${styles.menuButton} ${isMenuOpen ? styles.menuButtonActive : ''}`}
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        title="Системное меню"
      >
        <Menu />
      </button>

      {/* Выпадающее меню */}
      {isMenuOpen && (
        <div className={styles.dropdown}>
          <div className={styles.dropdownContent}>
            <button 
              className={styles.menuOption}
              onClick={() => handleMenuAction(onToggleMute)}
              title={isMuted ? 'Включить звук' : 'Выключить звук'}
            >
              {isMuted ? <VolumeX /> : <Volume2 />}
              <span>{isMuted ? 'Включить звук' : 'Выключить звук'}</span>
            </button>
            
            <button 
              className={styles.menuOption}
              onClick={() => handleMenuAction(onSave)}
              title="Сохранить игру"
            >
              <Save />
              <span>Сохранить</span>
            </button>
            
            <button 
              className={styles.menuOption}
              onClick={() => handleMenuAction(onLoad)}
              title="Загрузить игру"
            >
              <Upload />
              <span>Загрузить</span>
            </button>
            
            
            <button 
              className={styles.menuOption}
              onClick={() => handleMenuAction(onSettings)}
              title="Настройки"
            >
              <Settings />
              <span>Настройки</span>
            </button>
            
            <div className={styles.separator} />
            
            <button 
              className={`${styles.menuOption} ${styles.exitOption}`}
              onClick={() => handleMenuAction(onExit)}
              title="Выйти в меню"
            >
              <LogOut />
              <span>Выйти в меню</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
