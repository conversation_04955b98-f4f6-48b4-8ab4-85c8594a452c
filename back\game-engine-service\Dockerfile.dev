# Development Dockerfile with hot reload
FROM node:22-alpine

WORKDIR /app

# Install dependencies for development
RUN apk add --no-cache curl

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# For development, we'll run as root for simplicity
# In production, you should create a non-root user

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Start with hot reload
CMD ["npm", "run", "start:dev"]
