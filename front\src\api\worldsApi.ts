import { AuthApiClient } from '../utils/authFetch';
import { 
  WorldSettings, 
  WorldSummary, 
  WorldMap,
  CreateWorldRequest,
  CreateWorldResponse,
  GetWorldsResponse,
  GetWorldResponse,
  UpdateWorldResponse,
  SaveWorldResponse,
  DeleteWorldResponse
} from '../shared/types';

const GENERATOR_SERVICE_URL = import.meta.env.VITE_WORLD_GENERATOR_SERVICE_URL || 'http://localhost:3003/api';
const SAVE_SERVICE_URL = import.meta.env.VITE_SAVE_SERVICE_URL || 'http://localhost:3004/api';

const generatorClient = new AuthApiClient(GENERATOR_SERVICE_URL);
const saveClient = new AuthApiClient(SAVE_SERVICE_URL);

export async function createWorld(data: {
  name: string;
  description: string;
  userId: string;
  settings?: WorldSettings;
}): Promise<{ success: boolean; world: any }> {
  try {
    const result = await generatorClient.post<{ success: boolean; world: any }>('/generate-world', data);
    return result;
  } catch (error) {
    throw error;
  }
}

export async function createWorldWithProgress(data: {
  name: string;
  description: string;
  userId: string;
  settings?: WorldSettings;
}): Promise<{ sessionId: string }> {
  try {
    const WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL || 'http://localhost:3003';
    
    const response = await fetch(`${WEBSOCKET_URL}/generate-world-with-progress`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-Id': data.userId
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
}

export async function getUserWorlds(userId: string): Promise<{ worlds: WorldSummary[] }> {
  const { token, isAuthenticated } = await import('../store/authStore').then((m) => m.useAuthStore.getState());
  try {
    const data = await saveClient.get<{ success: boolean; worlds: WorldSummary[] }>(`/worlds?userId=${userId}`);
    if (data.success && data.worlds) {
      return { worlds: data.worlds };
    } else {
      return { worlds: [] };
    }
  } catch (err) {
    if (err instanceof Error && err.message.includes('404')) {
      return { worlds: [] };
    }
    throw err;
  }
}

export async function getWorldById(worldId: string, userId: string): Promise<{ success: boolean; world: WorldMap }> {
  try {
    const result = await saveClient.get<{ success: boolean; world: WorldMap }>(`/worlds/${worldId}?userId=${userId}`);
    return result;
  } catch (error) {
    throw error;
  }
}

export async function updateWorld(worldId: string, worldData: WorldMap): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await saveClient.put<{ success: boolean; error?: string }>(`/worlds/${worldId}`, worldData);
    return result;
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function saveWorldWithNewName(worldData: WorldMap, newName: string): Promise<{ success: boolean; worldId?: string; error?: string }> {
  try {
    const worldToSave = {
      ...worldData,
      name: newName
    };
    const result = await saveClient.post<{ success: boolean; worldId?: string; error?: string }>('/worlds', worldToSave);
    return result;
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function replaceWorldById(worldId: string, worldData: WorldMap): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await saveClient.put<{ success: boolean; error?: string }>(`/worlds/${worldId}`, worldData);
    return result;
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function deleteWorld(worldId: string, userId: string): Promise<{ success: boolean }> {
  try {
    const result = await saveClient.delete<{ success: boolean }>(`/worlds/${worldId}?userId=${userId}`);
    return result;
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      return { success: true };
    }
    throw error;
  }
}
