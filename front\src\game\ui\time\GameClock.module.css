.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.window {
  background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
  border: 2px solid #999;
  border-radius: 8px 8px 0 0;
  width: 400px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.titleBar {
  background: linear-gradient(90deg, #4a90e2, #357abd);
  border-bottom: 1px solid #2968a3;
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  user-select: none;
}

.titleBarContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 10px;
}

.windowTitle {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.windowControls {
  display: flex;
  gap: 2px;
}

.controlButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.2s;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.3);
}

.content {
  padding: 20px;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.zoomControls {
  width: 100%;
  margin-bottom: 20px;
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoomLabel {
  display: block;
  color: #333;
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
  font-size: 14px;
}

.zoomSlider {
  width: 100%;
  height: 6px;
  background: #ddd;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.zoomSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #4a90e2;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.zoomSlider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #4a90e2;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.clockContainer {
  background: white;
  border-radius: 50%;
  padding: 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  margin-bottom: 20px;
  border: 3px solid #ddd;
}

.clock {
  display: block;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.timeInfo {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
}

.digitalTime {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  font-family: 'Courier New', monospace;
  margin-bottom: 10px;
  letter-spacing: 2px;
}

.gameInfo {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.dayInfo {
  color: #4a90e2;
  font-weight: 600;
  font-size: 14px;
}

.lightInfo {
  color: #666;
  font-size: 12px;
}

/* Анимации */
.window {
  animation: windowOpen 0.3s ease-out;
}

@keyframes windowOpen {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Эффекты для часов */
.clock {
  filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.2));
}

/* Адаптивность */
@media (max-width: 768px) {
  .window {
    width: 90%;
    max-width: 350px;
  }
  
  .content {
    padding: 15px;
  }
  
  .digitalTime {
    font-size: 24px;
  }
  
  .clockContainer {
    padding: 15px;
  }
}
