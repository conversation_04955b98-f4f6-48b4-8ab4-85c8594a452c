// Константы для генерации террариан маркеров
export const TERRARIAN_MARKER_CONFIG = {
  // Вероятность появления террариан маркера на клетке (0.0 - 1.0)
  SPAWN_CHANCE: 0.04, // 4% - оригинальное значение
};

// Константы для шаблонных декораций (горы, реки, озера)
export const PATTERN_DECORATION_CONFIG = {
  // Шанс применения шаблона к террариан маркеру (0-100%)
  APPLICATION_CHANCE: 70, // 80% - оригинальное значение
  
  // Веса для выбора типа шаблонной декорации (равные веса как было изначально)
  PATTERN_WEIGHTS: {
    MOUNTAINS: 1.0,  // Горы
    RIVERS: 0.4,     // Реки  
    LAKES: 0.3,      // Озера
    TOWNS: 0.5,      // Городки
  },
  
  // Интервал обновления прогресса (каждые N маркеров)
  PROGRESS_UPDATE_INTERVAL: 50,
};

// Константы для генерации декораций
export const DECORATION_CONFIG = {
  // Шансы выбора типа декорации при обнаружении маркера
  DECORATION_CHANCES: {
    FOREST: 25,
    SWAMP: 5,
    RUINS: 15,
    RUBBLE: 10,
  },
  
  // Настройки леса
  FOREST: {
    MAX_RADIUS: 6,
    CHANCES: [80, 80, 40, 20, 10, 5], // По радиусам: центр, 1, 2, 3
  },
  
  // Настройки болота
  SWAMP: {
    MAX_RADIUS: 1,
    CHANCES: [50, 40], // По радиусам: центр, 1
  },
  
  // Настройки руин
  RUINS: {
    MAX_RADIUS: 2,
    CHANCES: [70, 50, 25], // По радиусам: центр, 1, 2
  },
  
  // Настройки обломков
  RUBBLE: {
    MAX_RADIUS: 2,
    CHANCES: [100, 70, 30], // По радиусам: центр, 1, 2
  },
};

// Константы для стабильной генерации декораций на основе размера карты
export const STABLE_DECORATION_CONFIG = {
  // Базовые коэффициенты для расчета количества декораций
  // Для карты 200x200 (40000 клеток) будет примерно:
  // Лес: 7000 клеток, Кусты: 7000 клеток
  DENSITY_COEFFICIENTS: {
    FOREST: 0.225,      // 17.5% от общей площади карты
    BUSHES: 0.225,      // 17.5% от общей площади карты  
    SWAMP: 0.05,        // 5% от общей площади карты
    RUINS: 0.02,        // 2% от общей площади карты
    RUBBLE: 0.03,       // 3% от общей площади карты
  },
  
  // Минимальное расстояние между центрами декораций одного типа
  MIN_DISTANCE_BETWEEN_CENTERS: {
    FOREST: 15,         // Минимум 15 клеток между лесами
    BUSHES: 12,         // Минимум 12 клеток между группами кустов
    SWAMP: 20,          // Минимум 20 клеток между болотами
    RUINS: 25,          // Минимум 25 клеток между руинами
    RUBBLE: 15,         // Минимум 15 клеток между кучами мусора
  },
  
  // Размеры областей для каждого типа декорации
  AREA_SIZES: {
    FOREST: { min: 3, max: 8 },      // Радиус леса от 3 до 8
    BUSHES: { min: 2, max: 8 },      // Радиус кустов от 2 до 5
    SWAMP: { min: 2, max: 3 },       // Радиус болота от 1 до 2 (максимум 4x4 пятна)
    RUINS: { min: 1, max: 3 },       // Радиус руин от 1 до 3
    RUBBLE: { min: 1, max: 2 },      // Радиус мусора от 1 до 3
  }
};