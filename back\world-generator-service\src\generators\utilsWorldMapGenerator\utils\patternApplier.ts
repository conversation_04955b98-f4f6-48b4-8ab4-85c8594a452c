import { 
  DecorationPattern, 
  PatternMatrix, 
  PatternApplicationResult,
  PatternValue 
} from '../../../shared/types/PatternTypes';

/**
 * Универсальная функция для наложения шаблонов декораций на карту
 */
export class PatternApplier {
  
  /**
   * Применяет шаблон к карте с проверкой границ
   * @param worldMap - карта мира для изменения
   * @param pattern - шаблон для применения
   * @param centerX - X координата центра размещения (где находится стартовая точка 0)
   * @param centerY - Y координата центра размещения (где находится стартовая точка 0)
   * @param decorationBorder - границы для применения декораций [1=север, 2=юг, 3=запад, 4=восток, 5=все стороны]
   * @returns результат применения шаблона
   */
  public static applyPattern(
    worldMap: number[][],
    pattern: DecorationPattern,
    centerX: number,
    centerY: number,
    decorationBorder?: number[]
  ): PatternApplicationResult {
    
    const mapHeight = worldMap.length;
    const mapWidth = mapHeight > 0 ? worldMap[0].length : 0;
    
    const matrix = pattern.matrix;
    const patternHeight = matrix.length;
    const patternWidth = patternHeight > 0 ? matrix[0].length : 0;
    
    // Используем центр шаблона (нулевой индекс)
    const patternCenterX = Math.floor(patternWidth / 2);
    const patternCenterY = Math.floor(patternHeight / 2);
    
    // Вычисляем смещение для размещения центра шаблона в указанной позиции
    const offsetX = centerX - patternCenterX;
    const offsetY = centerY - patternCenterY;
    
    let appliedCells = 0;
    let skippedCells = 0;
    let outOfBounds = 0;
    
    // Применяем шаблон
    for (let py = 0; py < patternHeight; py++) {
      for (let px = 0; px < patternWidth; px++) {
        const patternValue = matrix[py][px];
        
        // Пропускаем стартовую точку (0) - она используется только для позиционирования
        if (patternValue === 0) {
          skippedCells++;
          continue;
        }
        
        // Вычисляем координаты на карте
        const mapX = offsetX + px;
        const mapY = offsetY + py;
        
        // Проверяем границы карты
        if (mapX < 0 || mapX >= mapWidth || mapY < 0 || mapY >= mapHeight) {
          outOfBounds++;
          continue;
        }
        
        // Проверяем границы декораций если указаны
        if (decorationBorder && !this.isWithinDecorationBorder(mapX, mapY, mapWidth, mapHeight, decorationBorder)) {
          skippedCells++;
          continue;
        }
        
        // Применяем значение из шаблона (логика "0 = не перезаписывать" уже обработана выше)
        const mappedValue = this.mapPatternValueToDecoration(patternValue);
        if (mappedValue !== null) {
          worldMap[mapY][mapX] = mappedValue;
          appliedCells++;
        } else {
          skippedCells++;
        }
      }
    }
    
    return {
      success: appliedCells > 0,
      appliedCells,
      skippedCells,
      outOfBounds,
      pattern,
      position: { x: centerX, y: centerY }
    };
  }
  
    
  /**
   * Проверяет, находится ли точка в пределах границ декораций
   */
  private static isWithinDecorationBorder(
    x: number, 
    y: number, 
    mapWidth: number, 
    mapHeight: number, 
    decorationBorder: number[]
  ): boolean {
    // Если указано "все стороны" (5), то применяем везде
    if (decorationBorder.includes(5)) {
      return true;
    }
    
    const isNorthBorder = y === 0;
    const isSouthBorder = y === mapHeight - 1;
    const isWestBorder = x === 0;
    const isEastBorder = x === mapWidth - 1;
    
    // Проверяем каждую указанную границу
    for (const border of decorationBorder) {
      switch (border) {
        case 1: // Север
          if (isNorthBorder) return true;
          break;
        case 2: // Юг
          if (isSouthBorder) return true;
          break;
        case 3: // Запад
          if (isWestBorder) return true;
          break;
        case 4: // Восток
          if (isEastBorder) return true;
          break;
      }
    }
    
    return false;
  }
  
  /**
   * Маппинг значений шаблона на типы декораций
   * Согласно плану:
   * 0 - ничего (не перезаписывать) - уже обработано
   * 1 - горы
   * 2 - лес
   * 3 - кусты  
   * 4 - озеро
   * 5 - река
   * 6 - руины
   * 7 - мусор
   */
  private static mapPatternValueToDecoration(patternValue: number): number | null {
    switch (patternValue) {
      case PatternValue.MOUNTAINS: // 1
        return 1; // Горы
      case PatternValue.FOREST: // 2
        return 2; // Лес
      case PatternValue.BUSHES: // 3
        return 3; // Кусты
      case PatternValue.LAKE: // 4
        return 4; // Озеро
      case PatternValue.RIVER: // 5
        return 5; // Река
      case PatternValue.RUINS: // 6
        return 6; // Руины
      case PatternValue.RUBBLE: // 7
        return 7; // Мусор
      case PatternValue.SWAMP: // 8
        return 8; // Болото 
        case PatternValue.CITY: // 9
        return 9; // Город
      case PatternValue.ROAD: // 10
        return 10; // Дорога
      default:  
        return null; // Неизвестное значение - пропускаем
    }
  }
  
  /**
   * Проверяет, поместится л�� шаблон на карте
   */
  public static canFitPattern(
    worldMap: number[][],
    pattern: DecorationPattern,
    centerX: number,
    centerY: number
  ): boolean {
    const mapHeight = worldMap.length;
    const mapWidth = mapHeight > 0 ? worldMap[0].length : 0;
    
    const matrix = pattern.matrix;
    const patternHeight = matrix.length;
    const patternWidth = matrix[0].length;
    
    // Используем центр шаблона
    const patternCenterX = Math.floor(patternWidth / 2);
    const patternCenterY = Math.floor(patternHeight / 2);
    
    const offsetX = centerX - patternCenterX;
    const offsetY = centerY - patternCenterY;
    
    // Проверяем, что весь шаблон помещается в границы карты
    return (
      offsetX >= 0 &&
      offsetY >= 0 &&
      offsetX + patternWidth <= mapWidth &&
      offsetY + patternHeight <= mapHeight
    );
  }
}