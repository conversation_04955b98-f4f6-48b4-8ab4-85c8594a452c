import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { GenerationProgress } from '../utils/asyncUtils';

export interface ProgressSession {
  sessionId: string;
  socketId: string;
  userId?: string;
  worldName?: string;
  startTime: Date;
}

@WebSocketGateway({
  cors: {
    origin: '*', // В продакшене указать конкретные домены
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/progress',
})
export class ProgressGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: any; // Используем any вместо типизированного Server

  private readonly logger = new Logger(ProgressGateway.name);
  private sessions = new Map<string, ProgressSession>(); // sessionId -> session
  private socketToSession = new Map<string, string>(); // socketId -> sessionId

  handleConnection(client: any) { // Используем any вместо Socket
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: any) { // Используем any вместо Socket
    this.logger.log(`Client disconnected: ${client.id}`);
    
    // Удаляем сессию при отключении
    const sessionId = this.socketToSession.get(client.id);
    if (sessionId) {
      this.sessions.delete(sessionId);
      this.socketToSession.delete(client.id);
      this.logger.log(`Session ${sessionId} removed due to disconnect`);
    }
  }

  @SubscribeMessage('subscribe-progress')
  handleSubscribeProgress(
    @MessageBody() data: { sessionId: string; userId?: string; worldName?: string },
    @ConnectedSocket() client: any,
  ) {
    const { sessionId, userId, worldName } = data;
    
    this.logger.log(`Client ${client.id} subscribed to progress for session ${sessionId}`);

    // Создаем или обновляем сессию
    const session: ProgressSession = {
      sessionId,
      socketId: client.id,
      userId,
      worldName,
      startTime: new Date(),
    };

    this.sessions.set(sessionId, session);
    this.socketToSession.set(client.id, sessionId);

    // Подтверждаем подписку
    client.emit('progress-subscribed', {
      sessionId,
      message: 'Successfully subscribed to progress updates',
    });

    return { success: true, sessionId };
  }

  @SubscribeMessage('unsubscribe-progress')
  handleUnsubscribeProgress(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: any,
  ) {
    const { sessionId } = data;
    
    this.logger.log(`Client ${client.id} unsubscribed from session ${sessionId}`);

    this.sessions.delete(sessionId);
    this.socketToSession.delete(client.id);

    client.emit('progress-unsubscribed', { sessionId });
    return { success: true };
  }

  @SubscribeMessage('cancel-generation')
  handleCancelGeneration(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: any,
  ) {
    const { sessionId } = data;
    
    this.logger.log(`Cancellation requested for session ${sessionId}`);

    // Отправляем событие отмены (будет обработано в AppService)
    this.server.to('/progress').emit('generation-cancelled', { sessionId });
    
    return { success: true, message: 'Cancellation request sent' };
  }

  /**
   * Отправляет обновление прогресса конкретной сессии
   */
  sendProgressUpdate(sessionId: string, progress: GenerationProgress) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.logger.warn(`Session ${sessionId} not found for progress update`);
      return;
    }

    // Отправляем прогресс конкретному клиенту
    this.server.to(session.socketId).emit('progress-update', {
      sessionId,
      progress,
      timestamp: new Date().toISOString(),
    });

    this.logger.debug(`Progress sent to session ${sessionId}: ${progress.stage} - ${progress.progress}%`);
  }

  /**
   * Отправляет уведомление о завершении генерации
   */
  sendGenerationComplete(sessionId: string, result: { success: boolean; worldId?: string; error?: string }) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.logger.warn(`Session ${sessionId} not found for completion notification`);
      return;
    }

    const duration = new Date().getTime() - session.startTime.getTime();

    this.server.to(session.socketId).emit('generation-complete', {
      sessionId,
      result,
      duration,
      timestamp: new Date().toISOString(),
    });

    this.logger.log(`Generation completed for session ${sessionId} in ${duration}ms`);

    // Удаляем сессию после завершения
    this.sessions.delete(sessionId);
    this.socketToSession.delete(session.socketId);
  }

  /**
   * Отправляет уведомление об ошибке
   */
  sendGenerationError(sessionId: string, error: string) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.logger.warn(`Session ${sessionId} not found for error notification`);
      return;
    }

    this.server.to(session.socketId).emit('generation-error', {
      sessionId,
      error,
      timestamp: new Date().toISOString(),
    });

    this.logger.error(`Generation error for session ${sessionId}: ${error}`);
  }

  /**
   * Получает информацию о всех активных сессиях (для отладки)
   */
  getActiveSessions(): ProgressSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Проверяет, активна ли сессия
   */
  isSessionActive(sessionId: string): boolean {
    return this.sessions.has(sessionId);
  }
}