.worldList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.createButton {
  display: flex;
  justify-content: center;
}

.createWorldButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: scale(1);
}

.createWorldButton:hover {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  transform: scale(1.05);
}

.createWorldButton svg {
  width: 1.25rem;
  height: 1.25rem;
}

.createForm {
  background-color: rgba(31, 41, 55, 0.5);
  backdrop-filter: blur(8px);
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid #4b5563;
}

.formTitle {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formField {
  display: flex;
  flex-direction: column;
}

.formLabel {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #d1d5db;
  margin-bottom: 0.5rem;
}

.formInput {
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #374151;
  border: 1px solid #4b5563;
  border-radius: 0.5rem;
  color: white;
  font-family: inherit;
}

.formInput::placeholder {
  color: #9ca3af;
}

.formInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.formTextarea {
  resize: none;
  min-height: 4.5rem;
}

.formSection {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 1.5rem;
}

.formSection:last-child {
  border-bottom: none;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.difficultyGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.difficultyButton {
  padding: 0.5rem;
  border: 1px solid #4b5563;
  border-radius: 0.5rem;
  background-color: #374151;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.difficultyButtonActive {
  background-color: #2563eb;
  border-color: #3b82f6;
}

.difficultyButtonInactive {
  background-color: #374151;
}

.difficultyButtonInactive:hover {
  background-color: #4b5563;
}

.difficultyDescription {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  font-style: italic;
}

.settingDescription {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  font-style: italic;
}

.slider {
  width: 100%;
  height: 0.5rem;
  appearance: none;
  background: #4b5563;
  border-radius: 0.25rem;
  margin: 1rem 0;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

.rangeLabels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

.inputIcon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

.formButtons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.formButton {
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.greenButton {
  background-color: #2ecc71;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.greenButton:hover:not(:disabled) {
  background-color: #27ae60;
}

.greenButton:disabled {
  background-color: #4b5563;
  cursor: not-allowed;
}

.cancelButton {
  background-color: #4b5563;
  color: white;
}

.cancelButton:hover {
  background-color: #374151;
}

.emptyState {
  text-align: center;
  padding: 3rem 0;
}

.emptyIcon {
  font-size: 3.75rem;
  margin-bottom: 1rem;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

.emptyDescription {
  color: #9ca3af;
}

.worldsGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.worldCard {
  background-color: rgba(31, 41, 55, 0.7);
  backdrop-filter: blur(8px);
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid #4b5563;
  transition: border-color 0.3s ease;
}

.worldCard:hover {
  border-color: #6b7280;
}

.worldCardContent {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.worldInfo {
  flex: 1;
}

.worldName {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

.worldDescription {
  color: #d1d5db;
  margin-bottom: 0.75rem;
}

.worldMeta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 0.875rem;
  color: #9ca3af;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metaItem svg {
  width: 1rem;
  height: 1rem;
}

.worldActions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.playButton {
  background-color: #16a34a;
  color: white;
}

.playButton:hover {
  background-color: #15803d;
}

.deleteButton {
  background-color: #dc2626;
  color: white;
  padding: 0.5rem;
}

.deleteButton:hover {
  background-color: #b91c1c;
}

.actionButton svg {
  width: 1rem;
  height: 1rem;
}

/* Error message */
.errorMessage {
  background-color: rgba(220, 38, 38, 0.1);
  border: 1px solid #dc2626;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #fca5a5;
  font-size: 0.875rem;
  margin-top: 1rem;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .worldCardContent {
    flex-direction: column;
    gap: 1rem;
  }
  
  .worldActions {
    margin-left: 0;
    align-self: stretch;
  }
  
  .worldMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
