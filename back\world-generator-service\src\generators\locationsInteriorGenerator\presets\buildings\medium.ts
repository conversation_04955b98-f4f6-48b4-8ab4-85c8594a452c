import { PresetLocationMap } from "../presetType";


export const locationConfigs: Record<string, PresetLocationMap > = {

'mediumBuilding_1': {
  name: 'bigBuilding_1',
  width: 20,
  height: 20,
  tokenMap: 
  [
    ["0", "0", "0", "0", "0", "r", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", ",", ",", "0", "0", "0", "0", "0", "0", "0", "0", "T", "0"],
    ["0", "T", "T", ",", "T", "0", "0", "0", "0", "0", "0", "0", "T", ",", "0", "0", "r", "0", "0", "0"],
    ["0", "0", ",", "0", "0", "0", "T", "T", "0", "r", "0", "0", "0", "0", "0", "0", "0", "0", ",", "0"],
    ["0", "0", "r", "0", "0", "0", "0", ",", ",", "0", "0", ",", "0", "0", "r", ",", "0", "0", "0", "0"],
    ["0", "0", "0", "0", "i", "0", "r", "0", "0", "T", "0", "0", "T", "0", "0", "0", "0", "T", "0", "0"],
    ["0", "0", ",", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", ",", "0", "0", "0"],
    ["0", "0", "0", "0", "0", "w", "w", "w", "w", "w", "w", "w", "w", "w", "w", "0", "0", "0", "T", ","],
    ["0", "0", ",", "0", "r", "w", "o", "s", "w", "0", "f", "C", "C", "C", "w", "0", "0", "0", "0", ","],
    ["0", "0", "0", "0", "r", "w", "L", "x", "D", "0", "0", "0", "0", "0", "w", "i", "0", "r", "0", "0"],
    ["0", "T", "0", "0", "0", "w", "w", "w", "w", "0", "0", "t", "t", "t", "w", "i", "0", "0", "0", "0"],
    ["0", "T", "T", "r", "0", "w", "V", "0", "x", "L", "0", "0", "0", "0", "v", "0", "0", ",", "0", "T"],
    ["0", ",", "0", "r", "0", "v", "0", "0", "0", "0", "0", "x", "L", "0", "v", "0", "0", ",", "T", "0"],
    ["0", "0", "0", "0", "0", "w", "C", "0", "0", "0", "0", "0", "0", "U", "w", "0", "0", ",", "0", "0"],
    ["0", "i", "0", "T", "0", "w", "w", "v", "w", "D", "w", "w", "v", "w", "w", "0", "0", "0", "0", "0"],
    ["0", "0", "0", "T", ",", "0", "0", "0", "0", "0", "0", "0", "0", "R", "r", "0", "0", "0", "0", "r"],
    ["0", ",", "0", "0", "0", "0", "0", "0", ",", "0", "0", "0", "0", "0", "0", "0", "0", "0", "r", "r"],
    ["0", "0", "r", "r", "r", "0", "0", "0", "0", "0", "0", ",", "0", "0", "r", "T", "T", ",", "0", "0"],
    ["0", "0", "0", "0", "0", "0", "0", "0", "T", "T", "0", ",", "0", "r", "r", "T", "0", "0", "R", "0"],
    ["0", "0", ",", ",", ",", "F", "F", "0", "0", "T", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]
  ],
  anchor: { x: 15, y: 15  }, //смещение относительно центра локации  в любую сторону
  rotations: false,
  mirror: true,
  weight: 1,



}
}