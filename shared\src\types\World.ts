
import { Location, TransferLocation } from './Location';
import { Player, Position } from '../models/Player';
import { Language, TerrainType, WorldMapDecorations } from '../enums';

export interface WorldSettings {
  seed: string; 
  language: Language;
  worldSize: number //
  difficulty: 'easy' | 'normal' | 'hard';
  autosave: 'everytime' | 'when_rest' | 'on_exit' |  'forbidden'; // 'forbidden' - 1 жизнь
  timeScale: number;// where 0.1 = 9 days in month and 1 = 1 day in month
  showPath: boolean;
  
  // Audio settings
  volume: number; // 0-100
  backgroundMusic: number; // 0-100
  soundEffects: number; // 0-100
  
  // Display settings
  brightness: number; // 0-100
  contrast: number; // 0-100
  terminalTextSize: number;
  worldMapPlayerSpeed: number;

}

export interface WorldSummary {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  lastPlayedAt?: string;
  totalPlaytime?: number;
}

export interface WorldWeatherState {
  currentTime: {
    day: number;
    hour: number;
    minute: number;
    season: 'spring' | 'summer' | 'autumn' | 'winter';
  };

  weather: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    visibility: number;
    radiationStorm: boolean;
  };

  activeEvents: string[];
}

export interface WorldMap {
  id: string;
  userId: string;
  name: string;
  parameters: WorldWeatherState;
  description: string;
  settings: WorldSettings;
  worldMap: Record<string, WorldMapCell>;
  player?: Player;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorldMapCell {
  pos: Position;
  blocked: boolean;
  terrarianMarker: number;
  terrain: TerrainType;
  decorationBorder?: number[]; // [1] = север, [2] = юг, [3] = запад, [4] = восток, [5] = все стороны. Примеры: [1,3] = север+запад, [2,3,4] = юг+запад+восток, [5] = все стороны
  decoration: WorldMapDecorations;
  height: number; // 0 - water, 1 - ground, 2 - low hill, 3 - high hill,/small building, 4 - mountain/building
  location?: TransferLocation;
  fogOfWar: boolean;
  imgDirection: number;
  locationNear: boolean;
  LVLZone: number; // 1 = 0 - 5 | 2 = 5 - 10 | 3 = 10 - 15 | 4 = 15+
}