
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  width: 450px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.7);
  position: relative;
}

.header {
  background: var(--bg-overlay);
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  color: var(--primary-color);
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 10px var(--shadow-primary);
}

.closeButton {
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: 16px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: var(--primary-dark);
  color: var(--text-primary);
  box-shadow: 0 0 10px var(--shadow-primary);
}

.content {
  padding: 20px;
  color: var(--text-primary);
}

.timeInfo {
  margin-bottom: 25px;
  background: var(--bg-overlay);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.currentTime,
.targetTime {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.targetTime {
  margin-bottom: 0;
}

.label {
  color: var(--text-secondary);
  font-size: 14px;
}

.time {
  color: var(--primary-color);
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 5px var(--shadow-primary);
}

.controls {
  margin-bottom: 20px;
}

.sliderContainer {
  margin-bottom: 20px;
}

.sliderLabel {
  display: block;
  color: var(--text-primary);
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  font-size: 16px;
}

.slider {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px var(--shadow-primary);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px var(--shadow-primary);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: scale(1.1);
}

.sliderMarks {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: var(--text-muted);
}

.quickButtons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.quickButton {
  background: rgba(33, 66, 5, 0.1);
  border: 1px solid var(--border-light);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.quickButton:hover {
  background: var(--primary-dark);
  border-color: var(--primary-color);
  box-shadow: 0 0 10px var(--shadow-primary);
}

.quickButton.active {
  background: var(--primary-color);
  color: var(--text-primary);
  border-color: var(--primary-color);
  font-weight: bold;
  box-shadow: 0 0 15px var(--shadow-primary);
}

.quickButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.footer {
  background: var(--bg-overlay);
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.waitButton {
  background: var(--primary-color);
  border: none;
  color: var(--text-primary);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  font-size: 14px;
}

.waitButton:hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: 0 0 15px var(--shadow-primary);
  transform: translateY(-1px);
}

.waitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancelButton {
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  font-size: 14px;
}

.cancelButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--text-muted);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.waitingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.waitingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
  box-shadow: 0 0 20px var(--shadow-primary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.waitingText {
  color: var(--text-primary);
  font-size: 16px;
  margin: 0;
  text-align: center;
}

/* Адаптивность */
@media (max-width: 768px) {
  .modal {
    width: 90%;
    max-width: 400px;
  }
  
  .quickButtons {
    grid-template-columns: 1fr;
  }
  
  .footer {
    flex-direction: column;
  }
  
  .waitButton,
  .cancelButton {
    width: 100%;
  }
}
