import { GameStats } from '../types';

export class ValidationUtils {
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
    return usernameRegex.test(username);
  }

  static isValidPassword(password: string): boolean {
    return password.length >= 6;
  }
}

export class GameUtils {
  static clampStat(value: number, min: number = 0, max: number = 100): number {
    return Math.max(min, Math.min(max, value));
  }

  static applyStatChanges(current: GameStats, changes: Partial<GameStats>): GameStats {
    return {
      health: this.clampStat((current.health || 100) + (changes.health || 0)),
      maxHealth: current.maxHealth || 100,
      energy: this.clampStat((current.energy || 100) + (changes.energy || 0)),
      maxEnergy: current.maxEnergy || 100,
      hunger: this.clampStat((current.hunger || 100) + (changes.hunger || 0)),
      thirst: this.clampStat((current.thirst || 100) + (changes.thirst || 0)),
      radiation: this.clampStat((current.radiation || 0) + (changes.radiation || 0)),
      sanity: current.sanity !== undefined ? 
        this.clampStat((current.sanity || 100) + (changes.sanity || 0)) : 
        undefined,
    };
  }

  static calculateSurvivalScore(stats: GameStats): number {
    const weights = {
      health: 0.3,
      hunger: 0.2,
      thirst: 0.2,
      energy: 0.15,
      radiation: -0.15, // negative because radiation is bad
    };

    return Math.round(
      (stats.health * weights.health) +
      (stats.hunger * weights.hunger) +
      (stats.thirst * weights.thirst) +
      (stats.energy * weights.energy) +
      (stats.radiation * weights.radiation)
    );
  }

  static isPlayerInDanger(stats: GameStats): boolean {
    return stats.health <= 20 || 
           stats.hunger <= 10 || 
           stats.thirst <= 10 || 
           stats.radiation >= 80;
  }
}

export class DateUtils {
  static formatPlaytime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  }

  static getTimeSince(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    }
    if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    }
    if (diffMins > 0) {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    }
    return 'Just now';
  }
}

export class Constants {
  static readonly DEFAULT_GAME_STATS: GameStats = {
    health: 100,
    maxHealth: 100,
    energy: 100,
    maxEnergy: 100,
    hunger: 100,
    thirst: 100,
    radiation: 0,
  };

  static readonly MAX_INVENTORY_SLOTS = 50;
  static readonly MAX_SAVE_SLOTS = 10;
  static readonly DEFAULT_LOCATION = 'bunker';
  
  static readonly STAT_DECAY_RATES = {
    hunger: 1, // per minute
    thirst: 1.5, // per minute
    energy: 0.5, // per minute
  };

  static readonly CRITICAL_THRESHOLDS = {
    health: 20,
    hunger: 10,
    thirst: 10,
    radiation: 80,
    energy: 10,
  };
}

// Export new utility modules
export * from './diceRoll'
export * from './generateId'
// export * from './applyEffects'  // Временно отключаем
