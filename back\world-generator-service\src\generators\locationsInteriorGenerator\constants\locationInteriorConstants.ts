// Константы для генерации интерьеров локаций

// Отступы от краев карты для outdoor локаций
export const OUTDOOR_SPAWN_MARGIN = 6; // отступ от края для спавна игрока
export const OUTDOOR_GOBACK_ZONE_WIDTH = 3; // ширина зоны выхода от края карты

// Количество декораций для outdoor локаций (в процентах от общего количества клеток)
export const NATURE_DECORATIONS = {
  GRASS: { min: 0.05, max: 0.17 }, 
  BUSH: { min: 0.02, max: 0.06 },  
  TREE: { min: 0.01, max: 0.04 },  
  ROCKS: { min: 0.03, max: 0.06 }, 
  PUDDLE: { min: 0.005, max: 0.01 }, 
  LOG: { min: 0.002, max: 0.01 },
  MUD: { min: 0.001, max: 0.005 }   
};

export const DEBRIS_DECORATIONS = {
  LITTER: { min: 0.01, max: 0.03 }, 
  TIRE: { min: 0.005, max: 0.01 }   
};

// Настройки для indoor/underground локаций
export const INDOOR_DOORS_COUNT = 2; // количество дверей на случайных сторонах
