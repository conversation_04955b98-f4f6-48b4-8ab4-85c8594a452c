@echo off
chcp 1251 > nul
echo ========================================
echo Installing npm packages for all modules
echo ========================================

echo.
echo [1/7] Installing shared module...
cd /d "%~dp0..\shared"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install shared module
    pause
    exit /b 1
)

echo.
echo [2/7] Copying shared module source files to all services...
REM Создаем директории для бэкенда
mkdir "%~dp0..\back\auth-service\src\shared" 2>nul
xcopy /E /Y /Q "%~dp0..\shared\src" "%~dp0..\back\auth-service\src\shared"

mkdir "%~dp0..\back\game-engine-service\src\shared" 2>nul
xcopy /E /Y /Q "%~dp0..\shared\src" "%~dp0..\back\game-engine-service\src\shared"

mkdir "%~dp0..\back\save-service\src\shared" 2>nul
xcopy /E /Y /Q "%~dp0..\shared\src" "%~dp0..\back\save-service\src\shared"

mkdir "%~dp0..\back\world-generator-service\src\shared" 2>nul
xcopy /E /Y /Q "%~dp0..\shared\src" "%~dp0..\back\world-generator-service\src\shared"

mkdir "%~dp0..\front\src\shared" 2>nul
xcopy /E /Y /Q "%~dp0..\shared\src" "%~dp0..\front\src\shared"

echo.
echo [3/8] Installing world-generator-service...
cd /d "%~dp0..\back\world-generator-service"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install world-generator-service
    pause
    exit /b 1
)

echo.
echo [4/8] Installing save-service...
cd /d "%~dp0..\back\save-service"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install save-service
    pause
    exit /b 1
)

echo.
echo [5/8] Installing game-engine-service...
cd /d "%~dp0..\back\game-engine-service"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install game-engine-service
    pause
    exit /b 1
)

echo.
echo [6/8] Installing auth-service...
cd /d "%~dp0..\back\auth-service"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install auth-service
    pause
    exit /b 1
)

echo.
echo [8/8] Installing frontend...
cd /d "%~dp0..\front"
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend
    pause
    exit /b 1
)

echo.
echo ========================================
echo All modules installed successfully!
echo ========================================
pause
