import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

interface GenerationProgress {
  stage: string;
  progress: number;
  currentOperation: string;
  estimatedTimeRemaining?: number;
}

interface GenerationResult {
  success: boolean;
  worldId?: string;
  error?: string;
}

interface UseWorldGenerationReturn {
  isGenerating: boolean;
  progress: GenerationProgress | null;
  startGeneration: (worldData: any) => Promise<void>;
  cancelGeneration: () => void;
  error: string | null;
}

const WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL || 'http://localhost:3003';
const API_URL = import.meta.env.VITE_WORLD_GENERATOR_SERVICE_URL || 'http://localhost:3003/api';

export const useWorldGeneration = (): UseWorldGenerationReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState<GenerationProgress | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const onCompleteRef = useRef<((result: GenerationResult) => void) | null>(null);

  // Инициализация WebSocket
  useEffect(() => {
    const newSocket = io(`${WEBSOCKET_URL}/progress`, {
      transports: ['websocket'],
      autoConnect: false
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  // Настройка обработчиков WebSocket событий
  useEffect(() => {
    if (!socket) return;

    const handleProgressSubscribed = (data: any) => {
    };

    const handleProgressUpdate = (data: { progress: GenerationProgress }) => {
      setProgress(data.progress);
    };

    const handleGenerationComplete = (data: { result: GenerationResult }) => {
      setIsGenerating(false);
      setProgress(null);
      setSessionId(null);
      
      if (onCompleteRef.current) {
        onCompleteRef.current(data.result);
      }
    };

    const handleGenerationError = (data: { error: string }) => {
      setError(data.error);
      setIsGenerating(false);
      setProgress(null);
      setSessionId(null);
      
      if (onCompleteRef.current) {
        onCompleteRef.current({ success: false, error: data.error });
      }
    };

    const handleConnect = () => {
    };

    const handleDisconnect = () => {
    };

    const handleConnectError = (error: any) => {
      setError('Ошибка подключения к серверу');
    };

    socket.on('progress-subscribed', handleProgressSubscribed);
    socket.on('progress-update', handleProgressUpdate);
    socket.on('generation-complete', handleGenerationComplete);
    socket.on('generation-error', handleGenerationError);
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connect_error', handleConnectError);

    return () => {
      socket.off('progress-subscribed', handleProgressSubscribed);
      socket.off('progress-update', handleProgressUpdate);
      socket.off('generation-complete', handleGenerationComplete);
      socket.off('generation-error', handleGenerationError);
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connect_error', handleConnectError);
    };
  }, [socket]);

  const startGeneration = useCallback(async (worldData: any): Promise<void> => {
    if (!socket) {
      throw new Error('WebSocket не инициализирован');
    }

    return new Promise((resolve, reject) => {
      const generateWorld = async () => {
        try {
          setIsGenerating(true);
          setError(null);
          setProgress(null);

          const url = `${API_URL}/generate-world-with-progress`;

          // Запускаем генерацию с прогрессом
          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-User-Id': worldData.userId
            },
            body: JSON.stringify(worldData)
          });


          if (!response.ok) {
            let errorText = '';
            try {
              errorText = await response.text();
            } catch (e) {
              errorText = 'Не удалось получить текст ошибки';
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
          }

          let responseData;
          try {
            responseData = await response.json();
          } catch (e) {
            throw new Error('Ошибка парсинга ответа сервера');
          }
          
          const { sessionId: newSessionId } = responseData;
          if (!newSessionId) {
            throw new Error('Сервер не вернул sessionId');
          }
          
          setSessionId(newSessionId);

          // Устанавливаем callback для завершения
          onCompleteRef.current = (result: GenerationResult) => {
            if (result.success) {
              resolve();
            } else {
              reject(new Error(result.error || 'Генерация не удалась'));
            }
          };

          // Поключаемся к WebSocket
          socket.connect();

          // Подписываемся на прогресс
          socket.emit('subscribe-progress', {
            sessionId: newSessionId,
            userId: worldData.userId,
            worldName: worldData.name
          });

        } catch (error) {
          setIsGenerating(false);
          setError(error instanceof Error ? error.message : 'Неизвестная ошибка');
          reject(error);
        }
      };

      generateWorld();
    });
  }, [socket]);

  const cancelGeneration = useCallback(() => {
    if (sessionId && socket) {
      socket.emit('cancel-generation', { sessionId });
      
      // Сбрасываем состояние
      setIsGenerating(false);
      setProgress(null);
      setSessionId(null);
      setError(null);
    }
  }, [sessionId, socket]);

  return {
    isGenerating,
    progress,
    startGeneration,
    cancelGeneration,
    error
  };
};