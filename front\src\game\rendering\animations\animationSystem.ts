import { WorldMap } from '@/shared';
import { getTileCenterOnScreen, isoToScreen } from '../../utils/coordinates/isometric';
import { drawEquippedTextures } from './characterRenderer';
import { getCurrentPath, convertSpeedToMs } from '../../systems/movement/playerMovement';

import { useGameStore } from '@/game/store/gameStore'
import { LOCATION_SPEED_MULTIPLIER, WORLD_MAP_SPEED_MULTIPLIER } from '../../utils/constants/movement';
import animationsData from './skeletonsMap/animations.json';
import { DECORATION_TEXTURE_SETTINGS, WORLD_MAP_TERRAIN_TEXTURE_SETTINGS, getPlayerTextureSize } from '../../utils/constants/rendering';

// Расширяем Window для хранения скелета игрока
declare global {
  interface Window {
    __playerSkeleton?: AnimationPlayer;
    __playerSkeletonLastTime?: number;
    __playerDirection?: 'north' | 'south' | 'east' | 'west' | 'idle';
    __playerLastPosition?: { x: number; y: number };
    __playerMoveStartTime?: number;
    __playerMoveDirection?: { dx: number; dy: number };
    __playerSkewYCorrection?: number;
  }
}

// Менеджер проигрывания анимаций
export class AnimationPlayer {
  private skeleton: Skeleton;
  private animationName: string;
  private frameIndex: number = 0;
  private frameTime: number = 0;
  private frameDuration: number;
  private playing: boolean = false;

  constructor(
    base: Skeleton,
    animationName: string = 'idle',
    frameDuration: number = 400 // мс на кадр
  ) {
    this.skeleton = cloneSkeleton(base);
    this.animationName = animationName;
    this.frameDuration = frameDuration;
    this.applyFrame();
  }

  public getSkeleton(): Skeleton {
    return this.skeleton;
  }

  public getAnimationName(): string {
    return this.animationName;
  }

  public getFrameIndex(): number {
    return this.frameIndex;
  }

  public play() {
    this.playing = true;
  }

  public pause() {
    this.playing = false;
  }

  public setAnimation(name: string) {
    if (animations[name]) {
      this.animationName = name;
      this.frameIndex = 0;
      this.applyFrame();
    }
  }

  public setFrameDuration(duration: number) {
    this.frameDuration = duration;
  }

  public update(delta: number) {
    if (!this.playing) return;
    this.frameTime += delta;
    const frames = animations[this.animationName];
    if (!frames) return;
    if (this.frameTime >= this.frameDuration) {
      this.frameTime = 0;
      this.frameIndex = (this.frameIndex + 1) % frames.length;
      this.applyFrame();
    }
  }

  private applyFrame() {
    const frames = animations[this.animationName];
    if (!frames) return;
    applyAnimationFrame(this.skeleton, frames[this.frameIndex]);
  }
}

/**
 * Скелетная анимация персонажа для IMComp
 * Основа: структура костей, анимации, отрисовка и управление
 */

// Типы данных
export interface Bone {
  parent: string | null;
  x: number;
  y: number;
  rotation: number;
  length: number;
  width: number;
}

export type Skeleton = { [boneName: string]: Bone };

export type AnimationFrame = { [boneName: string]: Partial<Bone> };

export type Animation = AnimationFrame[];

// Базовая структура скелета (позиция покоя)
// rotation - в радианах 90 градусов = 1.5708
export const baseSkeleton: Skeleton = {
  torso: { parent: null, x: 0, y: -140, rotation: 0, length: 70, width: 44 },
  head: { parent: 'torso', x: 0, y: -21, rotation: 0, length: 25, width: 23 },
  // РУКИ - симметричные значения
  leftShoulder: { parent: 'torso', x: -28, y: 5, rotation: 0, length: 36, width: 18 },
  leftForearm: { parent: 'leftShoulder', x: -3, y: 35, rotation: 0, length: 30, width: 13 },
  leftHand: { parent: 'leftForearm', x: 0, y: 30, rotation: 0, length: 15, width: 14 },
  rightShoulder: { parent: 'torso', x: 28, y: 5, rotation: 0, length: 36, width: 18 },
  rightForearm: { parent: 'rightShoulder', x: 3, y: 35, rotation: 0, length: 30, width: 13 },
  rightHand: { parent: 'rightForearm', x: 0, y: 30, rotation: 0, length: 15, width: 14 },
  // НОГИ - симметричные значения
  leftThigh: { parent: 'torso', x: -10, y: 65, rotation: 0.11, length: 40, width: 21 },
  leftShin: { parent: 'leftThigh', x: 4, y: 40, rotation: -0.11, length: 36, width: 14 },
  leftFoot: { parent: 'leftShin', x: -3, y: 35, rotation: 0, length: 12, width: 22 },
  rightThigh: { parent: 'torso', x: 10, y: 65, rotation: -0.11, length: 40, width: 21 },
  rightShin: { parent: 'rightThigh', x: -4, y: 40, rotation: 0.11, length: 36, width: 14 },
  rightFoot: { parent: 'rightShin', x: 3, y: 35, rotation: 0, length: 12, width: 22 },
  weapon: { parent: 'rightHand', x: 0, y: -20, rotation: 0, length: 40, width: 4 },
};

// Анимации - массив кадров с изменениями углов поворота
const animations: Record<string, AnimationFrame[]> = animationsData as Record<string, AnimationFrame[]>;

// Создать копию скелета
export function cloneSkeleton(skeleton: Skeleton): Skeleton {
  const copy: Skeleton = {};
  for (const boneName in skeleton) {
    copy[boneName] = { ...skeleton[boneName] };
  }
  return copy;
}

// Применить кадр анимации к скелету
export function applyAnimationFrame(skeleton: Skeleton, frame: AnimationFrame) {
  // Сначала сбрасываем все кости к базовому состоянию
  for (let boneName in skeleton) {
    Object.assign(skeleton[boneName], baseSkeleton[boneName]);
  }
  
  // Затем применяем изменения из кадра анимации
  for (let boneName in frame) {
    if (skeleton[boneName]) {
      // Сохраняем parent перед применением изменений
      const originalParent = skeleton[boneName].parent;
      Object.assign(skeleton[boneName], frame[boneName]);
      
      // Если в кадре анимации не указан parent, восстанавливаем оригинальный
      if (frame[boneName].parent === undefined) {
        skeleton[boneName].parent = originalParent;
      }
    }
  }
}

// Рассчитать мировую позицию кости с учётом родительских трансформаций
export interface WorldTransform {
  x: number;
  y: number;
  rotation: number;
}

export function calculateWorldTransform(
  skeleton: Skeleton,
  boneName: string,
  transforms: { [boneName: string]: WorldTransform } = {}
): WorldTransform {
  const bone = skeleton[boneName];
  if (!bone) return { x: 0, y: 0, rotation: 0 };
  if (transforms[boneName]) {
    return transforms[boneName];
  }
  let worldTransform: WorldTransform = { x: bone.x, y: bone.y, rotation: bone.rotation };
  if (bone.parent) {
    const parentBone = skeleton[bone.parent];
    const parentTransform = calculateWorldTransform(skeleton, bone.parent, transforms);
    
    // Вычисляем позицию дочерней кости относительно родительской
    let localX = bone.x;
    let localY = bone.y;
    
    // Если дочерняя кость должна быть привязана к концу родительской кости
    // (определяем это по тому, что Y координата больше половины длины родительской кости)
    if (parentBone && Math.abs(bone.y) > parentBone.length * 0.5) {
      // Корректируем Y координату с учетом изменения длины родительской кости
      const baseBone = baseSkeleton[boneName];
      const baseParentBone = baseSkeleton[bone.parent];
      
      if (baseBone && baseParentBone) {
        // Вычисляем пропорциональное смещение
        const lengthRatio = parentBone.length / baseParentBone.length;
        localY = baseBone.y * lengthRatio;
      }
    }
    
    const cos = Math.cos(parentTransform.rotation);
    const sin = Math.sin(parentTransform.rotation);
    const rotatedX = localX * cos - localY * sin;
    const rotatedY = localX * sin + localY * cos;
    worldTransform.x = parentTransform.x + rotatedX;
    worldTransform.y = parentTransform.y + rotatedY;
    worldTransform.rotation = parentTransform.rotation + bone.rotation;
  }
  transforms[boneName] = worldTransform;
  return worldTransform;
}

// Отрисовка кости
export function drawBone(ctx: CanvasRenderingContext2D, transform: WorldTransform, bone: Bone, boneName: string, debugMode: boolean = false) {
  ctx.save();
  ctx.translate(transform.x, transform.y);
  ctx.rotate(transform.rotation);
  let color = ' #ffd3c000';
  
  ctx.fillStyle = color;
  ctx.fillRect(-bone.width / 2, -5, bone.width, bone.length);
  if (debugMode) {
    ctx.fillStyle = 'red';
    ctx.fillRect(-2, -8, 4, 4);
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.fillText(boneName, 5, -5);
  }
  ctx.restore();
}

// Главная функция отрисовки скелета
export function drawSkeleton(
  ctx: CanvasRenderingContext2D,
  skeleton: Skeleton,
  animationName: string,
  animationFrame: number,
  debugMode: boolean = false
) {
  // НЕ очищаем канвас!
  const transforms: { [boneName: string]: WorldTransform } = {};
  // Получаем текущий кадр анимации
  let frameDrawOrder: string[] | undefined;
  const frames = (animations as Record<string, AnimationFrame[]>)[animationName];
  if (frames && frames[animationFrame] && Array.isArray((frames[animationFrame] as any).drawOrder)) {
    frameDrawOrder = (frames[animationFrame] as any).drawOrder;
  }
  const defaultDrawOrder = [
    'torso',
    'head',
    'leftThigh',
    'leftShin',
    'leftFoot',
    'rightThigh',
    'rightShin',
    'rightFoot',
    'leftShoulder',
    'leftForearm',
    'leftHand',
    'rightShoulder',
    'rightForearm',
    'rightHand',
    'weapon',
  ];
  const drawOrder = frameDrawOrder || defaultDrawOrder;
  for (let boneName of drawOrder) {
    if (skeleton[boneName]) {
      const transform = calculateWorldTransform(skeleton, boneName, transforms);
      drawBone(ctx, transform, skeleton[boneName], boneName, debugMode);
    }
  }
  
  // Отрисовываем текстуры одежды поверх скелета
  const equippedArmor = 'underwear'; // Используем underwear как есть в папке
  // Если в названии анимации есть north - используем back, если south - front
  const playerView = animationName.includes('north') ? 'back' : 'front';
  
  if (equippedArmor) {
    drawEquippedTextures({
      ctx,
      skeleton,
      transforms,
      equippedArmor,
      view: playerView,
      zoom: 1,
      drawOrder: frameDrawOrder, // Передаем drawOrder
      animationName, // Передаем название анимации
    });
  }
  
  if (debugMode) {
    ctx.fillStyle = 'yellow';
    ctx.font = '14px Arial';
    ctx.fillText(`Анимация: ${animationName}`, 10, 30);
    ctx.fillText(`Кадр: ${animationFrame}`, 10, 50);
  }
}

/**
 * Отдельная функция для рендера скелета игрока поверх всех тайлов
 * Вызывать после drawTile для всех тайлов!
 */
export function drawPlayerSkeleton(
  ctx: CanvasRenderingContext2D,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  currentWorld: WorldMap | null,
  animationName: string = 'walk_south_east',
  playerPath?: { x: number; y: number }[]
) {
  if (!currentWorld?.player?.position) return;
  const { x: isoX, y: isoY } = currentWorld.player.position;
  // Конвертируем изометрические координаты в экранные
  const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
  // Получаем центр тайла на экране с уетом камеры
  let { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // СМЕЩЕНИЕ СКЕЛЕТА - отдельно от анимации, привязано к времени движения
  let offsetX = 0, offsetY = 0;
  
  if (window.__playerIsMoving) {
    const path = getCurrentPath();
    const currentPlayerPos = currentWorld.player.position;
    
    // Проверяем, изменилась ли позиция игрока (новый шаг)
    if (!window.__playerLastPosition || 
        window.__playerLastPosition.x !== currentPlayerPos.x || 
        window.__playerLastPosition.y !== currentPlayerPos.y) {
      
      // НОВЫЙ ШАГ - обновляем данные движения
      window.__playerLastPosition = { ...currentPlayerPos };
      window.__playerMoveStartTime = performance.now();
      
      // Находим следующую точку в пути ПРАВИЛЬНО
      let nextPoint = null;
      // Сначала найдем индекс текущей позиции в пути
      let currentIndex = -1;
      for (let i = 0; i < path.length; i++) {
        if (path[i].x === currentPlayerPos.x && path[i].y === currentPlayerPos.y) {
          currentIndex = i;
          break;
        }
      }
      
      // Если нашли текущую позицию, берем следующую точку
      if (currentIndex >= 0 && currentIndex + 1 < path.length) {
        nextPoint = path[currentIndex + 1];
      } else if (currentIndex === -1 && path.length > 0) {
        // Если текущая позиция не найдена в пути, берем первую точку пути
        nextPoint = path[0];
      }
      
      if (nextPoint) {
        window.__playerMoveDirection = {
          dx: nextPoint.x - currentPlayerPos.x,
          dy: nextPoint.y - currentPlayerPos.y
        };
      } else {
        window.__playerMoveDirection = undefined;
      }
    }
    
    // Вычисляем прогресс движения по времени (НЕ по кадрам анимации!)
    if (window.__playerMoveDirection && window.__playerMoveStartTime) {
      const movementSpeedSetting = currentWorld?.settings?.worldMapPlayerSpeed || 3;
      const { playerLocationPresent } = useGameStore.getState();
      const locationMultiplier = playerLocationPresent ? LOCATION_SPEED_MULTIPLIER : WORLD_MAP_SPEED_MULTIPLIER;
      const realMovementSpeed = convertSpeedToMs(movementSpeedSetting) / locationMultiplier;
      const elapsed = performance.now() - window.__playerMoveStartTime;
      
      // Линейный прогресс от 0 до 1 за время одного шага
      const progress = Math.min(1, elapsed / realMovementSpeed);
      
      const maxOffset = Math.min(tileWidth, tileHeight) * 0.75;
      const { dx, dy } = window.__playerMoveDirection;
      
      // Изометрическое преобразование направления в экранные координаты
      offsetX = (dx - dy) * maxOffset * progress ;
      offsetY = (dx + dy) * maxOffset * progress * 0.63;
    }
  } else {
    // Сбрасываем данные движения когда остановились
    window.__playerMoveStartTime = undefined;
    window.__playerMoveDirection = undefined;
    window.__playerLastPosition = undefined;
  }

  const baseW = getPlayerTextureSize(); // Используем глобальную константу размера текстур игрока
  const zoom = tileWidth / baseW;
  
  // ПРАВИЛЬНАЯ синхронизация: анимация должна завершиться за время одного шага движения
  const movementSpeedSetting = currentWorld?.settings?.worldMapPlayerSpeed || 3;
  const { playerLocationPresent } = useGameStore.getState();
  const locationMultiplier = playerLocationPresent ? LOCATION_SPEED_MULTIPLIER : WORLD_MAP_SPEED_MULTIPLIER;
  const realMovementSpeed = convertSpeedToMs(movementSpeedSetting) / locationMultiplier;
  const totalAnimFrames = animations[animationName]?.length || 1;
  // Скорость одного кадра = время шага / количество кадров
  const animationSpeed = Math.max(50, realMovementSpeed / totalAnimFrames);
  
  // Создаём и обновляем анимацию
  if (!window.__playerSkeleton) {
    window.__playerSkeleton = new AnimationPlayer(baseSkeleton, animationName, animationSpeed);
    window.__playerSkeleton.play();
    window.__playerSkeletonLastTime = performance.now();
  } else if (window.__playerSkeleton.getAnimationName() !== animationName) {
    window.__playerSkeleton.setAnimation(animationName);
  }
  
  // Обновляем скорость анимации в соответствии с настройками
  window.__playerSkeleton.setFrameDuration(animationSpeed);
  const now = performance.now();
  const delta = now - (window.__playerSkeletonLastTime || now);
  window.__playerSkeletonLastTime = now;
  window.__playerSkeleton.update(delta);

  ctx.save();
  ctx.translate(centerX + offsetX, centerY + offsetY); // центр клетки + смещение
  ctx.scale(zoom, zoom); // zoom как tileWidth / baseW

  const isoAngle = Math.PI / 1; // 30 градусов
  const scaleY = 0.9; // Сжатие по Y для эффекта наклона

  // Используем коррекцию угла, вычисленную в drawEngine.ts
  let skewYCorrection = window.__playerSkewYCorrection || 0;
  
  // Ограничиваем значение для предотвращения слишком сильного искажения
  skewYCorrection = Math.max(-0.2, Math.min(0.2, skewYCorrection));

  // Создаем изометрическую матрицу трансформации
  // [1, skewX, skewY, scaleY, 0, 0]
  ctx.transform(
    1,                    // scaleX
    skewYCorrection,      // skewY - корректируется относительно движения
    Math.sin(isoAngle),   // skewX (наклон для изометрии)
    scaleY,               // scaleY (сжатие по Y)
    0,                    // translateX
    0                    // translateY
  );

  // Получаем экипированную броню из данных игрока (если есть)
  const equippedArmor = currentWorld?.player?.equipment?.armor || 'LeatherArmor'; // По умолчанию кожаная броня

  drawSkeleton(
    ctx,
    window.__playerSkeleton.getSkeleton(),
    window.__playerSkeleton.getAnimationName(),
    window.__playerSkeleton.getFrameIndex(),
    false
  );
  ctx.restore();
}