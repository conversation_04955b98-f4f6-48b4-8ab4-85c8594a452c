import { Injectable } from '@nestjs/common';
import { CreateWorldDto } from './dto/create-world.dto';
import {  generateBaseWorldAsync } from './generators/worldGenerator';
import { GenerationProgress, createCancellationToken } from './utils/asyncUtils';
import { ProgressService } from './websocket/progress.service';
import { GenerationQueue } from './queue/generationQueue';

@Injectable()
export class AppService {
  private readonly generationQueue: GenerationQueue;

  constructor(private readonly progressService: ProgressService) {
    // Создаем очередь с максимум 3 одновременными генерациями
    this.generationQueue = new GenerationQueue(3);
  }

  getHello(): string {
    return 'World Generator Service работает! 🌍';
  }

  async generateWorld(createWorldDto: CreateWorldDto) {
    // Используем генератор для создания базового мира и сетки карты
    const worldShell = await generateBaseWorldAsync(createWorldDto);

    // Отправляем мир в save service для сохранения
    try {
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const saveResult = await saveResponse.json();
      return {
        success: true,
        world: saveResult.world
      };
    } catch (error) {
      throw new Error(`Failed to save generated world: ${error.message}`);
    }
  }

  async generateWorldAsync(
    createWorldDto: CreateWorldDto,
    progressCallback?: (progress: GenerationProgress) => void
  ) {
    const cancellationToken = createCancellationToken();
    
    try {
      
      // Используем асинхронный генератор для создания базового мира
      const worldShell = await generateBaseWorldAsync(
        createWorldDto,
        progressCallback,
        cancellationToken
      );


      // Отправляем мир в save service для сохранения
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const saveResult = await saveResponse.json();
      
      
      return {
        success: true,
        world: saveResult.world,
        cancellationToken // Возвращаем токен для возможной отмены
      };
    } catch (error) {
      if (error.message === 'Operation was cancelled') {
        return {
          success: false,
          cancelled: true,
          message: 'Генерация мира была отменена'
        };
      }
      
      throw new Error(`Failed to generate world: ${error.message}`);
    }
  }

  /**
   * Генерация мира с WebSocket поддержкой и очередью
   */
  async generateWorldWithProgress(
    createWorldDto: CreateWorldDto,
    userId?: string
  ): Promise<{ sessionId: string; queuePosition?: number }> {
    // Создаем сессию для отслеживания прогресса
    const sessionId = this.progressService.createProgressSession(userId, createWorldDto.name);
    
    // Создаем токен отмены
    const cancellationToken = this.progressService.createCancellationToken(sessionId);
    
    // Создаем callback для отправки прогресса через WebSocket
    const progressCallback = this.progressService.createProgressCallback(sessionId);

    // Добавляем задачу в очередь
    const queueItem = this.generationQueue.addToQueue(
      sessionId,
      userId || 'anonymous',
      createWorldDto,
      (progress) => {
        // Передаем прогресс через WebSocket
        progressCallback(progress);
      },
      cancellationToken
    );

    // Настраиваем обработку завершения генерации
    this.setupGenerationCompletion(sessionId, createWorldDto, cancellationToken);


    // Возвращаем sessionId и позицию в очереди
    return { 
      sessionId,
      queuePosition: queueItem.position 
    };
  }

  /**
   * Настраивает обработку завершения генерации
   */
  private setupGenerationCompletion(
    sessionId: string,
    createWorldDto: CreateWorldDto,
    cancellationToken: any
  ) {
    
    // Проверяем статус каждые 1 секунду
    const checkInterval = setInterval(async () => {
      const taskInfo = this.generationQueue.getTaskInfo(sessionId);
      
      if (!taskInfo || taskInfo.status === 'completed' || taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
        clearInterval(checkInterval);
        
        if (taskInfo?.status === 'completed') {
          // Сохраняем сгенерированный мир
          await this.saveGeneratedWorld(sessionId, createWorldDto);
        } else if (taskInfo?.status === 'failed') {
          this.progressService.notifyGenerationError(sessionId, 'Ошибка генерации мира');
        } else if (taskInfo?.status === 'cancelled') {
          this.progressService.notifyGenerationComplete(sessionId, {
            success: false,
            error: 'Generation was cancelled'
          });
        } else {
          // Если задача не найдена, но генерация завершилась, все равно пытаемся сохранить
          await this.saveGeneratedWorld(sessionId, createWorldDto);
        }
      }
    }, 1000);

    // Очищаем интервал при отмене
    if (cancellationToken && typeof cancellationToken === 'object') {
      cancellationToken.onCancel = () => {
        clearInterval(checkInterval);
      };
    }
  }

  /**
   * Сохраняет сгенерированный мир
   */
  private async saveGeneratedWorld(sessionId: string, createWorldDto: CreateWorldDto) {
    try {
      
      // Здесь должна быть логика получения сгенерированного мира из очереди
      // Пока это создаем заглушку
      const worldShell = await generateBaseWorldAsync(createWorldDto);

      // Сохраняем мир
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';
      
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });


      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const saveResult = await saveResponse.json();
      
      // Безопасно извлекаем worldId
      let worldId: string | undefined;
      if (saveResult && typeof saveResult === 'object') {
        if (saveResult.world && saveResult.world.id) {
          worldId = saveResult.world.id;
        } else if (saveResult.id) {
          worldId = saveResult.id;
        } else if (saveResult.data && saveResult.data.id) {
          worldId = saveResult.data.id;
        }
      }
      
      
      // Уведомляем о завершении
      this.progressService.notifyGenerationComplete(sessionId, {
        success: true,
        worldId: worldId
      });


    } catch (error) {
      this.progressService.notifyGenerationError(sessionId, error.message);
    }
  }

  /**
   * Отмена генерации мира
   */
  cancelWorldGeneration(sessionId: string): boolean {
    const removed = this.generationQueue.removeFromQueue(sessionId);
    if (removed) {
    }
    return removed;
  }

  /**
   * Получение статистики активных сессий и очереди
   */
  getGenerationStats() {
    const queueStats = this.generationQueue.getQueueStats();
    const progressStats = this.progressService.getSessionStats();
    
    return {
      queue: queueStats,
      progress: progressStats,
      summary: {
        totalInQueue: queueStats.waiting,
        totalProcessing: queueStats.processing,
        maxConcurrent: queueStats.maxConcurrent,
        activeSessions: progressStats.activeSessions
      }
    };
  }

  /**
   * Получение информации о конкретной задаче
   */
  getTaskInfo(sessionId: string) {
    return this.generationQueue.getTaskInfo(sessionId);
  }

  /**
   * Изменение максимального количества одновременных генераций
   */
  setMaxConcurrentGenerations(max: number) {
    this.generationQueue.setMaxConcurrent(max);
    return {
      success: true,
      newMaxConcurrent: max,
      message: `Максимум одновременных генераций установлен: ${max}`
    };
  }
}