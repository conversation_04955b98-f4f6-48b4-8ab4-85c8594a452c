{"name": "save-service", "version": "0.0.1", "description": "Save management microservice for NuclearStory", "author": "NuclearStory Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/swagger": "^7.1.16", "typeorm": "^0.3.17", "pg": "^8.11.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/node": "^20.3.1", "typescript": "^5.1.3"}}