import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { WorldEntity } from '../entities/world.entity'
import { WorldsController } from './worlds.controller'
import { WorldsService } from './worlds.service'

/**
 * Модуль для управления мирами
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([WorldEntity])
  ],
  controllers: [WorldsController],
  providers: [WorldsService],
  exports: [WorldsService]
})
export class WorldsModule {}
