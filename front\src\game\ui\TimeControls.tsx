/**
 * Компонент управления временем с горячими клавишами
 */

import React, { useState, useEffect } from 'react';
import { TimeWaitModal } from './dialogs/TimeWaitModal';
import { GameClock } from '../components/GameClock';
import { GameTimeDisplay } from './panels/infoTime/GameTimeDisplay';

export const TimeControls: React.FC = () => {
  const [isWaitModalOpen, setIsWaitModalOpen] = useState(false);
  const [isClockOpen, setIsClockOpen] = useState(false);
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Игнорируем, если фокус на input элементах
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }
      
      // T - открыть модалку ожидания
      if (event.key.toLowerCase() === 't' && !event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        setIsWaitModalOpen(true);
      }
      
      // Ctrl + T - открыть часы
      if (event.key.toLowerCase() === 't' && event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        setIsClockOpen(true);
      }
      
      // ESC - закрыть модалки
      if (event.key === 'Escape') {
        setIsWaitModalOpen(false);
        setIsClockOpen(false);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  
  return (
    <>
      {/* Постоянно отображаемые часы в углу */}
      <div style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 100
      }}>
        <GameTimeDisplay showDetails={false} />
      </div>
      
      {/* Модалка ожидания */}
      <TimeWaitModal 
        isOpen={isWaitModalOpen}
        onClose={() => setIsWaitModalOpen(false)}
      />
      
      {/* Окно часов */}
      <GameClock
        isOpen={isClockOpen}
        onClose={() => setIsClockOpen(false)}
      />
      
      {/* Подсказки по горячим клавишам */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        background: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        padding: '10px',
        borderRadius: '6px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 100,
        opacity: 0.8
      }}>
        <div>T - Ожидание времени</div>
        <div>Ctrl+T - Часы</div>
      </div>
    </>
  );
};
