import { PresetLocationMap } from "../presetType";


export const locationConfigs: Record<string, PresetLocationMap > = {

'mediumBuilding_1': {
  name: 'bigBuilding_1',
  width: 20,
  height: 20,
  tokenMap:  [
    ["w", "w", "w", "v", "v", "w", "w", "w", "w", "w"],
    ["w", "X", "0", "0", "0", "0", "0", "0", "O", "w"],
    ["w", "X", "0", "0", "0", "0", "0", "x", "0", "w"],
    ["w", "0", "0", "x", "0", "0", "L", "0", "0", "w"],
    ["w", "0", "0", "0", "0", "w", "w", "v", "v", "w"],
    ["w", "V", "0", "0", "S", "w", "0", "0", "0", "0"],
    ["w", "C", "0", "L", "S", "w", "0", "0", "0", "0"],
    ["w", "0", "0", "0", "0", "v", "0", "0", "0", "0"],
    ["w", "X", "0", "0", "0", "v", "0", "0", "0", "0"],
    ["w", "w", "D", "w", "w", "w", "0", "0", "0", "0"]
  ],
  anchor: { x: 15, y: 15  }, //смещение относительно центра локации  в любую сторону
  rotations: false,
  mirror: true,
  weight: 1,



}
}