import React, { useEffect, useState } from 'react';
import { X, Loader2 } from 'lucide-react';
import styles from './WorldGenerationProgress.module.css';

interface GenerationProgress {
  stage: string;
  progress: number;
  currentOperation: string;
  estimatedTimeRemaining?: number;
}

interface WorldGenerationProgressProps {
  isVisible: boolean;
  progress: GenerationProgress | null;
  worldName: string;
  onCancel: () => void;
}

const WorldGenerationProgress: React.FC<WorldGenerationProgressProps> = ({
  isVisible,
  progress,
  worldName,
  onCancel
}) => {
  const [dots, setDots] = useState('');

  // Анимация точек для загрузки
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isVisible]);

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.ceil(milliseconds / 1000);
    if (seconds < 60) {
      return `${seconds} сек`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!isVisible) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        {/* Заголовок */}
        <div className={styles.header}>
          <div className={styles.title}>
            <Loader2 className={styles.spinner} />
            <span>Создание мира "{worldName}"</span>
          </div>
          <button 
            onClick={onCancel}
            className={styles.cancelButton}
            title="Отменить создание мира"
          >
            <X />
          </button>
        </div>

        {/* Основной контент */}
        <div className={styles.content}>
          {progress ? (
            <>
              {/* Текущий этап */}
              <div className={styles.stageInfo}>
                <div className={styles.stageName}>
                  {progress.stage}{dots}
                </div>
                <div className={styles.currentOperation}>
                  {progress.currentOperation}
                </div>
              </div>

              {/* Прогресс-бр */}
              <div className={styles.progressContainer}>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ width: `${progress.progress}%` }}
                  />
                  <div className={styles.progressGlow} />
                </div>
                <div className={styles.progressText}>
                  {progress.progress}%
                </div>
              </div>

              {/* Дополнительная информация */}
              <div className={styles.additionalInfo}>
                {progress.estimatedTimeRemaining && (
                  <div className={styles.timeRemaining}>
                    Осталось примерно: {formatTime(progress.estimatedTimeRemaining)}
                  </div>
                )}
                <div className={styles.hint}>
                  Создание мира может занять несколько минут...
                </div>
              </div>
            </>
          ) : (
            <>
              {/* Состояние инициализации */}
              <div className={styles.stageInfo}>
                <div className={styles.stageName}>
                  Инициализация{dots}
                </div>
                <div className={styles.currentOperation}>
                  Подготовка к созданию мира
                </div>
              </div>

              {/* Прогресс-бар в состоянии ожидания */}
              <div className={styles.progressContainer}>
                <div className={styles.progressBar}>
                  <div className={styles.progressFillIndeterminate} />
                </div>
                <div className={styles.progressText}>
                  0%
                </div>
              </div>

              <div className={styles.additionalInfo}>
                <div className={styles.hint}>
                  Подключение к серверу генерации...
                </div>
              </div>
            </>
          )}
        </div>

        {/* Подсказки */}
        <div className={styles.footer}>
          <div className={styles.tips}>
            💡 <strong>Совет:</strong> Большие миры создаются дольше, но содержат больше контета
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorldGenerationProgress;