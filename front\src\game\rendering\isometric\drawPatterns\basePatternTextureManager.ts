/**
 * Базовый класс для менеджеров паттерновых текстур
 */

/**
 * Базовый менеджер паттерновых текстур
 */
export abstract class PatternTextureManager {
  protected textureCache = new Map<string, HTMLImageElement>();
  protected loadingPromises = new Map<string, Promise<HTMLImageElement>>();

  /**
   * Создает промис загрузки текстуры
   * @param texturePath - путь к текстуре
   * @param textureKey - ключ для кэша
   */
  protected createLoadingPromise(texturePath: string, textureKey: string): Promise<HTMLImageElement> {
    return new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.textureCache.set(textureKey, img);
        this.loadingPromises.delete(textureKey);
        resolve(img);
      };
      img.onerror = () => {
        this.loadingPromises.delete(textureKey);
        console.warn(`Failed to load pattern texture: ${texturePath}`);
        reject(new Error(`Failed to load texture: ${texturePath}`));
      };
      img.src = texturePath;
    });
  }

  /**
   * Очищает кэш текстур
   */
  clearCache(): void {
    this.textureCache.clear();
    this.loadingPromises.clear();
  }
}