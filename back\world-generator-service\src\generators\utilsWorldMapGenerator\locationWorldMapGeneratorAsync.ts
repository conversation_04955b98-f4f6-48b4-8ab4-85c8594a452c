import { v4 as uuidv4 } from 'uuid';
import { WorldMapCell } from '../../shared/types/World';
import { TransferLocation } from '../../shared/types/Location';
import { LocationSubtype, LocationType, MaterialTexture, TerrainType, WorldMapDecorations } from '../../shared/enums';
import { Position } from '../../shared/models/Player';
import { 
  GenerationProgress, 
  CancellationToken, 
  ProgressTracker,
  delay,
  processBatch
} from '../../utils/asyncUtils';

// Типы локаций для генерации


/**
 * Асинхронная генерация локаций на карте мира
 * @param grid - сетка карты мира
 * @param worldSize - размер мира
 * @param rng - генератор случайных чисел
 * @param locationDensity - плотность локаций (0.01 = 1% клеток с локациями)
 * @param progressTracker - трекер прогресса
 * @param cancellationToken - токен отмены операции
 */
export async function generateLocationsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  locationDensity: number = 0.04,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {

  try {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    // Этап 1: Помечаем крайние клетки как locationNear = true
    progressTracker.updateStageProgress(0, 'Разметка границ карты');
    await markBorderCellsAsync(grid, worldSize, progressTracker, cancellationToken);

    // Этап 2: Генерируем основные локации
    progressTracker.updateStageProgress(20, 'Генерация основных локаций');
    const generatedLocations = await generateMainLocationsAsync(
      grid, 
      worldSize, 
      rng, 
      locationDensity, 
      progressTracker, 
      cancellationToken
    );

    // Этап 3: Генерируем города и руины вокруг локаций
    progressTracker.updateStageProgress(60, 'Генерация городов и руин');
    await generateCitiesAndRuinsAroundLocationsAsync(
      grid, 
      worldSize, 
      rng, 
      progressTracker, 
      cancellationToken
    );

    // Этап 4: Генерируем дороги от локаций
    progressTracker.updateStageProgress(80, 'Генерация дорог');
    await generateRoadsFromLocationsAsync(
      grid, 
      worldSize, 
      rng, 
      progressTracker, 
      cancellationToken
    );

    progressTracker.updateStageProgress(100, `Генерация локаций завершена. Создано: ${generatedLocations}`);

  } catch (error) {
    if (error.message === 'Operation was cancelled') {
      throw error;
    }
    throw new Error(`Ошибка генерации локаций: ${error.message}`);
  }
}

/**
 * Асинхронно помечает крайние клетки карты как locationNear = true
 */
async function markBorderCellsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  const batchSize = Math.max(50, Math.floor(worldSize / 10));
  let processed = 0;
  const total = worldSize * worldSize;

  for (let x = 0; x < worldSize; x++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const batch: (() => void)[] = [];
    
    for (let y = 0; y < worldSize; y++) {
      batch.push(() => {
        const cellKey = `${x},${y}`;
        const cell = grid[cellKey];
        
        if (!cell) return;

        // Проверяем, является ли клетка крайней
        if (x === 0 || y === 0 || x === worldSize - 1 || y === worldSize - 1) {
          cell.locationNear = true;
        } else {
          cell.locationNear = false;
        }
      });
    }

    // Обрабатываем батч без delay
    await processBatch(batch, batchSize, async () => {});

    processed += worldSize;
    const progress = Math.floor((processed / total) * 20); // 20% от общего прогресса
    progressTracker.updateStageProgress(progress, `Разметка границ: ${processed}/${total} клеток`);
  }
}

/**
 * Асинхронная генерация основных локаций
 */
async function generateMainLocationsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  locationDensity: number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<number> {
  const totalCells = worldSize * worldSize;
  const targetLocations = Math.floor(totalCells * locationDensity);
  let generatedLocations = 0;

  // Создаем массив всех позиций
  const allPositions: Position[] = [];
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      allPositions.push({ x, y });
    }
  }

  // Перемешиваем позиции для случайного порядка
  shuffleArray(allPositions, rng);

  // Обрабатываем позиции батчами
  const batchSize = Math.max(100, Math.floor(allPositions.length / 100));
  let processed = 0;

  for (let i = 0; i < allPositions.length && generatedLocations < targetLocations; i += batchSize) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const batch = allPositions.slice(i, Math.min(i + batchSize, allPositions.length));
    
    for (const pos of batch) {
      if (generatedLocations >= targetLocations) break;

      const cellKey = `${pos.x},${pos.y}`;
      const cell = grid[cellKey];

      if (!cell) continue;

      // Проверяем возможность размещения локации
      if (canPlaceLocation(cell, grid, worldSize)) {
        // Если локация попадает на воду, пытаемся найти ближайшую сушу
        let finalPos = pos;
        if (cell.decoration === WorldMapDecorations.RIVER || cell.decoration === WorldMapDecorations.LAKE) {
          const landPos = await findNearestLandAsync(grid, pos, worldSize, cancellationToken);
          if (landPos && canPlaceLocation(grid[`${landPos.x},${landPos.y}`], grid, worldSize)) {
            finalPos = landPos;
          } else {
            continue; // Пропускаем эту позицию, если не нашли подходящую сушу
          }
        }

        // Генерируем локацию асинхронно
        const location = await generateLocationAsync(finalPos, grid[`${finalPos.x},${finalPos.y}`].LVLZone, rng, cancellationToken);
        grid[`${finalPos.x},${finalPos.y}`].location = location;

        // Убираем декорацию под локацией
        grid[`${finalPos.x},${finalPos.y}`].decoration = WorldMapDecorations.NONE;

        // Помечаем радиус вокруг локации как locationNear = true
        await markLocationRadiusAsync(grid, finalPos, 2, worldSize, cancellationToken);

        generatedLocations++;
      }
    }

    processed += batch.length;
    const progress = 20 + Math.floor((processed / allPositions.length) * 40); // 20-60% от общего прогресса
    progressTracker.updateStageProgress(
      progress, 
      `Генерация локаций: ${generatedLocations}/${targetLocations} (обработано ${processed}/${allPositions.length} позиций)`
    );

    // Оставляем только один delay для освобождения event loop между батчами
    if (i % (batchSize * 20) === 0) {
      await delay(0);
    }
  }

  return generatedLocations;
}

/**
 * Проверяет, можно ли разместить локацию в данной клетке
 */
function canPlaceLocation(
  cell: WorldMapCell, 
  grid: Record<string, WorldMapCell>, 
  worldSize: number
): boolean {
  // Не размещаем локации:
  // 1. Где есть terrainMarker
  if (cell.terrarianMarker === 1) return false;

  // 2. Где уже есть локация
  if (cell.location) return false;

  // 3. Где locationNear = true (рядом с другими локациями или на краю)
  if (cell.locationNear) return false;

  // 4. В заблокированных клетках
  if (cell.blocked) return false;

  // 5. В воде (height = 0)
  if (cell.height === 0) return false;

  // 6. На реках или озерах
  if (cell.decoration === WorldMapDecorations.RIVER || cell.decoration === WorldMapDecorations.LAKE) return false;

  return true;
}

/**
 * Асинхронно помечает радиус вокруг позиции как locationNear = true
 */
async function markLocationRadiusAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  radius: number, 
  worldSize: number,
  cancellationToken?: CancellationToken
): Promise<void> {
  for (let x = center.x - radius; x <= center.x + radius; x++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    for (let y = center.y - radius; y <= center.y + radius; y++) {
      // Проверяем границы карты
      if (x < 0 || y < 0 || x >= worldSize || y >= worldSize) continue;

      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (cell) {
        cell.locationNear = true;
      }
    }
  }
}

/**
 * Асинхронная генерация TransferLocation (только базовая инициализация для world map)
 */
async function generateLocationAsync(
  position: Position, 
  lvlZone: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<TransferLocation> {
  // Проверяем токен отмены
  if (cancellationToken?.isCancelled) {
    throw new Error('Operation was cancelled');
  }

  const locationId = uuidv4();
  

  // Создаем одинаковый TransferLocation шаблон 200x200
  const location: TransferLocation = {
    id: locationId,
    name: `TransferLocation_${Math.floor(rng() * 1000000)}`,
    description: `TransferLocation_${Math.floor(rng() * 1000000)}`,
    locationSize: [150, 130],
    type: LocationType.OUTDOOR,
    subtype: LocationSubtype.CAMP,
    terrain: TerrainType.WASTELAND,
    isDiscovered: false,
    isVisible: true,
    textureMaterial: MaterialTexture.WOOD,
    morality: -1,
    playerPosition: [100, 99], // Массив точек для позиций игрока
    playerPresent: false,
    spawnPosition: [100, 100],
    goBackPosition: [[100, 99], [99, 100], [100, 98] ],
    decorations: {
      


    },
    decorationSide: [],
    interactive: [ 
    
    ],
  };

  return location;
}

/**
 * Асинхронная генерация городов и руин вокруг локаций
 */
async function generateCitiesAndRuinsAroundLocationsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  // Находим все локации
  const locationPositions: Position[] = [];
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      if (grid[cellKey] && grid[cellKey].location) {
        locationPositions.push({ x, y });
      }
    }
  }

  // Обрабатываем локации батчами
  const batchSize = Math.max(1, Math.floor(locationPositions.length / 10));
  let processed = 0;

  for (let i = 0; i < locationPositions.length; i += batchSize) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const batch = locationPositions.slice(i, Math.min(i + batchSize, locationPositions.length));
    
    for (const locationPos of batch) {
      const location = grid[`${locationPos.x},${locationPos.y}`].location;
      if (!location) continue;

      // Исключаем подземные локации
      if (location.type === LocationType.UNDERGROUND) continue;

      // Генерируем города с шансом 50%
      if (rng() * 100 < 50) {
        await generateCityAroundLocationAsync(grid, locationPos, worldSize, rng, cancellationToken);
      }

      // Генерируем руины с шансом 40%
      if (rng() * 100 < 40) {
        await generateRuinsAroundLocationAsync(grid, locationPos, worldSize, rng, cancellationToken);
      }
    }

    processed += batch.length;
    const progress = 60 + Math.floor((processed / locationPositions.length) * 20); // 60-80% от общего прогресса
    progressTracker.updateStageProgress(
      progress, 
      `Генерация окружения: ${processed}/${locationPositions.length} локаций`
    );

    // Delay только между большими батчами
    if (i % (batchSize * 10) === 0) {
      await delay(0);
    }
  }
}

/**
 * Асинхронная гене��ация города вокруг локации
 */
async function generateCityAroundLocationAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const patterns = [
    () => generateCircularCityAsync(grid, center, worldSize, rng, cancellationToken),
    () => generateStripCityAsync(grid, center, worldSize, rng, cancellationToken),
    () => generateScatteredCityAsync(grid, center, worldSize, rng, cancellationToken)
  ];

  // Выбираем случайный паттерн
  const selectedPattern = patterns[Math.floor(rng() * patterns.length)];
  await selectedPattern();
}

/**
 * Асинхронный паттерн 1: Круговой город
 */
async function generateCircularCityAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const radius = 2 + Math.floor(rng() * 2); // Радиус 2-3

  for (let x = center.x - radius; x <= center.x + radius; x++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    for (let y = center.y - radius; y <= center.y + radius; y++) {
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;

      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;

      if (distance > 1 && distance <= radius && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          !grid[cellKey].location &&
          rng() * 100 < 60) {
        grid[cellKey].decoration = WorldMapDecorations.CITY;
        grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.CITY);
      }
    }
  }
}

/**
 * Асинхронный паттерн 2: Полосовой город
 */
async function generateStripCityAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const directions = [
    { x: 1, y: 0 },   // Горизонтальная полоса
    { x: 0, y: 1 },   // Вертикальная полоса
  ];

  const direction = directions[Math.floor(rng() * directions.length)];
  const stripLength = 3;
  const stripWidth = 1;

  for (let i = -stripLength; i <= stripLength; i++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    for (let w = -stripWidth; w <= stripWidth; w++) {
      let x, y;

      if (direction.x === 1 && direction.y === 0) {
        x = center.x + i;
        y = center.y + w;
      } else {
        x = center.x + w;
        y = center.y + i;
      }

      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;

      const cellKey = `${x},${y}`;
      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);

      if (distance > 1 && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          !grid[cellKey].location &&
          rng() * 100 < 70) {
        grid[cellKey].decoration = WorldMapDecorations.CITY;
        grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.CITY);
      }
    }
  }
}

/**
 * Асинхронный паттерн 3: Разбросанный город
 */
async function generateScatteredCityAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const clusterCount = 2 + Math.floor(rng() * 3); // 2-4 кластера

  for (let cluster = 0; cluster < clusterCount; cluster++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const offsetX = Math.floor((rng() - 0.5) * 6); // Смещение -3 до 3
    const offsetY = Math.floor((rng() - 0.5) * 6);
    const clusterCenter = {
      x: center.x + offsetX,
      y: center.y + offsetY
    };

    for (let x = clusterCenter.x - 1; x <= clusterCenter.x + 1; x++) {
      for (let y = clusterCenter.y - 1; y <= clusterCenter.y + 1; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;

        const cellKey = `${x},${y}`;
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);

        if (distance > 1 && 
            grid[cellKey] && 
            grid[cellKey].decoration === WorldMapDecorations.NONE &&
            !grid[cellKey].location &&
            rng() * 100 < 50) {
          grid[cellKey].decoration = WorldMapDecorations.CITY;
          grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.CITY);
        }
      }
    }
  }
}

/**
 * Асинхронная генерация руин вокруг локации
 */
async function generateRuinsAroundLocationAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const patterns = [
    () => generateCircularRuinsAsync(grid, center, worldSize, rng, cancellationToken),
    () => generateScatteredRuinsAsync(grid, center, worldSize, rng, cancellationToken)
  ];

  // Выбираем случайный паттерн
  const selectedPattern = patterns[Math.floor(rng() * patterns.length)];
  await selectedPattern();
}

/**
 * Асинхронный паттерн 1: Круговые руины
 */
async function generateCircularRuinsAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const radius = 2;

  for (let x = center.x - radius; x <= center.x + radius; x++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    for (let y = center.y - radius; y <= center.y + radius; y++) {
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;

      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;

      if (distance > 1 && distance <= radius && 
          grid[cellKey] && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          !grid[cellKey].location &&
          rng() * 100 < 40) {
        grid[cellKey].decoration = WorldMapDecorations.RUINS;
        grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.RUINS);
      }
    }
  }
}

/**
 * Асинхронный паттерн 2: Разбросанные руины
 */
async function generateScatteredRuinsAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const ruinCount = 3 + Math.floor(rng() * 4); // 3-6 руин

  for (let ruin = 0; ruin < ruinCount; ruin++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const offsetX = Math.floor((rng() - 0.5) * 8); // Смещение -4 до 4
    const offsetY = Math.floor((rng() - 0.5) * 8);
    const ruinPos = {
      x: center.x + offsetX,
      y: center.y + offsetY
    };

    if (ruinPos.x < 0 || ruinPos.x >= worldSize || ruinPos.y < 0 || ruinPos.y >= worldSize) continue;

    const cellKey = `${ruinPos.x},${ruinPos.y}`;
    const distance = Math.sqrt((ruinPos.x - center.x) ** 2 + (ruinPos.y - center.y) ** 2);

    if (distance > 1 && 
        grid[cellKey] && 
        grid[cellKey].decoration === WorldMapDecorations.NONE &&
        !grid[cellKey].location) {
      grid[cellKey].decoration = WorldMapDecorations.RUINS;
      grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.RUINS);
    }
  }
}

/**
 * Асинхронная генерация дорог от локаций
 */
async function generateRoadsFromLocationsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  // Находим все локации (кроме подземных)
  const locationPositions: Position[] = [];
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      if (grid[cellKey] && grid[cellKey].location && 
          grid[cellKey].location.type !== LocationType.UNDERGROUND) {
        locationPositions.push({ x, y });
      }
    }
  }

  // Обрабатываем локации батчами
  const batchSize = Math.max(1, Math.floor(locationPositions.length / 5));
  let processed = 0;

  for (let i = 0; i < locationPositions.length; i += batchSize) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const batch = locationPositions.slice(i, Math.min(i + batchSize, locationPositions.length));
    
    for (const locationPos of batch) {
      await generateRoadFromLocationAsync(grid, locationPos, worldSize, rng, cancellationToken);
    }

    processed += batch.length;
    const progress = 80 + Math.floor((processed / locationPositions.length) * 20); // 80-100% от общего прогресса
    progressTracker.updateStageProgress(
      progress, 
      `Генерация дорог: ${processed}/${locationPositions.length} локаций`
    );

    // Delay только между большими батчами
    if (i % (batchSize * 10) === 0) {
      await delay(0);
    }
  }
}

/**
 * Асинхронная генерация дороги от конкретной локации
 */
async function generateRoadFromLocationAsync(
  grid: Record<string, WorldMapCell>, 
  start: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const directions = [
    { x: 1, y: 0 },   // Восток
    { x: -1, y: 0 },  // Запад
    { x: 0, y: 1 },   // Юг
    { x: 0, y: -1 },  // Север
    { x: 1, y: 1 },   // Юго-восток
    { x: -1, y: 1 },  // Юго-запад
    { x: 1, y: -1 },  // Северо-восток
    { x: -1, y: -1 }, // Северо-запад
  ];

  let currentPos = { ...start };
  let currentDirection = directions[Math.floor(rng() * directions.length)];
  const minLength = 4; // Минимальная длина дороги
  const maxLength = 8; // Максимальная длина дороги
  let roadLength = 0;

  // Начинаем с клетки рядом с локацией, а не с самой локации
  currentPos.x += currentDirection.x;
  currentPos.y += currentDirection.y;

  for (let i = 0; i < maxLength; i++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    if (currentPos.x < 0 || currentPos.x >= worldSize || currentPos.y < 0 || currentPos.y >= worldSize) {
      // Если вышли за границы, но не достигли минимальной длины, меняем направление
      if (roadLength < minLength) {
        // Возвращаемся на предыдущую позицию и меняем направление
        currentPos.x -= currentDirection.x;
        currentPos.y -= currentDirection.y;
        currentDirection = directions[Math.floor(rng() * directions.length)];
        currentPos.x += currentDirection.x;
        currentPos.y += currentDirection.y;
        continue;
      }
      break;
    }

    const cellKey = `${currentPos.x},${currentPos.y}`;
    
    // Не касаемся локаций
    if (grid[cellKey] && !grid[cellKey].location) {
      const currentDecoration = grid[cellKey].decoration;

      // Если упираемся в озеро, меняем направление
      if (currentDecoration === WorldMapDecorations.LAKE) {
        if (roadLength < minLength) {
          // Меняем направление, если не достигли минимальной длины
          currentDirection = directions[Math.floor(rng() * directions.length)];
          currentPos.x += currentDirection.x;
          currentPos.y += currentDirection.y;
          continue;
        } else {
          // Если достигли минимальной длины, можем остановиться
          break;
        }
      }

      // Если пересекаем реку, ставим мост
      if (currentDecoration === WorldMapDecorations.RIVER) {
        grid[cellKey].decoration = WorldMapDecorations.BRIDGE;
        grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.BRIDGE);
        roadLength++;
      }
      // Размещаем дорогу только если клетка пустая
      else if (currentDecoration === WorldMapDecorations.NONE) {
        grid[cellKey].decoration = WorldMapDecorations.ROAD;
        grid[cellKey].terrain = getTerrainForDecoration(WorldMapDecorations.ROAD);
        roadLength++;
      }

      // Змейка: случайное изменение направления (30% шанс после минимальной длины)
      if (roadLength >= minLength && rng() * 100 < 30) {
        currentDirection = directions[Math.floor(rng() * directions.length)];
      }
    }

    currentPos.x += currentDirection.x;
    currentPos.y += currentDirection.y;
  }

  // Если не достигли минимальной длины, пытаемся продолжить в другом направлении
  if (roadLength < minLength) {
    await extendRoadToMinimumLengthAsync(grid, start, roadLength, minLength, worldSize, rng, cancellationToken);
  }
}

/**
 * Асинхронно ищет ближайшую сушу от заданной позиции
 */
async function findNearestLandAsync(
  grid: Record<string, WorldMapCell>, 
  start: Position, 
  worldSize: number,
  cancellationToken?: CancellationToken
): Promise<Position | null> {
  const maxRadius = 5; // Максимальный радиус поиска
  
  for (let radius = 1; radius <= maxRadius; radius++) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    for (let x = start.x - radius; x <= start.x + radius; x++) {
      for (let y = start.y - radius; y <= start.y + radius; y++) {
        // Проверяем только клетки на границе текущего радиуса
        if (Math.abs(x - start.x) !== radius && Math.abs(y - start.y) !== radius) continue;
        
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;

        const cellKey = `${x},${y}`;
        const cell = grid[cellKey];
        
        if (cell && 
            cell.decoration !== WorldMapDecorations.RIVER && 
            cell.decoration !== WorldMapDecorations.LAKE &&
            cell.height > 0) {
          return { x, y };
        }
      }
    }
  }
  
  return null; // Не нашли подходящую сушу
}

/**
 * Асинхронно продлевает дорогу до минимальной длины
 */
async function extendRoadToMinimumLengthAsync(
  grid: Record<string, WorldMapCell>, 
  start: Position, 
  currentLength: number, 
  minLength: number, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const directions = [
    { x: 1, y: 0 },   // Восток
    { x: -1, y: 0 },  // Запад
    { x: 0, y: 1 },   // Юг
    { x: 0, y: -1 },  // Север
  ];

  const remainingLength = minLength - currentLength;
  
  // Пытаемся найти направление, где можем продлить дорогу
  for (const direction of directions) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    let currentPos = { ...start };
    let extendedLength = 0;
    let canExtend = true;

    // Проверяем, можем ли продлить в этом направлении
    for (let i = 1; i <= remainingLength && canExtend; i++) {
      currentPos.x += direction.x;
      currentPos.y += direction.y;

      if (currentPos.x < 0 || currentPos.x >= worldSize || 
          currentPos.y < 0 || currentPos.y >= worldSize) {
        canExtend = false;
        break;
      }

      const cellKey = `${currentPos.x},${currentPos.y}`;
      const cell = grid[cellKey];

      if (!cell || cell.location || cell.decoration === WorldMapDecorations.LAKE) {
        canExtend = false;
        break;
      }
    }

    // Если можем продлить в этом направлении, делаем это
    if (canExtend) {
      currentPos = { ...start };
      
      for (let i = 1; i <= remainingLength; i++) {
        currentPos.x += direction.x;
        currentPos.y += direction.y;

        const cellKey = `${currentPos.x},${currentPos.y}`;
        const cell = grid[cellKey];

        if (cell.decoration === WorldMapDecorations.RIVER) {
          cell.decoration = WorldMapDecorations.BRIDGE;
          cell.terrain = getTerrainForDecoration(WorldMapDecorations.BRIDGE);
        } else if (cell.decoration === WorldMapDecorations.NONE) {
          cell.decoration = WorldMapDecorations.ROAD;
          cell.terrain = getTerrainForDecoration(WorldMapDecorations.ROAD);
        }
      }
      break; // Успешно продлили, выходим
    }
  }
}

/**
 * Возвращает соответствующий terrain для декорации
 */
function getTerrainForDecoration(decoration: WorldMapDecorations): TerrainType {
  switch (decoration) {
    case WorldMapDecorations.LAKE:
      return TerrainType.WATER;
    
    default:
      return TerrainType.WASTELAND; // Все остальные декорации на пустошах
  }
}

// Утилитарные функции

/**
 * Выбирает случайный элемент на основе весов
 */
function selectWeightedRandom<T extends { weight: number }>(items: T[], rng: () => number): T {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = rng() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random <= 0) {
      return item;
    }
  }
  
  return items[items.length - 1]; // Fallback
}

/**
 * Перемешивает массив (алгоритм Фишера-Йетса)
 */
function shuffleArray<T>(array: T[], rng: () => number): void {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
}