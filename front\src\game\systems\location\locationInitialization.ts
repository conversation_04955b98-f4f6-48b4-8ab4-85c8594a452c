/**
 * Система инициализации состояния локаций при загрузке игры
 */

import { WorldMap } from '../../../shared/types/World';

/**
 * Проверяет состояние игрока при загрузке мира и возвращает правильное значение playerLocationPresent
 */
export function checkPlayerLocationStatus(world: WorldMap): boolean {
  if (!world.worldMap || !world.player?.position) {
    return false;
  }

  // Проходим по всем клеткам мира и ищем локации с playerPresent: true
  for (const [tileKey, tile] of Object.entries(world.worldMap)) {
    if (tile.location?.playerPresent) {
      return true;
    }
  }

  return false;
}

