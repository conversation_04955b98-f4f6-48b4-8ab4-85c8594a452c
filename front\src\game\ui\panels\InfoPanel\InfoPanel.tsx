import * as React from 'react';
import { GameTimeDisplay } from '../../../components/GameTimeDisplay';

import styles from './InfoPanel.module.css';

interface InfoPanelProps {
  displayCoordinates?: { x: number; y: number };
  onCenterOnPlayer?: () => void;
  zoom?: number;
  onZoomChange?: (newZoom: number) => void;
  // Системные действия
  isMuted?: boolean;
  onToggleMute?: () => void;
  onSave?: () => void;
  onLoad?: () => void;
  onSettings?: () => void;
  onExit?: () => void;
}

export const InfoPanel: React.FC<InfoPanelProps> = ({
  displayCoordinates,
  onCenterOnPlayer,
  zoom,
  onZoomChange,
  isMuted = false,
  onToggleMute,
  onSave,
  onLoad,
  onSettings,
  onExit
}) => {
  return (
    <div className={styles.panel_img}>
     
      
    </div>
  );
}