import { Controller, Get, Post, Put, Body, Param, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { CreateWorldDto } from './dto/create-world.dto';

@ApiTags('world-generator')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Получить информацию о сервисе' })
  @ApiResponse({ status: 200, description: 'Информация о сервисе' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({ summary: 'Проверка здоровья сервиса' })
  @ApiResponse({ status: 200, description: 'Сервис работает нормально' })
  getHealth() {
    return {
      status: 'ok',
      service: 'world-generator-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  @Post('generate-world')
  @ApiOperation({ summary: 'Генерация нового мира (синхронная версия)' })
  @ApiResponse({ status: 201, description: 'Мир успешно сгенерирован' })
  async generateWorld(@Body() createWorldDto: CreateWorldDto) {
    return this.appService.generateWorld(createWorldDto);
  }

  @Post('generate-world-async')
  @ApiOperation({ summary: 'Асинхронная генерация нового мира с прогрессом' })
  @ApiResponse({ status: 201, description: 'Мир успешно сгенерирован асинхронно' })
  async generateWorldAsync(@Body() createWorldDto: CreateWorldDto) {
    
    const progressCallback = (progress: any) => {
      if (progress.estimatedTimeRemaining) {
      }
    };

    return this.appService.generateWorldAsync(createWorldDto, progressCallback);
  }

  @Post('generate-world-with-progress')
  @ApiOperation({ summary: 'Генерация мира с WebSocket прогрессом и очередью' })
  @ApiResponse({ status: 201, description: 'Генерация добавлена в очередь, прогресс через WebSocket' })
  async generateWorldWithProgress(
    @Body() createWorldDto: CreateWorldDto,
    @Req() request: any
  ) {
    
    const userId = request.headers['x-user-id'] || 'anonymous';
    
    return this.appService.generateWorldWithProgress(createWorldDto, userId);
  }

  @Post('cancel-generation/:sessionId')
  @ApiOperation({ summary: 'Отмена генерации мира' })
  @ApiResponse({ status: 200, description: 'Запрос на отмену отправлен' })
  async cancelGeneration(@Param('sessionId') sessionId: string) {
    
    const cancelled = this.appService.cancelWorldGeneration(sessionId);
    
    return {
      success: cancelled,
      message: cancelled ? 'Generation cancelled and removed from queue' : 'Session not found'
    };
  }

  @Get('generation-stats')
  @ApiOperation({ summary: 'Статистика активных генераций и очереди' })
  @ApiResponse({ status: 200, description: 'Статистика сессий генерации и очереди' })
  getGenerationStats() {
    return this.appService.getGenerationStats();
  }

  @Get('task-info/:sessionId')
  @ApiOperation({ summary: 'Информация о конкретной задаче' })
  @ApiResponse({ status: 200, description: 'Информация о задаче в очереди или обработке' })
  getTaskInfo(@Param('sessionId') sessionId: string) {
    const taskInfo = this.appService.getTaskInfo(sessionId);
    if (!taskInfo) {
      return {
        success: false,
        message: 'Задача не найдена'
      };
    }
    return {
      success: true,
      taskInfo
    };
  }

  @Post('set-max-concurrent/:max')
  @ApiOperation({ summary: 'Установить максимальное количество одновременных генераций' })
  @ApiResponse({ status: 200, description: 'Максимум одновременных генераций изменен' })
  setMaxConcurrent(@Param('max') max: string) {
    const maxNum = parseInt(max, 10);
    if (isNaN(maxNum) || maxNum < 1 || maxNum > 10) {
      return {
        success: false,
        message: 'Максимум дожен быть числом от 1 до 10'
      };
    }
    return this.appService.setMaxConcurrentGenerations(maxNum);
  }
}