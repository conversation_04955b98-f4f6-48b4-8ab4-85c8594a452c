/**
 * Система отрисовки паттерновых текстур для озер
 */

import { WorldMapCell } from '../../../../shared/types/World';
import { WorldMapDecorations } from '../../../../shared/enums';
import { PatternTextureManager } from './basePatternTextureManager';

/**
 * Менеджер паттерновых текстур для озер
 */
class LakeTextureManager extends PatternTextureManager {
  /**
   * Получает текстуру озера на основе направлений берегов и номера изображения
   * @param decorationBorder - массив направлений берегов [1, 4] означает папку "1_4"
   * @param imgDirection - номер изображения от 1 до 4
   */
  async getLakePatternTexture(decorationBorder: number[], imgDirection: number): Promise<HTMLImageElement | null> {
    // Фильтруем только основные направления (1-4), игнорируем углы (5-8)
    const filteredBorders = decorationBorder.filter(border => border >= 1 && border <= 4);
    
    // Если нет основных направлений, возвращаем null
    if (filteredBorders.length === 0) {
      return null;
    }

    // Формируем ключ для кэша
    const borderKey = filteredBorders.sort((a, b) => a - b).join('_');
    const textureKey = `lake_${borderKey}_${imgDirection}`;

    // Проверяем кэш
    if (this.textureCache.has(textureKey)) {
      return this.textureCache.get(textureKey)!;
    }

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(textureKey)) {
      return this.loadingPromises.get(textureKey)!;
    }

    // Формируем путь к текстуре
    const texturePath = `/textures/worldMap/decorations/lake/${borderKey}/${imgDirection}.png`;

    // Создаем промис загрузки
    const loadingPromise = this.createLoadingPromise(texturePath, textureKey);
    this.loadingPromises.set(textureKey, loadingPromise);

    try {
      return await loadingPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * Получае�� текстуру угла озера
   * @param cornerType - тип угла: 5 (западный), 6 (северный), 7 (восточный), 8 (южный)
   * @param imgDirection - номер изображения от 1 до 4
   */
  async getLakeCornerTexture(cornerType: number, imgDirection: number): Promise<HTMLImageElement | null> {
    // Проверяем, что это валидный угол
    if (cornerType < 5 || cornerType > 8) {
      return null;
    }

    const textureKey = `lake_corner_${cornerType}_${imgDirection}`;

    // Проверяем кэш
    if (this.textureCache.has(textureKey)) {
      return this.textureCache.get(textureKey)!;
    }

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(textureKey)) {
      return this.loadingPromises.get(textureKey)!;
    }

    // Смещаем значения на 1 по часовой: 5->6, 6->7, 7->8, 8->5
    let mappedCornerType: number;
    switch (cornerType) {
      case 5: mappedCornerType = 6; break; // запад -> север
      case 6: mappedCornerType = 7; break; // север -> восток  
      case 7: mappedCornerType = 8; break; // восток -> юг
      case 8: mappedCornerType = 5; break; // юг -> запад
      default: return null;
    }

    // Формируем п��ть к текстуре угла - используем смещенное значение для папки
    const texturePath = `/textures/worldMap/decorations/lake/${mappedCornerType}/${imgDirection}.png`;

    // Создаем промис загрузки
    const loadingPromise = this.createLoadingPromise(texturePath, textureKey);
    this.loadingPromises.set(textureKey, loadingPromise);

    try {
      return await loadingPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * Синхронно получает текстуру озера (если она уже загружена)
   */
  getLoadedPatternTexture(decorationBorder: number[], imgDirection: number): HTMLImageElement | null {
    // Фильтруем только основные направления (1-4), игнорируем углы (5-8)
    const filteredBorders = decorationBorder.filter(border => border >= 1 && border <= 4);
    
    if (filteredBorders.length === 0) {
      return null;
    }

    const borderKey = filteredBorders.sort((a, b) => a - b).join('_');
    const textureKey = `lake_${borderKey}_${imgDirection}`;
    return this.textureCache.get(textureKey) || null;
  }

  /**
   * Синхронно получает текстуру угла (если она уже загружена)
   */
  getLoadedCornerTexture(cornerType: number, imgDirection: number): HTMLImageElement | null {
    if (cornerType < 5 || cornerType > 8) {
      return null;
    }

    const textureKey = `lake_corner_${cornerType}_${imgDirection}`;
    return this.textureCache.get(textureKey) || null;
  }

  /**
   * Предзагружает все возможные комбинации текстур озер
   */
  async preloadAllLakeTextures(): Promise<void> {
    const borderCombinations = [
      [1], [2], [3], [4],
      [1, 2], [1, 3], [1, 4], [2, 3], [2, 4], [3, 4],
      [1, 2, 3], [2, 3, 4], [3, 4, 1], [1, 2, 4],
      [1, 2, 3, 4]
    ];

    const loadPromises: Promise<HTMLImageElement | null>[] = [];

    // Загружаем основные текстуры озер
    for (const borders of borderCombinations) {
      for (let direction = 1; direction <= 4; direction++) {
        loadPromises.push(this.getLakePatternTexture(borders, direction));
      }
    }

    // Загружаем текстуры углов (5-8)
    for (let cornerType = 5; cornerType <= 8; cornerType++) {
      for (let direction = 1; direction <= 4; direction++) {
        loadPromises.push(this.getLakeCornerTexture(cornerType, direction));
      }
    }

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текст��р озер
export const lakeTextureManager = new LakeTextureManager();