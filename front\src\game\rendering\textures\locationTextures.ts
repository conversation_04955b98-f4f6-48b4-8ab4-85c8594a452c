/**
 * Система загрузки и управления текстурами для локаций
 */

import { TerrainType, LocationDecorations } from '../../../shared/enums';
import { TERRAIN_TEXTURES, TERRAIN_LOCATIONS_TEXTURES } from './terrain';
import { DECORATION_LOCATION_TEXTURES } from './decorations';


// Маппинг типов земли локации на текстуры (обновленный под TerrainType)
export const LOCATION_TERRAIN_TEXTURES: Record<TerrainType, readonly string[]> = {
  [TerrainType.ASPHALT]: TERRAIN_TEXTURES.grass, // нет отдельной текстуры, используем grass
  [TerrainType.BETON]: TERRAIN_LOCATIONS_TEXTURES.beton, // используем специальную текстуру бетона
  [TerrainType.WOOD]: TERRAIN_TEXTURES.grass, // нет отдельной текстуры, используем grass
  [TerrainType.METAL]: TERRAIN_TEXTURES.grass, // нет отдельной текстуры, используем grass
  [TerrainType.GROUND]: TERRAIN_TEXTURES.grass, // используем grass вместо несуществующего desert
  [TerrainType.WATER]: TERRAIN_TEXTURES.water,
  [TerrainType.WASTELAND]: TERRAIN_LOCATIONS_TEXTURES.wasteland
};

// Кэш загруженных изображений
const locationTextureCache = new Map<string, HTMLImageElement>();

// Экспортируем кэш для доступа из других модулей
export { locationTextureCache };

/**
 * Загружает текстуру и кэширует её
 */
export async function loadLocationTexture(src: string): Promise<HTMLImageElement> {
  // Проверяем кэш
  if (locationTextureCache.has(src)) {
    return locationTextureCache.get(src)!;
  }

  // Загружаем новую текстуру
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      locationTextureCache.set(src, img);
      resolve(img);
    };
    img.onerror = () => {
      console.warn(`Не удалось загрузить текстуру локации: ${src}`);
      // Создаем заглушку
      const placeholder = new Image(1, 1);
      locationTextureCache.set(src, placeholder);
      resolve(placeholder);
    };
    img.src = src;
  });
}

/**
 * Получает детерминированную текстуру для типа земли на основе координат
 */
export function getLocationTexture(terrain: TerrainType, x: number, y: number): string | null {
  const textures = LOCATION_TERRAIN_TEXTURES[terrain];
  if (!textures || textures.length === 0) {
    return null;
  }
  // Детерминированный выбор с использованием числа π для лучшего распределения
  const p = (x * Math.PI + y * Math.PI * 2.71828);
  const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
  const index = Math.floor(hash) % textures.length;
  return textures[index];
}

/**
 * Получает случайную текстуру для типа земли (устаревшая функция)
 * @deprecated Используйте getLocationTexture с координатами для стабильности
 */
export function getRandomLocationTexture(terrain: TerrainType): string | null {
  const textures = LOCATION_TERRAIN_TEXTURES[terrain];
  if (!textures || textures.length === 0) {
    return null;
  }
  
  const randomIndex = Math.floor(Math.random() * textures.length);
  return textures[randomIndex];
}

/**
 * Предзагружает все текстуры локаций
 */
export async function preloadLocationTextures(): Promise<void> {
  const allTextures: string[] = [];
  
  // Собираем все уникальные текстуры
  Object.values(LOCATION_TERRAIN_TEXTURES).forEach(textureArray => {
    allTextures.push(...textureArray);
  });
  
  // Убираем дубликаты
  const uniqueTextures = [...new Set(allTextures)];
  
  // Загружаем все текстуры параллельно
  const loadPromises = uniqueTextures.map(texture => loadLocationTexture(texture));
  
  try {
    await Promise.all(loadPromises);
  } catch (error) {
    console.error('Ошибка при предзагрузке текстур локаций:', error);
  }
}

// Кэш загруженных изображений декораций локаций
const locationDecorationTextureCache = new Map<string, HTMLImageElement>();

/**
 * Загружает текстуру декорации локации и кэширует её
 */
export async function loadLocationDecorationTexture(src: string): Promise<HTMLImageElement> {
  if (locationDecorationTextureCache.has(src)) {
    return locationDecorationTextureCache.get(src)!;
  }

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      locationDecorationTextureCache.set(src, img);
      resolve(img);
    };
    img.onerror = () => {
      console.warn(`Не удалось загрузить текстуру декорации локации: ${src}`);
      const placeholder = new Image(1, 1);
      locationDecorationTextureCache.set(src, placeholder);
      resolve(placeholder);
    };
    img.src = src;
  });
}

/**
 * Получает детерминированную текстуру для типа декорации локации на основе координат
 */
export function getLocationDecorationTexture(decoration: LocationDecorations, x: number, y: number): string | null {
  const textures = DECORATION_LOCATION_TEXTURES[decoration];
  if (!textures || textures.length === 0) {
    return null;
  }
  
  // Детерминированный выбор с другой формулой на основе π для разнообразия декораций
  const p = (x * Math.PI * 1.414 + y * Math.PI * 1.732);
  const hash = Math.abs(Math.sin(p * 2.236) * Math.cos(p / 1.618) * 50000); // Умножаем на 50000 для еще большего разнообразия
  const index = Math.floor(hash) % textures.length;
  return textures[index];
}

/**
 * Получает случайную текстуру для типа декорации локации
 * @deprecated Используйте getLocationDecorationTexture с координатами для стабильности
 */
export function getRandomLocationDecorationTexture(decoration: LocationDecorations): string | null {
  const textures = DECORATION_LOCATION_TEXTURES[decoration];
  if (!textures || textures.length === 0) {
    return null;
  }
  
  const randomIndex = Math.floor(Math.random() * textures.length);
  return textures[randomIndex];
}

/**
 * Предзагружает все текстуры декораций локаций
 */
export async function preloadLocationDecorationTextures(): Promise<void> {
  const allTextures: string[] = [];
  
  Object.values(DECORATION_LOCATION_TEXTURES).forEach(textureArray => {
    if (textureArray) allTextures.push(...textureArray);
  });
  
  const uniqueTextures = [...new Set(allTextures)];
  const loadPromises = uniqueTextures.map(texture => loadLocationDecorationTexture(texture));
  
  try {
    await Promise.all(loadPromises);
    console.log('Все текстуры декораций локаций загружены');
  } catch (error) {
    console.error('Ошибка при загрузке текстур декораций локаций:', error);
  }
}
