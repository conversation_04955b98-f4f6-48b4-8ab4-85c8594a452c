// Расширяем Window для глобального флага движения игрока
declare global {
  interface Window {
    __playerIsMoving?: boolean;
  }
}

import { useEffect } from 'react'
import { Position } from '../../../shared/types/Common'
import { WorldMap } from '../../../shared/types/World'
import { createPlayerPathKeyDownHandler } from '../interaction/eventHandlers'

/**
 * Хук для передвижения игрока по пути с задержкой между шагами
 */
export function usePlayerPathMovement(
  playerPosition: Position,
  path: Position[],
  speed?: number,
  currentWorld?: WorldMap | null
) {
  useEffect(() => {
    // Устанавливаем глобальный флаг движения
    const handleKeyDown = createPlayerPathKeyDownHandler(playerPosition, path, speed, currentWorld);
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [playerPosition, path, speed, currentWorld]);
}