# Руководство по работе с shared модулем

## Основные принципы

1. **Модуль shared копируется в каждый сервис** при сборке Docker контейнеров.
2. **НИКОГДА НЕ ИМПОРТИРУЙТЕ** модуль shared из корня проекта.
3. **ВСЕГДА ИСПОЛЬЗУЙТЕ** локальную копию shared из вашего сервиса.

## Расположение модуля shared

В исходном коде модуль shared находится в корне проекта:
```
/shared/
```

После сборки он копируется в директорию `src/shared/` каждого сервиса:
```
back/auth-service/src/shared/
back/game-engine-service/src/shared/
back/save-service/src/shared/
back/world-generator-service/src/shared/
front/src/shared/
```

## Правильный способ импорта

```typescript
// Frontend (React)
import { WorldSettings } from '../shared/types/World';
import { Language } from '../shared/enums';

// Backend (NestJS)
import { WorldSettings } from '../shared/types/World';
import { Language } from '../shared/enums';
```

## Неправильный способ импорта (НИКОГДА ТАК НЕ ДЕЛАЙТЕ)

```typescript
// ❌ НЕПРАВИЛЬНО - ПРИВЕДЕТ К ОШИБКАМ
import { WorldSettings } from '@nuclearstory/shared';
import { Language } from '../../../shared/enums';
```

## Почему это важно?

Docker контейнеры не имеют доступа к корневому модулю shared. 
Каждый сервис имеет только свою локальную копию модуля shared в директории `src/shared/`.

Импорт из корневого модуля может работать в режиме разработки (npm run dev), 
но **гарантированно сломается** в Docker контейнерах.

## Обновление типов

При изменении типов в модуле shared:

1. Внесите изменения в корневой модуль shared
2. Пересоберите все контейнеры с помощью скрипта: `./infra/scripts/build.sh`

**ВАЖНО**: Все сервисы должны иметь одинаковую версию модуля shared.

## Отладка проблем с shared модулем

Если возникают ошибки типа "Cannot find module '../shared/types'" или аналогичные:

1. Проверьте, правильно ли скопирован модуль shared в директорию src/shared вашего сервиса
2. Убедитесь, что вы импортируете типы из локального shared модуля, а не из корневого
3. При необходимости запустите полную пересборку проекта

## Запомните!

> Импорт из корневого shared приведет к НЕОБРАТИМЫМ ошибкам в Docker контейнерах!
> ВСЕГДА используйте локальную копию в src/shared вашего сервиса!
