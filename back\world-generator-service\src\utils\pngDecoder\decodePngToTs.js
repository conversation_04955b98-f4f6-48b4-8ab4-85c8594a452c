#!/usr/bin/env node

// Simple PNG -> TX (JSON) converter
// Usage: node decodePngToTs.js input.png output.tx.json [colorMap.json]

const fs = require('fs');
const path = require('path');
let PNG;
try {
  PNG = require('pngjs').PNG;
} catch (e) {
  console.error('Missing dependency: pngjs. Install with `npm install pngjs`');
  process.exit(2);
}

function toHex(r, g, b) {
  return '#' + [r, g, b].map(x => x.toString(16).padStart(2, '0')).join('').toLowerCase();
}

function loadColorMap(filePath) {
  if (!filePath) return null;
  try {
    const raw = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(raw);
  } catch (e) {
    console.error('Failed to load color map', e.message);
    return null;
  }
}

function defaultColorMap() {
  const p = path.join(__dirname, 'colorMap.json');
  try {
    return JSON.parse(fs.readFileSync(p, 'utf8'));
  } catch (e) {
  console.error('Cannot load default color map:', e.message);
  console.error('Please create or restore "colorMap.json" in the pngDecoder folder with color -> decoration mappings.');
  process.exit(2);
  }
}

function loadTokenLegend() {
  const p = path.join(__dirname, 'tokenLegend.json');
  try {
    return JSON.parse(fs.readFileSync(p, 'utf8'));
  } catch (e) {
    return null;
  }
}

function printUsageAndExit() {
  console.log('Usage: node decodePngToTs.js input.png output.tx.json [colorMap.json]');
  process.exit(1);
}

(async function main() {
  const argv = process.argv.slice(2);

  // New behavior: if first arg is a directory or there are no args, process all .png files in that directory.
  // If two args provided, keep previous behavior: inputPath outputPath [colorMap]
  const mapPath = argv[2];

  function buildOutputPathForInput(inputFile) {
    const dir = path.dirname(inputFile);
    const base = path.basename(inputFile, path.extname(inputFile));
    return path.join(dir, base + '.tx.json');
  }

  function processSingle(inputPath, outputPath, colorMapPath) {
    if (!fs.existsSync(inputPath)) {
      console.error('Input file not found:', inputPath);
      return false;
    }
    const map = loadColorMap(colorMapPath) || defaultColorMap();
    const data = fs.readFileSync(inputPath);
    try {
      const png = PNG.sync.read(data);
      const {width, height, data: buf} = png;
      const tokenLegend = loadTokenLegend();
      const out = {
        width,
        height,
        decorations: [], // full decoration keys per cell
        tokens: [] // human-readable single-char tokens
      };

      for (let y = 0; y < height; y++) {
        const drow = [];
        const trow = [];
        for (let x = 0; x < width; x++) {
          const idx = (width * y + x) << 2;
          const r = buf[idx];
          const g = buf[idx + 1];
          const b = buf[idx + 2];
          const a = buf[idx + 3];

          const hex = a === 0 ? '#000000' : toHex(r, g, b);
          let decoKey = map[hex] || 'other';
          if (decoKey === undefined) decoKey = 'other';
          drow.push(decoKey);

          const token = (tokenLegend && tokenLegend[decoKey]) ? tokenLegend[decoKey] : (String(decoKey)[0] || '?');
          trow.push(token);
        }
        out.decorations.push(drow);
        out.tokens.push(trow);
      }
      fs.writeFileSync(outputPath, formatObjectWithOneLineRows(out), 'utf8');
      console.log('Wrote', outputPath);
      return true;
    } catch (e) {
      console.error('Failed to parse PNG:', inputPath, e.message);
      return false;
    }
  }

  // No args: process all .png files in the script directory
  if (argv.length === 0) {
    const folder = __dirname;
    const files = fs.readdirSync(folder).filter(f => f.toLowerCase().endsWith('.png'));
    if (files.length === 0) {
      console.error('No .png files found in', folder);
      process.exit(1);
    }
    let ok = true;
    for (const f of files) {
      const inPath = path.join(folder, f);
      const outPath = buildOutputPathForInput(inPath);
      const result = processSingle(inPath, outPath, mapPath);
      if (!result) ok = false;
    }
    if (!ok) process.exit(1);
    process.exit(0);
  }

  // If first arg is a directory, process all .png inside it
  const first = argv[0];
  if (fs.existsSync(first) && fs.statSync(first).isDirectory()) {
    const folder = first;
    const files = fs.readdirSync(folder).filter(f => f.toLowerCase().endsWith('.png'));
    if (files.length === 0) {
      console.error('No .png files found in', folder);
      process.exit(1);
    }
    let ok = true;
    for (const f of files) {
      const inPath = path.join(folder, f);
      const outPath = buildOutputPathForInput(inPath);
      const result = processSingle(inPath, outPath, mapPath);
      if (!result) ok = false;
    }
    if (!ok) process.exit(1);
    process.exit(0);
  }

  // Otherwise, if two or more args provided, assume inputPath outputPath [colorMap]
  if (argv.length >= 2) {
    const inputPath = argv[0];
    const outputPath = argv[1];
    const colorMapPath = argv[2];
    const success = processSingle(inputPath, outputPath, colorMapPath);
    if (!success) process.exit(1);
    process.exit(0);
  }

  // If we got here, args are invalid
  printUsageAndExit();
})();

function is2DArray(v) {
  return Array.isArray(v) && v.length > 0 && Array.isArray(v[0]);
}

function formatObjectWithOneLineRows(obj) {
  const keys = Object.keys(obj);
  let parts = [];
  for (let i = 0; i < keys.length; i++) {
    const k = keys[i];
    const v = obj[k];
    if (is2DArray(v)) {
      let rows = v.map(row => {
        const items = row.map(x => JSON.stringify(x)).join(', ');
        return '    [' + items + ']';
      }).join(',\n');
      parts.push(`  "${k}": [\n${rows}\n  ]`);
    } else {
      const str = JSON.stringify(v, null, 2).replace(/\n/g, '\n  ');
      parts.push(`  "${k}": ${str}`);
    }
  }
  return '{\n' + parts.join(',\n') + '\n}\n';
}
