import { Injectable, NotFoundException, Logger } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { WorldEntity } from '../entities/world.entity'
import { WorldMap } from '../shared/types/World'

/**
 * Сервис для управления мирами в базе данных
 */
@Injectable()
export class WorldsService {
  private readonly logger = new Logger(WorldsService.name)

  constructor(
    @InjectRepository(WorldEntity)
    private readonly worldRepository: Repository<WorldEntity>
  ) {}

 
  async saveWorld(
    userId: string,
    world: WorldMap,
    generationTime: number = 0,
    generatedWithAI: boolean = false
  ): Promise<WorldEntity> {
    this.logger.log(`Saving world ${world.id} for user ${userId}`)
    const worldEntity = new WorldEntity()
    worldEntity.id = world.id
    worldEntity.userId = world.userId
    worldEntity.name = world.name
    worldEntity.parameters = world.parameters
    worldEntity.description = world.description
    worldEntity.settings = world.settings
    worldEntity.worldMap = world.worldMap
    worldEntity.player = world.player
    worldEntity.createdAt = world.createdAt
    worldEntity.updatedAt = world.updatedAt
    const savedWorld = await this.worldRepository.save(worldEntity)
    this.logger.log(`World ${world.id} saved with database ID ${savedWorld.id}`)
    return savedWorld
  }
  async getWorldById(worldId: string, userId?: string): Promise<WorldEntity> {
    const world = await this.worldRepository.findOne({ where: { id: worldId } })
    if (!world) {
      throw new NotFoundException(`World ${worldId} not found`)
    }
    if (userId && world.userId !== userId) {
      throw new NotFoundException(`World ${worldId} does not belong to user ${userId}`)
    }
    return world
  }

  
  async getUserWorlds(
    userId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ worlds: WorldEntity[]; total: number; page: number; limit: number }> {
    const [worlds, total] = await this.worldRepository.findAndCount({
      where: { userId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit
    })
    return {
      worlds,
      total,
      page,
      limit
    }
  }

  async updateWorld(
    worldId: string,
    userId: string,
    world: WorldMap
  ): Promise<WorldEntity> {
    this.logger.log(`Updating world ${worldId} for user ${userId}`)
    const existingWorld = await this.worldRepository.findOne({ where: { id: worldId } })
    if (!existingWorld) {
      throw new NotFoundException(`World ${worldId} not found`)
    }
    if (existingWorld.userId !== userId) {
      throw new NotFoundException(`World ${worldId} does not belong to user ${userId}`)
    }
    
    existingWorld.name = world.name
    existingWorld.parameters = world.parameters
    existingWorld.description = world.description
    existingWorld.settings = world.settings
    existingWorld.worldMap = world.worldMap
    existingWorld.player = world.player
    existingWorld.updatedAt = new Date()
    
    const updatedWorld = await this.worldRepository.save(existingWorld)
    this.logger.log(`World ${worldId} updated successfully`)
    return updatedWorld
  }

  async deleteWorld(worldId: string, userId: string): Promise<void> {
    const world = await this.worldRepository.findOne({ where: { id: worldId } })
    if (!world || world.userId !== userId) {
      throw new Error(`World ${worldId} not found or doesn't belong to user ${userId}`)
    }
    await this.worldRepository.remove(world)
    this.logger.log(`World ${worldId} permanently deleted by user ${userId}`)
  }


}
