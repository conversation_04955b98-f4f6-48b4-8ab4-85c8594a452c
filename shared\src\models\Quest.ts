import { IsString, IsEnum, IsOptional, IsArray, IsNumber, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { QuestStatus, QuestType } from '../enums';
import { GameStats } from '../types';

export class QuestObjective {
  @IsString()
  id: string;

  @IsString()
  description: string;

  @IsBoolean()
  completed: boolean;

  @IsOptional()
  @IsNumber()
  targetCount?: number;

  @IsOptional()
  @IsNumber()
  currentCount?: number;

  constructor(partial: Partial<QuestObjective> = {}) {
    Object.assign(this, partial);
  }
}

export class QuestReward {
  @IsOptional()
  @IsNumber()
  experience?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  items?: string[];

  @IsOptional()
  stats?: Partial<GameStats>;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  unlockQuests?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  unlockLocations?: string[];

  constructor(partial: Partial<QuestReward> = {}) {
    Object.assign(this, partial);
  }
}

export class Quest {
  @IsString()
  id: string;

  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsEnum(QuestType)
  type: QuestType;

  @IsEnum(QuestStatus)
  status: QuestStatus;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestObjective)
  objectives: QuestObjective[];

  @IsOptional()
  @ValidateNested()
  @Type(() => QuestReward)
  reward?: QuestReward;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @IsOptional()
  @IsNumber()
  timeLimit?: number; // in minutes

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsNumber()
  priority?: number;

  constructor(partial: Partial<Quest> = {}) {
    Object.assign(this, partial);
  }
}

export class CreateQuestDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsEnum(QuestType)
  type: QuestType;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestObjective)
  objectives: QuestObjective[];

  @IsOptional()
  @ValidateNested()
  @Type(() => QuestReward)
  reward?: QuestReward;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @IsOptional()
  @IsNumber()
  timeLimit?: number;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsNumber()
  priority?: number;
}

export class UpdateQuestDto {
  @IsOptional()
  @IsEnum(QuestStatus)
  status?: QuestStatus;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestObjective)
  objectives?: QuestObjective[];
}
