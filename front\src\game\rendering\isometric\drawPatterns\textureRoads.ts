/**
 * Система отрисовки паттерновых текстур для дорог
 */

import { PatternTextureManager } from './basePatternTextureManager';

/**
 * Менеджер паттерновых текстур для дорог
 */
class RoadsTextureManager extends PatternTextureManager {
  /**
   * Обрабатывает направления дорог
   * Числа означают стороны где НЕТУ стыка (там другой декоратор или его нет)
   * @param decorationBorder - массив направлений где нет стыка
   * @returns обработанный массив направлений или "0" для перекрестка
   */
  private processRoadDirections(decorationBorder: number[]): string {
    // Фильтруем только основные направления (1-4), игнорируем углы (5-8)
    const filteredBorders = decorationBorder.filter(border => border >= 1 && border <= 4);
    
    // Если массив пустой - используем перекресток из папки "0"
    if (filteredBorders.length === 0) {
      return "0";
    }

    // Если есть все 4 направления (1_2_3_4)  случайная дорога
    if (filteredBorders.length === 4 && 
        filteredBorders.includes(1) && filteredBorders.includes(2) && 
        filteredBorders.includes(3) && filteredBorders.includes(4)) {
      return "0"; 
    }

    // Если 3 направления - это тупик, заменяем на прямую дорогу
    if (filteredBorders.length === 3) {
      const sorted = filteredBorders.sort((a, b) => a - b);
      
      // Определяем какую прямую дорогу использовать вместо тупика
      if (sorted.includes(2) && sorted.includes(3) && sorted.includes(4)) {
        // 2_3_4 -> прямая дорога 2_4 (вертикальная)
        return "2_4";
      } else if (sorted.includes(1) && sorted.includes(3) && sorted.includes(4)) {
        // 1_3_4 -> прямая дорога 1_3 (горизонтальная)
        return "1_3";
      } else if (sorted.includes(1) && sorted.includes(2) && sorted.includes(4)) {
        // 1_2_4 -> прямая дорога 1_3 (гори��онтальная)
        return "2_4";
      } else if (sorted.includes(1) && sorted.includes(2) && sorted.includes(3)) {
        // 1_2_3 -> прямая дорога 2_4 (вертикальная)
        return "1_3";
      }
    }

    // Для остальных случаев формируем ключ из отсортированных направлений
    return filteredBorders.sort((a, b) => a - b).join('_');
  }

  /**
   * Получает текстуру дороги на основе направлений и номера изображения
   * @param decorationBorder - массив направлений где нет стыка
   * @param imgDirection - номер изображения от 1 до 4
   */
  async getRoadPatternTexture(decorationBorder: number[], imgDirection: number): Promise<HTMLImageElement | null> {
    // Обрабатываем направления для дорог
    const borderKey = this.processRoadDirections(decorationBorder);
    const textureKey = `road_${borderKey}_${imgDirection}`;

    // Проверяем кэш
    if (this.textureCache.has(textureKey)) {
      return this.textureCache.get(textureKey)!;
    }

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(textureKey)) {
      return this.loadingPromises.get(textureKey)!;
    }

    // Формируем путь к текстуре
    const texturePath = `/textures/worldMap/decorations/road/${borderKey}/${imgDirection}.png`;

    // Создаем промис загрузки
    const loadingPromise = this.createLoadingPromise(texturePath, textureKey);
    this.loadingPromises.set(textureKey, loadingPromise);

    try {
      return await loadingPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * Синхронно получает текстуру дороги (если она уже загружена)
   */
  getLoadedRoadTexture(decorationBorder: number[], imgDirection: number): HTMLImageElement | null {
    // Обрабатываем направления для дорог
    const borderKey = this.processRoadDirections(decorationBorder);
    const textureKey = `road_${borderKey}_${imgDirection}`;
    return this.textureCache.get(textureKey) || null;
  }

  /**
   * Предзагружает все возможные комбинации текстур дорог
   */
  async preloadAllRoadTextures(): Promise<void> {
    // Все возможные комбинации направлений где нет стыка
    const borderCombinations = [
      [], // пустой массив -> перекресток "0"
      [1], [2], [3], [4], // одиночные направления
      [1, 2], [1, 3], [1, 4], [2, 3], [2, 4], [3, 4], // двойные комбинации
      [1, 2, 3], [1, 2, 4], [1, 3, 4], [2, 3, 4], // тройные комбинации
      [1, 2, 3, 4] // все направления -> перекресток "0"
    ];

    const loadPromises: Promise<HTMLImageElement | null>[] = [];

    // Загружаем текстуры дорог
    for (const borders of borderCombinations) {
      for (let direction = 1; direction <= 4; direction++) {
        loadPromises.push(this.getRoadPatternTexture(borders, direction));
      }
    }

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текстур дорог
export const roadsTextureManager = new RoadsTextureManager();