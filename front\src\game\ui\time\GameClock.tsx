/**
 * Компонент часов в стиле окна с зумом
 */

import React, { useState } from 'react';
import { useGameTime } from '../../rendering/hooks';
import { formatGameTime, isDaytime, getLightLevel } from '../../utils/time/gameTime';
import styles from './GameClock.module.css';

interface GameClockProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GameClock: React.FC<GameClockProps> = ({ isOpen, onClose }) => {
  const [zoom, setZoom] = useState(1);
  const gameTime = useGameTime();
  
  if (!isOpen) return null;
  
  const isDay = isDaytime(gameTime);
  const lightLevel = getLightLevel(gameTime);
  
  // Преобразуем время в углы для стрелок
  const hourAngle = ((gameTime.hour % 12) * 30) + (gameTime.minute * 0.5); // 30° за час + минутная коррекция
  const minuteAngle = gameTime.minute * 6; // 6° за минуту
  
  const clockSize = 200 * zoom;
  const centerX = clockSize / 2;
  const centerY = clockSize / 2;
  
  // Генерируем часовые метки
  const hourMarks = Array.from({ length: 12 }, (_, i) => {
    const angle = (i * 30) - 90; // -90 для поворота на 12 часов вверх
    const isMainHour = [0, 3, 6, 9].includes(i); // Основные часы (12, 3, 6, 9)
    const radius = clockSize * 0.35;
    const markRadius = clockSize * (isMainHour ? 0.4 : 0.38);
    
    return {
      x1: centerX + Math.cos(angle * Math.PI / 180) * radius,
      y1: centerY + Math.sin(angle * Math.PI / 180) * radius,
      x2: centerX + Math.cos(angle * Math.PI / 180) * markRadius,
      y2: centerY + Math.sin(angle * Math.PI / 180) * markRadius,
      hour: i === 0 ? 12 : i,
      isMain: isMainHour
    };
  });
  
  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.window} onClick={(e) => e.stopPropagation()}>
        <div className={styles.titleBar}>
          <div className={styles.titleBarContent}>
            <span className={styles.windowTitle}>Игровые часы</span>
            <div className={styles.windowControls}>
              <button className={styles.controlButton} onClick={onClose}>
                ✕
              </button>
            </div>
          </div>
        </div>
        
        <div className={styles.content}>
          <div className={styles.zoomControls}>
            <label className={styles.zoomLabel}>
              Масштаб: {Math.round(zoom * 100)}%
            </label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={zoom}
              onChange={(e) => setZoom(parseFloat(e.target.value))}
              className={styles.zoomSlider}
            />
          </div>
          
          <div className={styles.clockContainer}>
            <svg 
              width={clockSize} 
              height={clockSize} 
              className={styles.clock}
              style={{ 
                filter: isDay ? 'none' : `brightness(${lightLevel})` 
              }}
            >
              {/* Фон часов */}
              <circle
                cx={centerX}
                cy={centerY}
                r={clockSize * 0.45}
                fill="url(#clockGradient)"
                stroke="#333"
                strokeWidth="3"
              />
              
              {/* Градиент для фона */}
              <defs>
                <radialGradient id="clockGradient">
                  <stop offset="0%" stopColor="#f5f5f5" />
                  <stop offset="100%" stopColor="#ddd" />
                </radialGradient>
                <filter id="shadow">
                  <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.3"/>
                </filter>
              </defs>
              
              {/* Часовые метки */}
              {hourMarks.map((mark, i) => (
                <g key={i}>
                  <line
                    x1={mark.x1}
                    y1={mark.y1}
                    x2={mark.x2}
                    y2={mark.y2}
                    stroke="#333"
                    strokeWidth={mark.isMain ? "3" : "1"}
                  />
                  {mark.isMain && (
                    <text
                      x={centerX + Math.cos(((i * 30) - 90) * Math.PI / 180) * clockSize * 0.3}
                      y={centerY + Math.sin(((i * 30) - 90) * Math.PI / 180) * clockSize * 0.3}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize={clockSize * 0.08}
                      fill="#333"
                      fontWeight="bold"
                    >
                      {mark.hour}
                    </text>
                  )}
                </g>
              ))}
              
              {/* Часовая стрелка */}
              <line
                x1={centerX}
                y1={centerY}
                x2={centerX + Math.cos((hourAngle - 90) * Math.PI / 180) * clockSize * 0.2}
                y2={centerY + Math.sin((hourAngle - 90) * Math.PI / 180) * clockSize * 0.2}
                stroke="#333"
                strokeWidth="4"
                strokeLinecap="round"
                filter="url(#shadow)"
              />
              
              {/* Минутная стрелка */}
              <line
                x1={centerX}
                y1={centerY}
                x2={centerX + Math.cos((minuteAngle - 90) * Math.PI / 180) * clockSize * 0.35}
                y2={centerY + Math.sin((minuteAngle - 90) * Math.PI / 180) * clockSize * 0.35}
                stroke="#666"
                strokeWidth="2"
                strokeLinecap="round"
                filter="url(#shadow)"
              />
              
              {/* Центральная точка */}
              <circle
                cx={centerX}
                cy={centerY}
                r={clockSize * 0.03}
                fill="#333"
                filter="url(#shadow)"
              />
              
              {/* Индикатор дня/ночи */}
              <circle
                cx={clockSize * 0.8}
                cy={clockSize * 0.2}
                r={clockSize * 0.08}
                fill={isDay ? "#FFD700" : "#4169E1"}
                stroke="#333"
                strokeWidth="1"
              />
              <text
                x={clockSize * 0.8}
                y={clockSize * 0.2}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize={clockSize * 0.06}
                fill="#fff"
              >
                {isDay ? "☀" : "🌙"}
              </text>
            </svg>
          </div>
          
          <div className={styles.timeInfo}>
            <div className={styles.digitalTime}>
              {gameTime.hour.toString().padStart(2, '0')}:
              {gameTime.minute.toString().padStart(2, '0')}
            </div>
            <div className={styles.gameInfo}>
              <div className={styles.dayInfo}>
                День {gameTime.day} • {gameTime.season === 'spring' ? '🌸 Весна' : 
                                        gameTime.season === 'summer' ? '☀️ Лето' : 
                                        gameTime.season === 'autumn' ? '🍂 Осень' : '❄️ Зима'}
              </div>
              <div className={styles.lightInfo}>
                {isDay ? '☀️ День' : '🌙 Ночь'} • Освещение: {Math.round(lightLevel * 100)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
