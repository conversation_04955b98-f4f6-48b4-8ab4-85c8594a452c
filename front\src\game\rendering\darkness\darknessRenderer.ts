/**
 * Система рендеринга затемнения в локациях
 */

import { Position } from '../../../shared/types/Common';
import { getVisionRadius } from '../../systems/location/visionSystem';
import { gameTimeManager } from '../../utils/time/gameTimeManager';
import { getLightColor, isDaytime, getLightLevel } from '../../utils/time/gameTime';

/**
 * Применяет затемнение к клетке в зависимости от видимости с плавным ореолом
 * @param ctx - контекст canvas
 * @param centerX - центр X клетки на экране
 * @param centerY - центр Y клетки на экране
 * @param tileWidth - ширина клетки
 * @param tileHeight - высота клетки
 * @param playerPosition - позиция игрока
 * @param tilePosition - позиция клетки
 * @param perception - характеристика восприятия игрока
 */
export function applyDarknessToTile(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  tileWidth: number,
  tileHeight: number,
  playerPosition: Position,
  tilePosition: Position,
  perception: number
): void {
  const currentTime = gameTimeManager.getCurrentTime();
  
  // Днем нет затемнения
  if (isDaytime(currentTime)) {
    return;
  }
  
  const visionRadius = getVisionRadius(perception, currentTime);
  const distance = Math.sqrt(
    Math.pow(tilePosition.x - playerPosition.x, 2) + 
    Math.pow(tilePosition.y - playerPosition.y, 2)
  );
  
  let darknessLevel = 0;
  
  // Полная видимость в центральной зоне
  if (distance <= visionRadius) {
    return; // Нет затемнения
  }
  
  // Плавный переход в ореоле (2 клетки)
  const haloRadius = 2;
  if (distance <= visionRadius + haloRadius) {
    // Плавный градиент от 0 до максимального затемнения
    const haloProgress = (distance - visionRadius) / haloRadius;
    const lightLevel = getLightLevel(currentTime);
    darknessLevel = haloProgress * (1 - lightLevel);
  } else {
    // Полное затемнение за пределами ореола
    const lightLevel = getLightLevel(currentTime);
    darknessLevel = 1 - lightLevel;
  }
  
  if (darknessLevel <= 0) {
    return;
  }
  
  // Сохраняем текущее состояние контекста
  ctx.save();
  
  // Применяем затемнение с плавным градиентом для ореола
  if (distance <= visionRadius + 2) {
    // Создаем радиальный градиент для плавного перехода
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, tileWidth
    );
    
    const haloProgress = Math.max(0, Math.min(1, (distance - visionRadius) / 2));
    gradient.addColorStop(0, `rgba(0, 0, 0, ${darknessLevel * 0.3})`);
    gradient.addColorStop(1, `rgba(0, 0, 0, ${darknessLevel * 0.8})`);
    
    ctx.globalAlpha = 1;
    ctx.fillStyle = gradient;
  } else {
    // Обычное затемнение для дальних клеток
    ctx.globalAlpha = darknessLevel * 0.9;
    ctx.fillStyle = '#000000';
  }
  
  // Рисуем ромб затемнения (как форма изометрической клетки)
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - tileHeight / 2); // Верх
  ctx.lineTo(centerX + tileWidth / 2, centerY); // Право
  ctx.lineTo(centerX, centerY + tileHeight / 2); // Низ
  ctx.lineTo(centerX - tileWidth / 2, centerY); // Лево
  ctx.closePath();
  ctx.fill();
  
  // Восстанавливаем состояние контекста
  ctx.restore();
}

/**
 * Применяет цветовой фильтр времени дня ко всей локации
 * @param ctx - контекст canvas
 * @param canvasWidth - ширина canvas
 * @param canvasHeight - высота canvas
 */
export function applyTimeOfDayFilter(
  ctx: CanvasRenderingContext2D,
  canvasWidth: number,
  canvasHeight: number
): void {
  const currentTime = gameTimeManager.getCurrentTime();
  const lightColor = getLightColor(currentTime);
  
  // Если нет цветового фильтра (день), ничего не делаем
  if (lightColor.a <= 0) {
    return;
  }
  
  // Сохраняем текущее состояние контекста
  ctx.save();
  
  // Применяем цветовой фильтр ко всему экрану
  ctx.globalAlpha = lightColor.a;
  ctx.fillStyle = `rgb(${lightColor.r}, ${lightColor.g}, ${lightColor.b})`;
  ctx.globalCompositeOperation = 'multiply'; // Эффект смешивания для естественного освещения
  
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);
  
  // Восстанавливаем состояние контекста
  ctx.restore();
}

/**
 * Рендерит индикатор времени в углу экрана
 * @param ctx - контекст canvas
 * @param x - позиция X
 * @param y - позиция Y
 */
export function renderTimeIndicator(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number
): void {
  const currentTime = gameTimeManager.getCurrentTime();
  
  // Сохраняем состояние контекста
  ctx.save();
  
  // Настройки текста
  ctx.font = '16px Arial';
  ctx.fillStyle = '#ffffff';
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = 2;
  
  // Форматируем время
  const timeText = `День ${currentTime.day}, ${currentTime.hour.toString().padStart(2, '0')}:${currentTime.minute.toString().padStart(2, '0')}`;
  
  // Рисуем обводку текста
  ctx.strokeText(timeText, x, y);
  
  // Рисуем сам текст
  ctx.fillText(timeText, x, y);
  
  // Добавляем индикатор дня/ночи
  const isDay = currentTime.hour >= 6 && currentTime.hour < 18;
  const dayNightText = isDay ? '☀️ День' : '🌙 Ночь';
  
  ctx.font = '14px Arial';
  ctx.strokeText(dayNightText, x, y + 20);
  ctx.fillText(dayNightText, x, y + 20);
  
  // Восстанавливаем состояние контекста
  ctx.restore();
}

/**
 * Рендерит зону видимости вокруг игрока (для дебага)
 * @param ctx - контекст canvas
 * @param playerScreenPos - позиция игрока на экране
 * @param visionRadius - радиус видимости в клетках
 * @param tileWidth - ширина клетки
 * @param tileHeight - высота клетки
 */
export function renderVisionRadius(
  ctx: CanvasRenderingContext2D,
  playerScreenPos: { x: number; y: number },
  visionRadius: number,
  tileWidth: number,
  tileHeight: number
): void {
  // Сохраняем состояние контекста
  ctx.save();
  
  // Настройки круга видимости
  ctx.globalAlpha = 0.2;
  ctx.strokeStyle = '#ffff00';
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);
  
  // Рисуем круг видимости
  const radiusInPixels = visionRadius * (tileWidth + tileHeight) / 4; // Приблизительный расчет
  ctx.beginPath();
  ctx.arc(playerScreenPos.x, playerScreenPos.y, radiusInPixels, 0, Math.PI * 2);
  ctx.stroke();
  
  // Восстанавливаем состояние контекста
  ctx.restore();
}
