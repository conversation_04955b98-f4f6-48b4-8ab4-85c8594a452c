import { useState, useEffect, useRef } from 'react'
import { WorldMap } from '../../../shared/types/World'
import { isoToScreen } from '../../utils/coordinates/isometric'
import { CAMERA_UI_UPDATE_INTERVAL } from '../../utils/constants/rendering'
import { CameraRef, createKeyDownHandler } from '../interaction/eventHandlers'
import { useGameStore } from '@/game/store/gameStore'

/**
 * Хук для центрирования камеры на карте ТОЛЬКО при загрузке мира
 */
export const useCameraCenter = (
  currentWorld: WorldMap | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number
) => {
  const worldIdRef = useRef<string | null>(null)

  useEffect(() => {
    if (!cameraRef.current || !currentWorld) return

    // Центрируем камеру ТОЛЬКО при смене мира (новый worldId)
    const currentWorldId = currentWorld.id || 'default'
    if (worldIdRef.current === currentWorldId) return // Мир не изменился - не центрируем

    worldIdRef.current = currentWorldId

    // центр карты в тайлах (по изометрии)
    const mapSize = currentWorld.settings?.worldSize || 20
    const centerTile = (mapSize - 1) / 2

    // конвертим в экранные координаты центр карты
    const centerScreen = isoToScreen(centerTile, centerTile, tileWidth, tileHeight)

    // ставим камеру так, чтобы центр карты был в середине канваса
    cameraRef.current.x = centerScreen.x
    cameraRef.current.y = centerScreen.y

  }, [currentWorld, tileWidth, tileHeight]) // Убираем draw из зависимостей!
}

/**
 * Хук для обновления UI камеры
 */
export const useCameraUI = (
  cameraRef: React.RefObject<CameraRef>,
  setCameraUI: (camera: { x: number; y: number }) => void
) => {
  useEffect(() => {
    const interval = setInterval(() => {
      if (cameraRef.current) {
        setCameraUI({
          x: Math.round(cameraRef.current.x),
          y: Math.round(cameraRef.current.y)
        })
      }
    }, CAMERA_UI_UPDATE_INTERVAL)

    return () => clearInterval(interval)
  }, [cameraRef, setCameraUI])
}

/**
 * Хук для обработки клавиш камеры
 */
export const useKeyboardControls = (cameraRef: React.RefObject<CameraRef>) => {
  useEffect(() => {
    const handleKeyDown = createKeyDownHandler(cameraRef)
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [cameraRef])
}

/**
 * Функция для перемещения камеры на игрока
 */
export const moveCameraToPlayer = (
  currentWorld: WorldMap | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number,
  // optional currentLocation for interior rendering; prefer its local playerPosition if provided
  currentLocation?: { playerPosition?: { x: number; y: number } } | null
) => {
  if (!cameraRef.current) return

  // Prefer in-location player position when player is inside a location
  const store = useGameStore.getState()
  const playerLocationPresent = store.playerLocationPresent
  const storeLocation = store.currentLocation

  let playerPosition = currentWorld?.player?.position
  if (playerLocationPresent) {
    // priority: explicitly provided currentLocation -> store.currentLocation -> fallback to world
    playerPosition = currentLocation?.playerPosition ?? storeLocation?.playerPosition ?? playerPosition
  } else {
    playerPosition = currentWorld?.player?.position ?? playerPosition
  }

  if (!playerPosition) return

  // Конвертируем позицию игрока в экранные координаты
  const playerScreen = isoToScreen(playerPosition.x, playerPosition.y, tileWidth, tileHeight)

  // Перемещаем камеру на игрока
  cameraRef.current.x = playerScreen.x
  cameraRef.current.y = playerScreen.y
}

/**
 * Хук для создания функции перемещения камеры на игрока
 */
export const useCameraToPlayer = (
  currentWorld: WorldMap | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number,
  currentLocation?: { playerPosition?: { x: number; y: number } } | null
) => {
  return () => moveCameraToPlayer(currentWorld, cameraRef, tileWidth, tileHeight, currentLocation)
}