/**
 * React хук для работы с игровым временем
 */

import { useState, useEffect } from 'react';
import { GameTime } from './gameTime';
import { gameTimeManager, TimeUpdateCallback } from './gameTimeManager';

/**
 * Хук для получения текущего игрового времени
 * Автоматически обновляется при изменении времени
 */
export function useGameTime(): GameTime {
  const [gameTime, setGameTime] = useState<GameTime>(gameTimeManager.getCurrentTime());
  
  useEffect(() => {
    const updateCallback: TimeUpdateCallback = (newTime: GameTime) => {
      setGameTime(newTime);
    };
    
    // Подписываемся на изменения времени
    gameTimeManager.addTimeUpdateCallback(updateCallback);
    
    // Получаем актуальное время при подключении
    setGameTime(gameTimeManager.getCurrentTime());
    
    // Отписываемся при размонтировании
    return () => {
      gameTimeManager.removeTimeUpdateCallback(updateCallback);
    };
  }, []);
  
  return gameTime;
}

/**
 * Хук для управления игровым временем
 */
export function useGameTimeControl() {
  const [isRunning, setIsRunning] = useState(gameTimeManager.isTimeRunning());
  
  const start = () => {
    gameTimeManager.start();
    setIsRunning(true);
  };
  
  const stop = () => {
    gameTimeManager.stop();
    setIsRunning(false);
  };
  
  const setTime = (newTime: GameTime) => {
    gameTimeManager.setTime(newTime);
  };
  
  const fastForward = (minutes: number) => {
    gameTimeManager.fastForward(minutes);
  };
  
  return {
    isRunning,
    start,
    stop,
    setTime,
    fastForward,
    getCurrentTime: () => gameTimeManager.getCurrentTime()
  };
}
