@echo off
setlocal EnableDelayedExpansion
REM Simple launcher for PNG -> TX decoder (placed in pngDecoder folder)
REM Usage: start-decode.bat input.png output.tx.json [colorMap.json]

if "%*"=="" (
  REM No args: run decoder in folder mode (process all .png in this folder)
  echo No args provided — will process all .png files in "%~dp0"
  echo Running: node "%~dp0decodePngToTs.js"
  node "%~dp0decodePngToTs.js"
  exit /b %ERRORLEVEL%
)

REM Script path (decodePngToTs.js is in the same folder)
set SCRIPT_PATH=%~dp0decodePngToTs.js

:run
REM Diagnostic: print node version and the exact command we will run
node --version 2>&1
echo Command:
echo   node "%~dp0decodePngToTs.js" "%IN%" "%OUT%" "%CM%"

if not exist "%~dp0decodePngToTs.js" (
  echo ERROR: decoder script not found: "%~dp0decodePngToTs.js"
  dir "%~dp0"
  pause
  exit /b 2
)

REM Quick command: start-decode.bat dct [file.json]
REM Usage: dct formats provided JSON (or defaults to _out.json) using line.js and exits
if /I "%~1"=="dct" (
  set "INFILE=%~2"
  if "%INFILE%"=="" set "INFILE=%~dp0_out.json"
  if not exist "%INFILE%" (
    echo Target JSON not found: "%INFILE%"
    exit /b 1
  )
  rem decodePngToTs.js now formats output itself; nothing else to do for dct
  echo Formatting assumed handled by embedded formatter in decodePngToTs.js; nothing to run here.
  exit /b 0
)

if defined IN (
  node "%~dp0decodePngToTs.js" "%IN%" "%OUT%" "%CM%"
) else (
  node "%~dp0decodePngToTs.js" "%~1" "%~2" "%~3"
)

REM Check output
if exist "%OUT%" (
  echo Output file created: "%OUT%"
) else (
  echo ERROR: output file was not created: "%OUT%"
  dir "%~dp0"
  echo --- decodePngToTs.js (head) ---
  more +0 "%~dp0decodePngToTs.js"
  pause
  exit /b 3
)

if errorlevel 1 (
  echo Decoder returned non-zero, skipping formatting.
  pause
  exit /b 1
)

echo Decoder finished successfully.
exit /b 0
