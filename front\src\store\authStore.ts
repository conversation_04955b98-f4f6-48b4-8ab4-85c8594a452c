import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { authApi, type AuthResponse, type UserProfile } from '../api/authApi'

interface User {
  id: string
  email: string
  username?: string
  role?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (email: string, password: string) => Promise<void>
  signup: (email: string, username: string, password: string) => Promise<void>
  logout: () => void
  setUser: (user: User) => void
  setToken: (token: string) => void
  updateUser: (updates: Partial<User>) => void
  checkAuth: () => Promise<void>
  refreshProfile: () => Promise<void>
}

// Store для авторизации
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        
        try {
          const response: AuthResponse = await authApi.login({ email, password })
          
          const user: User = {
            id: response.user.id,
            email: response.user.email,
            username: response.user.username,
            role: response.user.role
          }
          
          set({
            user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      signup: async (email: string, username: string, password: string) => {
        set({ isLoading: true })
        
        try {
          const response: AuthResponse = await authApi.register({ email, username, password })
          
          const user: User = {
            id: response.user.id,
            email: response.user.email,
            username: response.user.username,
            role: response.user.role
          }
          
          set({
            user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true })
      },

      setToken: (token: string) => {
        set({ token })
      },

      updateUser: (updates: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({ user: { ...user, ...updates } })
        }
      },

      // Проверка авторизации при загрузке приложения
      checkAuth: async () => {
        const { token } = get()
        if (!token) {
          set({ isAuthenticated: false, user: null })
          return
        }

        try {
          const isValid = await authApi.verifyToken(token)
          if (isValid) {
            // Если токен действителен, обновляем профиль
            await get().refreshProfile()
          } else {
            // Если токен недействителен, очищаем состояние
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false
            })
          }
        } catch (error) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })
        }
      },

      // Обновление профиля пользователя
      refreshProfile: async () => {
        const { token } = get()
        if (!token) return

        try {
          const profile = await authApi.getProfile(token)
          const user: User = {
            id: profile.id,
            email: profile.email,
            username: profile.username,
            role: profile.role
          }
          
          set({ user, isAuthenticated: true })
        } catch (error) {
          // При ошибке обновления профиля, возможно токен истёк
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })
        }
      }
    }),
    {
      name: 'nuclear-story-auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
