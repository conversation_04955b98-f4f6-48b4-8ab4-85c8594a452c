/**
 * Генерирует уникальный ID с префиксом
 */
export function generateId(prefix: string = 'id'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * Генерирует ID для игроков
 */
export function generatePlayerId(): string {
  return generateId('player')
}

/**
 * Генерирует ID для объектов на карте
 */
export function generateObjectId(): string {
  return generateId('obj')
}

/**
 * Генерирует ID для локаций
 */
export function generateLocationId(): string {
  return generateId('loc')
}

/**
 * Генерирует ID для квестов
 */
export function generateQuestId(): string {
  return generateId('quest')
}

/**
 * Генерирует UUID v4 (для совместимости)
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}