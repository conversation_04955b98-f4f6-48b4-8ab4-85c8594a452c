import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { generateForestAsync } from './forestGenerator';
import { generateSwampAsync } from './swampGenerator';
import { generateRuinsAsync } from './ruinsGenerator';
import { generateRubbleAsync } from './rubbleGenerator';
import { generatePatternDecorationsAsync } from './generatePatternDecorationsAsync';
import { generateStableDecorationsAsync } from './stableDecorationGenerator';
import { WorldMapDecorations } from 'src/shared';
import { 
  ProgressTracker, 
  CancellationToken, 
  processBatch 
} from '../../utils/asyncUtils';

// Асинхронная генерация декораций - НОВАЯ СТАБИЛЬНАЯ СИСТЕМА
export async function generateDecorationsAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  
  // НОВАЯ СИСТЕМА: Стабильная генерация на основе размера карты
  // Этап 1: Стабильная генерация алгоритмических декораций (лес, кусты, болота, руины, мусор)
  progressTracker.updateStageProgress(0, 'Стабильная генерация декораций');
  
  // Создаем вложенный ProgressTracker для стабильной генерации
  const stableProgressTracker = new ProgressTracker((progress) => {
    progressTracker.updateStageProgress(
      progress.progress * 0.8, // 0-80% диапазон для стабильной генерации
      `Стабильная генерация: ${progress.currentOperation}`
    );
  });
  
  stableProgressTracker.addStage('Стабильная генера��ия декораций', 1);

  await generateStableDecorationsAsync(
    grid, 
    worldSize, 
    rng, 
    stableProgressTracker,
    cancellationToken
  );

  // Этап 2: Шаблонные декорации (горы, реки, озера) - только если есть маркеры
  progressTracker.updateStageProgress(80, 'Поиск маркеров для шаблонной генерации');
  
  // Находим террариан маркеры для шаблонной генерации
  const markers: Position[] = [];
  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      if (grid[cellKey].terrarianMarker === 1) {
        markers.push({ x, y });
      }
      
      processedCells++;
      // Освобождаем event loop каждые 2000 ячеек
      if (processedCells % 2000 === 0) {
        progressTracker.updateStageProgress(
          80 + (processedCells / totalCells) * 5, 
          `Поиск маркеров: ${processedCells}/${totalCells}`
        );
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }


  if (markers.length > 0) {
    // Этап 3: Шаблонные декорации (горы, реки, озера)
    progressTracker.updateStageProgress(85, 'Генерация шаблонных декораций (горы, реки, озера)');

    // Создаем вложенный ProgressTracker для шаблонных декораций
    const patternProgressTracker = new ProgressTracker((progress) => {
      progressTracker.updateStageProgress(
        85 + (progress.progress * 0.15), // 85-100% диапазон для шаблонов
        `Шаблоны: ${progress.currentOperation}`
      );
    });
    
    patternProgressTracker.addStage('Шаблонные декорации', 1);

    await generatePatternDecorationsAsync(
      grid, 
      worldSize, 
      rng, 
      patternProgressTracker,
      cancellationToken
    );
  } else {
  }

  progressTracker.updateStageProgress(100, 'Все декорации сгенерированы');
}

// СТАРАЯ СИСТЕМА (оставляем для совместимости, но переименовываем)
export async function generateDecorationsLegacyAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  progressTracker: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  // Находим все маркеры асинхронно
  const markers: Position[] = [];
  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      if (grid[cellKey].terrarianMarker === 1) {
        markers.push({ x, y });
      }
      
      processedCells++;
      // Освобождаем event loop каждые 1000 ячеек
      if (processedCells % 1000 === 0) {
        progressTracker.updateStageProgress(
          (processedCells / totalCells) * 10, 
          `Поиск маркеров: ${processedCells}/${totalCells}`
        );
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }


  if (markers.length === 0) {
    progressTracker.updateStageProgress(100, 'Маркеры не найдены, пропускаем генерацию декораций');
    return;
  }

  // Этап 1: Алгоритмические декорации
  const algorithmicStages = [
    { name: 'Лес', generator: generateForestAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.FOREST },
    { name: 'Болота', generator: generateSwampAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.SWAMP },
    { name: 'Мусор', generator: generateRubbleAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.RUBBLE },
    { name: 'Руины', generator: generateRuinsAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.RUINS },
  ];

  // Проходим по алгоритмическим декорациям
  for (let stageIndex = 0; stageIndex < algorithmicStages.length; stageIndex++) {
    const stage = algorithmicStages[stageIndex];
    
    progressTracker.updateStageProgress(
      10 + (stageIndex / (algorithmicStages.length + 1)) * 80, 
      `Генерация декораций: ${stage.name}`
    );

    const startIndex = Math.floor(rng() * markers.length);
    const reorderedMarkers = markers.slice(startIndex).concat(markers.slice(0, startIndex));

    let processedMarkers = 0;
    for (const marker of reorderedMarkers) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }

      // Проверяем шанс генерации
      if (rng() * 100 < stage.chance) {
        await stage.generator(grid, marker, worldSize, rng, cancellationToken);
      }
      
      processedMarkers++;
      // Освобождаем event loop каждые 10 маркеров
      if (processedMarkers % 10 === 0) {
        const stageProgress = 10 + ((stageIndex + processedMarkers / markers.length) / (algorithmicStages.length + 1)) * 80;
        progressTracker.updateStageProgress(
          stageProgress, 
          `Генерация декораций: ${stage.name} (${processedMarkers}/${markers.length})`
        );
        await new Promise(resolve => setImmediate(resolve));
      }
    }
    
    await new Promise(resolve => setImmediate(resolve));
  }

  // Этап 2: Шаблонные декорации (горы, реки, озера)
  progressTracker.updateStageProgress(90, 'Генерация шаблонных декораций (горы, реки, озера)');

  // Создаем вложенный ProgressTracker для шаблонных декораций
  const patternProgressTracker = new ProgressTracker((progress) => {
    progressTracker.updateStageProgress(
      90 + (progress.progress * 0.1), // 90-100% диапазон для шаблонов
      `Шаблоны: ${progress.currentOperation}`
    );
  });
  
  patternProgressTracker.addStage('Шаблонные декорации', 1);

  await generatePatternDecorationsAsync(
    grid, 
    worldSize, 
    rng, 
    patternProgressTracker,
    cancellationToken
  );

  progressTracker.updateStageProgress(100, 'Все декорации сгенерированы');
}

// Алиас для обратной совместимости - теперь использует новую стабильную систему
export const generateDecorations = generateDecorationsAsync;

// Получение названия декорации для отображения прогресса
function getDecorationName(decoration: WorldMapDecorations): string {
  const names = {
    [WorldMapDecorations.FOREST]: 'Лес',
    [WorldMapDecorations.MOUNTAINS]: 'Горы',
    [WorldMapDecorations.LAKE]: 'Озеро',
    [WorldMapDecorations.RIVER]: 'Река',
    [WorldMapDecorations.SWAMP]: 'Болото',
    [WorldMapDecorations.RUINS]: 'Руины',
    [WorldMapDecorations.RUBBLE]: 'Обломки',
    [WorldMapDecorations.NONE]: 'Нет',
    [WorldMapDecorations.BUSHES]: 'Кусты',
    [WorldMapDecorations.CITY]: 'Город',
    [WorldMapDecorations.ROAD]: 'Дорога'
  };
  
  return names[decoration] || 'Неизвестная декорация';
}