import {  DecorationZoneType, LocationDecorations, LocationSubtype, LocationType, MaterialTexture, TerrainType } from '../enums'
import { Position } from '../models/Player'
import { Container } from './Container'
import { NPC } from './NPC'
import { OpenClose } from './OpenClose'



export interface Location {
  id: string
  name: string
  description: string
  type: LocationType
  subtype: LocationSubtype
  locationSize: LocationSize
  locationMap: Record<string, LocationMapCell>;
  terrain: TerrainType
  textureMaterial: MaterialTexture;
  isDiscovered: boolean
  isVisible: boolean  
  isExplored: boolean
  morality: number; //-1 - 20  where -1 always enemy  like beast, robots, mutants, zombies, 0 -6 bad guys, 7 - 13 neutral, 14 - 20 good guys
  playerPosition: Position;
  playerPresent: boolean;
  faction?: string;
  createdAt: Date
  lastUpdatedAt: Date
  spawnPosition: Position;
  goBackPosition: Point[];
  
  // статическая карта освещения от декораций [x, y, lightLevel]
  staticLightMap?: Array<[number, number, number]>;
}
export interface LocationMapCell {
  pos: Position
  terrain: TerrainType;
  terrainMarker: number; //none - для пустых клеток под конструкцией
  blocked: boolean; 
  decoration?: LocationDecorations;
  decorationSides?: PointWithNumber;
  interactive?: LocationInteractive;
  decorationZoneType?: DecorationZoneType;
  roof?: MaterialTexture;
  floor?: TerrainType;
  decorationRNG?: number[]; 
  decorationBorder?: number[]; 
  textureDirection?: number;
  goBackZone: boolean;
  spawnZone: boolean;
}

export interface LocationSize {
  x: number; 
  y: number; 
}

export type Point = [number, number];              // x, y
export type PointWithId = [number, number, number]; // x, y, id
 // x, y
export type PointWithNumber = [number, number, number[]]; // x, y, number

export interface Hazard {
  id: string
  name: string
  description: string
  position: Position
  type: 'fire' | 'landmine' | 'trap' | 'nuclearWaste'
decorationRNG?: number;
}

export interface LocationInteractive {
  id: string
  type: Container | OpenClose | NPC |  Hazard | 'interactive'
  name: string
  description?: string
  position: Position
  decorationRNG?: number;
}

export interface TransferLocation {
  id: string;
  name: string;
  description: string;
  locationSize: Point;
  type: LocationType;
  subtype: LocationSubtype
  morality: number; //-1 - 20  where -1 always enemy  like beast, robots, mutants, zombies, 0 -6 bad guys, 7 - 13 neutral, 14 - 20 good guys
  terrain: TerrainType;
  playerPresent: boolean;
  playerPosition: Point;
  spawnPosition: Point;
  goBackPosition: Point[];
  decorations?: Partial<Record<LocationDecorations, Point[]>>;
  decorationZoneType?: Partial<Record<DecorationZoneType, PointWithId[]>>;
  roof?:  Record<MaterialTexture, Point[]>;
  floor?:  Record<TerrainType, Point[]>;
  decorationSide?: PointWithNumber[];
  textureMaterial?: MaterialTexture;
  interactive?:  LocationInteractive[];
  isDiscovered: boolean;
  isVisible: boolean;
  faction?: string;
}
