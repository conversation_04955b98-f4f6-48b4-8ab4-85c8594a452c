/**
 * Менеджер игрового времени - управляет ходом времени в реальном времени
 */

import { GameTime, TIME_CONSTANTS, addSeconds, addMinutes, createGameTime } from './gameTime';

export type TimeUpdateCallback = (gameTime: GameTime) => void;

class GameTimeManager {
  private gameTime: GameTime;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;
  private callbacks: Set<TimeUpdateCallback> = new Set();
  
  constructor(initialTime?: GameTime) {
    if (initialTime) {
      // Обеспечиваем обратную совместимость
      this.gameTime = { 
        ...initialTime, 
        second: initialTime.second ?? 0 
      };
    } else {
      this.gameTime = createGameTime(1, 12, 0, 0); // По умолчанию полдень первого дня
    }
  }
  
  /**
   * Запускает ход времени
   */
  start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    
    // Обновляем время каждые 100мс для плавных переходов освещения
    // Каждые 100мс реального времени = 6 игровых секунд (1 реальная секунда = 1 игровая минута)
    this.intervalId = setInterval(() => {
      this.gameTime = addSeconds(this.gameTime, 6); // 6 секунд каждые 100мс = 60 секунд/минуту
      this.notifyCallbacks();
    }, TIME_CONSTANTS.UPDATE_INTERVAL_MS);
    
  }
  
  /**
   * Останавливает ход времени
   */
  stop(): void {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
  }
  
  /**
   * Получает текущее игровое время
   */
  getCurrentTime(): GameTime {
    return { ...this.gameTime };
  }
  
  /**
   * Устанавливает игровое время
   */
  setTime(newTime: GameTime): void {
    // Обеспечиваем обратную совместимость с данными без поля second
    this.gameTime = { 
      ...newTime, 
      second: newTime.second ?? 0 
    };
    this.notifyCallbacks();
  }
  
  /**
   * Добавляет слушатель изменений времени
   */
  addTimeUpdateCallback(callback: TimeUpdateCallback): void {
    this.callbacks.add(callback);
  }
  
  /**
   * Удаляет слушатель изменений времени
   */
  removeTimeUpdateCallback(callback: TimeUpdateCallback): void {
    this.callbacks.delete(callback);
  }
  
  /**
   * Проверяет, идет ли время
   */
  isTimeRunning(): boolean {
    return this.isRunning;
  }
  
  /**
   * Ускоряет время (добавляет указанное количество минут)
   */
  fastForward(minutes: number): void {
    this.gameTime = addMinutes(this.gameTime, minutes);
    this.notifyCallbacks();
  }
  
  /**
   * Синхронизирует время с gameStore
   */
  private syncWithGameStore(): void {
    try {
      // Динамический импорт чтобы избежать циклических зависимостей
      import('../../store/gameStore').then(({ useGameStore }) => {
        const { setGameTime } = useGameStore.getState();
        setGameTime(this.gameTime);
      });
    } catch (error) {
      // Игнорируем ошибки синхронизации
    }
  }
  
  /**
   * Уведомляет всех слушателей об изменении времени
   */
  private notifyCallbacks(): void {
    this.callbacks.forEach(callback => {
      try {
        callback(this.gameTime);
      } catch (error) {
        console.error('❌ Ошибка в callback времени:', error);
      }
    });
    
    // Также синхронизируем с gameStore при каждом обновлении
    this.syncWithGameStore();
  }
  
  /**
   * Очищает все ресурсы
   */
  dispose(): void {
    this.stop();
    this.callbacks.clear();
  }
}

// Singleton экземпляр менеджера времени
export const gameTimeManager = new GameTimeManager();

// Автоматический запуск времени при загрузке модуля
if (typeof window !== 'undefined') {
  // В браузере запускаем время
  gameTimeManager.start();
  
  // Останавливаем время при закрытии страницы
  window.addEventListener('beforeunload', () => {
    gameTimeManager.stop();
  });
}
