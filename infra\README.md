# Infrastructure - NuclearStory

Docker and infrastructure configuration for the NuclearStory project.

## Contents

- `docker-compose.yml` - Main orchestration file
- `docker-compose.dev.yml` - Development configuration with hot reload
- `postgres/` - Database initialization scripts
- `env/` - Environment variable templates

## Services

### Databases
- **postgres-auth** - Authentication service database (Port 5432)
- **postgres-saves** - Save service database (Port 5433)
- **redis** - Caching and session storage (Port 6379)

### Application Services
- **auth-service** - Authentication API (Port 3001)
- **game-engine-service** - Game logic API (Port 3002)
- **world-generator-service** - World generation API (Port 3003)
- **save-service** - Save management API (Port 3004)
- **ai-service** - AI integration service (Port 3005)
- **frontend** - React frontend (Port 3000)

### Development Tools
- **adminer** - Database management interface (Port 8080, dev only)

## Quick Start

```bash
# Start all services (production)
docker-compose up -d

# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up --build -d
```

## Service Access

После запуска сервисы доступны по следующим адресам:

- **Frontend**: http://localhost:3000
- **Auth Service**: http://localhost:3001/api/auth
- **Game Engine**: http://localhost:3002/api/game
- **World Generator**: http://localhost:3003/api/story
- **Save Service**: http://localhost:3004/api/save
- **AI Service**: http://localhost:3005
- **Adminer** (dev only): http://localhost:8080

## Environment Variables

Copy the example environment files and configure:

```bash
cp env/.env.example .env
# Edit .env with your configuration
```

## Database Management

```bash
# Access postgres
docker-compose exec postgres-auth psql -U nuclearstory -d auth_db

# Run migrations
docker-compose exec auth-service npm run migration:run
```
