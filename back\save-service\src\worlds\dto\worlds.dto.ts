import { IsString, IsObject, IsNumber, IsBoolean, IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { WorldSettings, WorldWeatherState, WorldMapCell } from '../../shared/types/World'
import { Player } from 'src/shared'

/**
 * DTO для сохранения мира
 */
export class SaveWorldDto {
  @ApiProperty({ description: 'ID мира', example: 'uuid' })
  @IsString()
  id: string

  @ApiProperty({ description: 'ID пользователя', example: 'user_123' })
  @IsString()
  userId: string

  @ApiProperty({ description: 'Название мира', example: 'My World' })
  @IsString()
  name: string

  @ApiProperty({ description: 'Параметры мира', type: 'object' })
  @IsObject()
  parameters: WorldWeatherState

  @ApiProperty({ description: 'Описание мира', example: 'Описание' })
  @IsString()
  description: string

  @ApiProperty({ description: 'Настройки мира', type: 'object' })
  @IsObject()
  settings: WorldSettings

  @ApiProperty({ description: 'Карта мира', type: 'object' })
  @IsObject()
  worldMap: Record<string, WorldMapCell>

  @ApiProperty({ description: 'Игрок', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  player?: Player

  @ApiProperty({ description: 'Дата создания', type: 'string', format: 'date-time' })
  @IsString()
  createdAt: Date

  @ApiProperty({ description: 'Дата обновления', type: 'string', format: 'date-time' })
  @IsString()
  updatedAt: Date

  @ApiProperty({ description: 'Время генерации', example: 5000, required: false })
  @IsOptional()
  @IsNumber()
  generationTime?: number

  @ApiProperty({ description: 'Генерировалось ли с помощью ИИ', example: true, required: false })
  @IsOptional()
  @IsBoolean()
  generatedWithAI?: boolean
}

