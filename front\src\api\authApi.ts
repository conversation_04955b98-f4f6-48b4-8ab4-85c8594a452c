import { LoginRequest, RegisterRequest, AuthResponse, UserProfile } from '../shared/types'

// URL сервиса авторизации - прямое подключение
const AUTH_SERVICE_URL = import.meta.env.VITE_AUTH_SERVICE_URL || 'http://localhost:3001/api'

// Создание базового клиента с автоматическим добавлением токена
class AuthApiClient {
  private getHeaders(token?: string): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    return headers
  }

  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await fetch(`${AUTH_SERVICE_URL}/login`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка авторизации: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    
    const response = await fetch(`${AUTH_SERVICE_URL}/register`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка регистрации: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async getProfile(token: string): Promise<UserProfile> {
    
    const response = await fetch(`${AUTH_SERVICE_URL}/profile`, {
      method: 'GET',
      headers: this.getHeaders(token),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Ошибка получения профиля: ${response.statusText}`)
    }

    const result = await response.json()
    return result
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(`${AUTH_SERVICE_URL}/verify`, {
        method: 'POST',
        headers: this.getHeaders(token),
      })

      const isValid = response.ok
      return isValid
    } catch (error) {
      return false
    }
  }
}

export const authApi = new AuthApiClient()
