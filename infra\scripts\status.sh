#!/bin/bash

# NuclearStory - Status Script
echo "📊 NuclearStory Status"
echo "===================="

# Check Docker containers
echo ""
echo "🐳 Docker Containers:"
docker compose ps

# Check service health
echo ""
echo "🏥 Service Health Checks:"

services=("auth-service:3001" "game-engine-service:3002" "world-generator-service:3003" "save-service:3004" "ai-service:3005")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "   ✅ $name (port $port) - Healthy"
    else
        echo "   ❌ $name (port $port) - Unhealthy"
    fi
done

# Check frontend
if curl -s -f "http://localhost:3000" > /dev/null 2>&1; then
    echo "   ✅ frontend (port 3000) - Healthy"
else
    echo "   ❌ frontend (port 3000) - Unhealthy"
fi

echo ""
echo "🔗 Quick Access:"
echo "   🎮 Game: http://localhost:3000"
echo "   🔐 Auth API: http://localhost:3001/api/docs"
echo "   🎯 Game API: http://localhost:3002/api/docs"
echo "   🌍 Story API: http://localhost:3003/api/docs"
echo "   💾 Save API: http://localhost:3004/api/docs"
echo "   🤖 AI API: http://localhost:3005/docs"
echo ""
echo "📝 View logs with: docker compose logs [service-name]"
echo "🔄 Restart service: docker compose restart [service-name]"
