# Статус фронтенда - Краткий справочник

## 🎯 Общая информация

### 🛠 Технологический стек
- **React 18** + TypeScript
- **Vite** для сборки и dev сервера
- **React Router** для роутинга
- **Zustand** для управления состоянием
- **Pure CSS** для стилизации
- **shadcn/ui** для UI компонентов
- **Axios** для API запросов
- **Socket.io-client** для WebSocket

### 📁 Структура проекта
```
front/
├── src/
│   ├── components/     # Переиспользуемые UI компоненты
│   ├── pages/         # Страницы приложения
│   ├── hooks/         # Кастомные React хуки
│   ├── store/         # Zustand стор
│   ├── services/      # API сервис
│   ├── types/         # TypeScript типы
│   ├── utils/         # Утилиты
│   ├── styles/        # Глобальные стили
│   └── game/          # Игровая логика
├── public/            # Статические ресурсы
└── index.html         # Входная точка
```

## 🎮 Игровые системы

### ✅ Система текстур местности
**Файл**: `TERRAIN_TEXTURES_GUIDE.md`

**Что работает:**
- Автоматическое отображение текстур в `drawTile()`
- 7 типов местности с 4 вариациями каждый
- Предзагрузка и кэширование текстур
- Fallback на цветную заливку

**Использование:**
```typescript
import { drawTerrainTexture, TerrainType } from './game/comp/IMComp'

// Автоматически работает в drawTile()
// Или ручное использование:
drawTerrainTexture(ctx, centerX, centerY, halfWidth, halfHeight, TerrainType.GRASS)
```

**Доступные типы:**
- GRASS, DEADFOREST, MOUNTAIN, WATER, DESERT, SWAMP, WASTELAND

### ✅ WebSocket интеграция для прогресса генерации
**Файл**: `WEBSOCKET_FRONTEND_GUIDE.md`

**Оновные возможности:**
- Real-time отслеживание прогресса генерации мира
- Подписка на обновления через WebSocket
- Отмена генерации в процессе
- Готовый React хук `useWorldGeneration`

**Ключевые события:**
```javascript
// Подписка на прогресс
socket.emit('subscribe-progress', { sessionId, userId, worldName })

// Получение обновлений
socket.on('progress-update', (data) => {
  // data.progress: { stage, progress, currentOperation, estimatedTimeRemaining }
})

// Завершение генерации
socket.on('generation-complete', (data) => {
  // data.result: { success, worldId, error }
})
```

## 🚀 Команды разработки

```bash
# Установка зависимостей
npm install

# Запуск dev сервера
npm run dev

# Сборка для продакшена
npm run build

# Предпросмотр сборки
npm run preview
```

## 🌐 Доступные маршруты

- `/` - Главная страница
- `/login` - Авторизация
- `/signup` - Регистрация
- `/game` - Основной игровой интерфейс

## 🔌 API интеграция

### WebSocket подключение
```javascript
const socket = io('http://localhost:3003/progress', {
  transports: ['websocket'],
  autoConnect: false
});
```

### REST API эндпоинты
- `POST /generate-world-with-progress` - запуск генерации с прогрессом
- `POST /cancel-generation/:sessionId` - отмена генерации
- `GET /generation-stats` - статистика активных генераций

## 🎨 Игровые компоненты

### IMComp система
**Расположение**: `src/game/comp/IMComp/`

**Структура:**
- `constants.ts` - пути к текстурам
- `renderUtils.ts` - функция отрисовки текстур
- `hooks.ts` - хук предзагрузки текстур
- `terrainTextureDemo.ts` - демо утилиты

**Использование:**
```typescript
import { useTerrainTextures, drawTerrainDemo } from './game/comp/IMComp'

// В компоненте
const { texturesLoaded } = useTerrainTextures()

// Демо всех текстур
drawTerrainDemo(ctx)
```

## 🔧 Готовые решения

### React хук для генерации мира
```javascript
import { useWorldGeneration } from './hooks/useWorldGeneration'

const { isGenerating, progress, startGeneration, cancelGeneration } = useWorldGeneration()

// Запуск генераии
await startGeneration(worldData)

// Отмена
cancelGeneration()
```

### Компонент прогресс-бара
```jsx
{progress && (
  <div>
    <div>Этап: {progress.stage}</div>
    <div>Операция: {progress.currentOperation}</div>
    <div style={{ width: `${progress.progress}%` }} />
    <div>{progress.progress}%</div>
    {progress.estimatedTimeRemaining && (
      <div>Осталось: {Math.round(progress.estimatedTimeRemaining / 1000)} сек</div>
    )}
  </div>
)}
```

## 📊 Этапы генерации мира

1. **Инициализация** (1%)
2. **Создание базовой сетки** (3%)
3. **Генерация маркеров** (2%)
4. **Расчет зон уровней** (2%)
5. **Установка направлений** (1%)
6. **Генерация декораций** (4%)
7. **Создание локаций** (5%)
8. **Финализация** (1%)

## 🛡 Важные особенности

### Текстурная система
- ✅ Автоматическое кэширование
- ✅ Fallback на цвета при ошибках загрузки
- ✅ Поддержка вариаций и поворотов
- ✅ Интеграция с существующими типами

### WebSocket система
- ⚠️ Подключение ПОСЛЕ получения sessionId
- ⚠️ Обязательная отписка при размонтировании
- ⚠️ Обработка ошибок подключения
- ⚠️ Уникальность сессий

## 🔍 Отладка

### Проверка текстур
```typescript
import { logTextureStatus } from './game/comp/IMComp'
logTextureStatus() // В консоль
```

### WebSocket логирование
```javascript
socket.onAny((event, ...args) => {
  console.log('WebSocket событие:', event, args)
})
```

## 📝 Следующие улучшения

### Текстурная система
- Текстуры фона (без натягивания на ромб)
- Объектные текстуры с эффектом объема
- Анимированные текстуры

### WebSocket система
- Пауза/возобновление генерации
- Расширенная аналитика
- Уведомления о статусе очереди

## 💡 Архитектурные принципы

- **Максимальная простота** использования
- **Легкий импорт** через единые точки входа
- **Автоматическое кэширование** ресурсов
- **Fallback системы** для стаильности
- **Интеграция** с существующей архитектурой