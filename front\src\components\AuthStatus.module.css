.authStatus {
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  border: 1px solid var(--dark-border);
  background: var(--dark-card);
}

.loading {
  color: var(--nuclear-yellow);
  font-weight: 500;
}

.authenticated {
  color: var(--nuclear-green);
  font-weight: 500;
}

.notAuthenticated {
  color: var(--nuclear-red);
  font-weight: 500;
}

.userInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.userDetails {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  font-size: 0.8rem;
}

.email {
  color: var(--text-primary);
  font-weight: 500;
}

.username {
  color: var(--text-secondary);
}

.role {
  background: var(--nuclear-orange);
  color: var(--dark-bg);
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.tokenInfo {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--dark-border);
}

.tokenLabel {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.token {
  display: block;
  background: var(--dark-bg);
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.7rem;
  color: var(--nuclear-green);
  word-break: break-all;
}
