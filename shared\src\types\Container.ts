import { ContainerType } from '../enums'
import { Position } from '../models/Player'

export interface ContainerLock {
  // Тип замка
  type: 'key' | 'combination' | 'electronic' | 'biometric' | 'magical'
  
  // Сложность взлома
  difficulty: number // 0-100
  
  // Требования для открытия
  requirements: {
    keyId?: string // ID ключа
    combination?: string // комбинация цифр
    biometricId?: string // ID биометрических данных
    skillLevel?: number // уровень навыка взлома
    toolRequired?: string // ID инструмента
  }
  
  // Состояние замка
  isLocked: boolean
  isBroken: boolean
  isJammed: boolean
  
  // Попытки взлома
  failedAttempts: number
  maxAttempts: number
  lockoutTime?: number // минут блокировки
}

export interface ContainerMaterial {
  // Материал контейнера
  type: 'wood' | 'metal' | 'plastic' | 'glass' | 'fabric' | 'leather' | 'composite'
  
  // Прочность
  durability: number // текущая прочность
  maxDurability: number // максимальная прочность
  
  // Сопротивления
  resistances: {
    physical: number // сопротивление физическому урону
    fire: number // сопротивление огню
    water: number // водостойкость
    acid: number // сопротивление кислоте
    radiation: number // защита от радиации
  }
  
  // Свойства
  isTransparent: boolean // можно ли видеть содержимое
  isWaterproof: boolean // водонепроницаемость
  isFireproof: boolean // огнестойкость
  isInsulated: boolean // теплоизоляция
}

export interface ContainerProperties {
  // Размеры (для визуализации, вместимость бесконечная)
  dimensions: {
    width: number
    height: number
    depth: number
  }
  
  // Вес
  weight: number // собственный вес контейнера
  
  // Специальные свойства
  isPortable: boolean // можно ли переносить
  requiresTwoHands: boolean // требует ли две руки для переноски
  canBeOpened: boolean // можно ли открыть
  canBeClosed: boolean // можно ли закрыть
  hasLid: boolean // есть ли крышка
  
  // Условия хранения
  preservesFood: boolean // сохраняет ли еду от порчи
  preservesMedicine: boolean // сохраняет ли лекарства
  protectsFromRadiation: boolean // защищает ли содержимое от радиации
  maintainsTemperature: boolean // поддерживает ли температуру
  
  // Доступность
  requiresElectricity: boolean // требует ли электричество для работы
  workingTemperatureRange?: {
    min: number
    max: number
  }
}

export interface Container {
  // Основная информация
  id: string
  name: string
  description: string
  type: ContainerType
  
  // Позиция и ориентация
  position: Position
  rotation: number // градусы (0-360)
  
  // Состояние контейнера
  isOpen: boolean
  isDestroyed: boolean
  isEmpty: boolean
  
  // Материал и прочность
  material: ContainerMaterial
  
  // Свойства
  properties: ContainerProperties
  
  // Замок (если есть)
  lock?: ContainerLock
  
  // Содержимое (ID предметов, вместимость бесконечная)
  contents: string[]
  
  // Организация содержимого
  organization: {
    // Категории предметов
    categories: Record<string, string[]> // категория -> массив ID предметов
    
    // Быстрый доступ
    quickAccess: string[] // ID предметов для быстрого доступа
    
    // Скрытые отделения
    hiddenCompartments: Array<{
      id: string
      name: string
      isDiscovered: boolean
      contents: string[]
      discoveryRequirement?: {
        skill: string
        level: number
      }
    }>
  }
  
  // Доступ и разрешения
  access: {
    // Кто может использовать
    allowedFactions: string[] // ID фракций
    allowedNPCs: string[] // ID конкретных NPC
    allowedPlayers: string[] // ID игроков
    
    // Ограничения
    requiresPermission: boolean
    permissionLevel: number // уровень доступа
    
    // Владелец
    ownerId?: string
    ownerType?: 'player' | 'npc' | 'faction'
  }
  
  // Взаимодействие
  interaction: {
    // Можно ли взаимодействовать
    isInteractable: boolean
    
    // Расстояние взаимодействия
    interactionRange: number
    
    // Время взаимодействия
    openTime: number // секунд для открытия
    closeTime: number // секунд для закрытия
    searchTime: number // секунд для поиска
    
    // Требования для взаимодействия
    requirements?: {
      strength?: number // минимальная сила
      skill?: string // требуемый навык
      tool?: string // требуемый инструмент
    }
  }
  
  // Эффекты и ловушки
  effects: {
    // Ловушки
    hasTrap?: boolean
    trapType?: 'explosive' | 'poison' | 'electric' | 'alarm' | 'dart'
    trapDamage?: number
    trapIsArmed?: boolean
    trapIsTriggered?: boolean
    
    // При открытии
    onOpen?: {
      triggerAlarm?: boolean
      alertNPCs?: string[] // ID NPC для оповещения
      giveItems?: string[] // ID предметов для получения
      removeItems?: string[] // ID предметов для удаления
      startQuest?: string // ID квеста для запуска
    }
    
    // При разрушении
    onDestroy?: {
      scatterContents?: boolean
      destroyContents?: boolean
      createDebris?: boolean
      triggerExplosion?: boolean
    }
  }
  
  // Визуальные эффекты
  visual: {
    sprite?: string
    model?: string
    texture?: string
    
    // Эффекты состояния
    glowWhenLocked?: boolean
    sparkWhenDamaged?: boolean
    smokeWhenDestroyed?: boolean
    
    // Индикаторы
    hasStatusLight?: boolean
    statusLightColor?: string // цвет индикатора состояния
    
    // Анимации
    openAnimation?: string
    closeAnimation?: string
    searchAnimation?: string
  }
  
  // Звуки
  sounds: {
    openSound?: string
    closeSound?: string
    lockSound?: string
    unlockSound?: string
    breakSound?: string
    searchSound?: string
  }
  
  // История и статистика
  history: {
    timesOpened: number
    timesLooted: number
    timesLocked: number
    timesBroken: number
    lastOpenedAt?: Date
    lastLootedAt?: Date
    lastRepairedAt?: Date
    
    // Кто последний взаимодействовал
    lastInteractedBy?: {
      type: 'player' | 'npc'
      id: string
      action: 'open' | 'close' | 'loot' | 'lock' | 'unlock' | 'break' | 'repair'
    }
  }
  
  // Респавн лута
  respawn: {
    canRespawn: boolean
    respawnTime: number // минут
    lastRespawnAt?: Date
    respawnLootTable?: string // ID таблицы лута
    maxRespawns?: number // максимальное количество респавнов
    currentRespawns: number
  }
  
  // Метаданные
  createdAt: Date
  lastUpdatedAt: Date
  version: string
}
