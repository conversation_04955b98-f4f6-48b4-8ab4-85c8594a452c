/**
 * Информационный терминал в стиле Fallout 2
 */

import * as React from 'react'
import { useState, useEffect, useRef } from 'react'
import { useGameStore } from '@/game/store/gameStore'
import styles from './InfoTerminal.module.css'
import { WorldMapCell } from '@/shared'
interface TerminalMessage {
  id: number
  timestamp: string
  text: string
}

interface InfoTerminalProps {
  cellTarget: { isoX: number; isoY: number; tileData: WorldMapCell } | null
  onAddMessage?: (addMessageFn: (message: string) => void) => void
}


/**
 * Форматирует время для терминала
 */
const formatTime = (): string => {
  const now = new Date()
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
}

/**
 * Компонент информационного терминала
 */
export const InfoTerminal: React.FC<InfoTerminalProps> = ({ cellTarget, onAddMessage }) => {
  const locationLoading = useGameStore((state) => state.locationLoading)
  const [messages, setMessages] = useState<TerminalMessage[]>([
    {
      id: 0,
      timestamp: formatTime(),
      text: '> СИСТЕМА РАЗВЕДКИ АКТИВИРОВАНА'
    },
    {
      id: 1,
      timestamp: formatTime(),
      text: '> ГОТОВ К СКАНИРОВАНИЮ МЕСТНОСТИ'
    },
    {
      id: 2,
      timestamp: formatTime(),
      text: '> ИНСТРУКЦИЯ: Левый клик - выбор цели'
    },
    {
      id: 3,
      timestamp: formatTime(),
      text: '> Правый клик - контекстное меню с осмотром'
    },
    {
      id: 4,
      timestamp: formatTime(),
      text: '> Нажмите ENTER для начала движения'
    }
  ])

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messageIdRef = useRef(5)

  // Функция для добавления сообщения извне
  const addMessage = React.useCallback((text: string) => {
    const newMessage: TerminalMessage = {
      id: messageIdRef.current++,
      timestamp: formatTime(),
      text: text
    };
    setMessages(prev => [...prev, newMessage]);
  }, []);

  // Передаем функцию добавления сообщения наружу
  React.useEffect(() => {
    if (onAddMessage) {
      onAddMessage(addMessage);
    }
  }, [onAddMessage, addMessage]);

  // Отслеживаем состояние загрузки локации с дополнительной проверкой
  React.useEffect(() => {
    // Получаем необходимые данные из стора
    const currentWorld = useGameStore.getState().currentWorld;
    const currentLocation = useGameStore.getState().currentLocation;
    const playerPosition = currentWorld?.player?.position;

    let shouldShowLoading = false;

    if (currentWorld && playerPosition && !currentLocation) {
      // Игрок на главной карте, проверяем есть ли локация на этом тайле
      const tileKey = `${playerPosition.x},${playerPosition.y}`;
      const tile = currentWorld.worldMap?.[tileKey];
      if (tile && tile.location) {
        shouldShowLoading = true;
      }
    } else if (currentLocation && currentLocation.locationMap) {
      // Игрок в локации, ищем goBackZone на текущем тайле
      const posKey = `${currentLocation.playerPosition.x},${currentLocation.playerPosition.y}`;
      const cell = currentLocation.locationMap[posKey];
      if (cell && cell.goBackZone) {
        shouldShowLoading = true;
      }
    }

    if (shouldShowLoading) {
      if (locationLoading) {
        // Проверяем, что последнее сообщение не о загрузке
        const lastMsg = messages[messages.length - 1];
        if (!lastMsg || !lastMsg.text.includes('ЗАГРУЗКА ЛОКАЦИИ')) {
          addMessage('> ЗАГРУЗКА ЛОКАЦИИ...');
        }
      } else {
        // Добавляем сообщение о завершении загрузки только если до этого была загрузка
        const lastMsg = messages[messages.length - 1];
        if (lastMsg && lastMsg.text.includes('ЗАГРУЗКА ЛОКАЦИИ')) {
          addMessage('> ЗАГРУЗКА ЗАВЕРШЕНА');
        }
      }
    }
  }, [locationLoading, addMessage, messages]);

  // Автопрокрутка к последнему сообщению
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Убираем автоматическое добавление сообщения при выбое тайла
  // Теперь сообщения добавляются только при левом клике или через контекстное меню

  return (
    <div className={styles.terminal_img}>
      <div className={styles.terminal}>
        <div className={styles.terminalOverlay} />
        <div className={styles.terminalContent}>
          <div className={styles.messagesContainer}>
            {messages.map((message) => (
              <div key={message.id} className={styles.message}>
                {/* <span className={styles.timestamp}>[{message.timestamp}]</span> */}
                <span className={styles.messageText}>{message.text}</span>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>
    </div>
  )
}