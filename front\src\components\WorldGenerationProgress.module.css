/* Overlay для модального окна */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

/* Основное модальное окно */
.modal {
  background: var(--dark-card);
  border: 1px solid var(--dark-border);
  border-radius: 1rem;
  box-shadow: 
    0 10px 20px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(0, 255, 65, 0.1);
  width: 90%;
  max-width: 500px;
  min-height: 280px;
  font-family: inherit;
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

/* Заголовок */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--dark-border);
  background: var(--dark-card);
}

.title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: none;
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
  color: var(--text-secondary);
  filter: none;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.cancelButton {
  background: linear-gradient(135deg, var(--nuclear-red), #cc0000);
  border: none;
  border-radius: 0.375rem;
  color: white;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelButton:hover {
  background: linear-gradient(135deg, #cc0000, var(--nuclear-red));
  box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
  transform: scale(1.05);
}

.cancelButton:active {
  transform: scale(0.95);
}

/* Основной контент */
.content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* Информация об этапе */
.stageInfo {
  text-align: center;
}

.stageName {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: none;
  margin-bottom: 0.5rem;
  min-height: 1.5rem;
}

.currentOperation {
  font-size: 0.875rem;
  color: var(--text-secondary);
  opacity: 0.9;
  min-height: 1.125rem;
}

/* Контейнер прогресс-бара */
.progressContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Прогресс-бар */
.progressBar {
  flex: 1;
  height: 1.5rem;
  background: var(--dark-bg);
  border: 1px solid var(--dark-border);
  border-radius: 0.375rem;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5);
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #16a34a, var(--nuclear-green), #16a34a);
  background-size: 200% 100%;
  animation: progressShine 2s ease-in-out infinite;
  transition: width 0.3s ease;
  position: relative;
  box-shadow: 
    0 0 10px rgba(0, 255, 65, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@keyframes progressShine {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: 0% 0; }
}

/* Неопределенный прогресс (пульсирующий) */
.progressFillIndeterminate {
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 255, 65, 0.3) 25%, 
    rgba(0, 255, 65, 0.6) 50%, 
    rgba(0, 255, 65, 0.3) 75%, 
    transparent 100%
  );
  background-size: 200% 100%;
  animation: indeterminateProgress 1.5s ease-in-out infinite;
}

@keyframes indeterminateProgress {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Свечение прогресс-бара */
.progressGlow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%
  );
  background-size: 50% 100%;
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
  0%, 100% { background-position: -50% 0; }
  50% { background-position: 150% 0; }
}

/* Текст прогресса */
.progressText {
  font-size: 1rem;
  font-weight: bold;
  color: var(--nuclear-green);
  text-shadow: 0 0 6px rgba(0, 255, 65, 0.5);
  min-width: 2.8rem;
  text-align: right;
}

/* Дополнительная информация */
.additionalInfo {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.timeRemaining {
  font-size: 0.875rem;
  color: var(--nuclear-yellow);
  font-weight: bold;
  text-shadow: 0 0 6px rgba(255, 255, 0, 0.5);
}

.hint {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Подвал с советами */
.footer {
  padding: 1rem 1.5rem 1.25rem;
  border-top: 1px solid var(--dark-border);
  background: var(--dark-card);
}

.tips {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tips strong {
  color: var(--nuclear-yellow);
}

/* Адаптивность */
@media (max-width: 600px) {
  .modal {
    width: 95%;
    margin: 1.25rem;
  }
  
  .header {
    padding: 1rem 1.25rem 0.75rem;
  }
  
  .title {
    font-size: 1rem;
  }
  
  .content {
    padding: 1.25rem;
    gap: 1rem;
  }
  
  .stageName {
    font-size: 1.125rem;
  }
  
  .progressContainer {
    gap: 0.75rem;
  }
  
  .progressBar {
    height: 1.25rem;
  }
  
  .progressText {
    font-size: 0.875rem;
    min-width: 2.5rem;
  }
  
  .footer {
    padding: 0.75rem 1.25rem 1rem;
  }
}

@media (max-width: 480px) {
  .modal {
    width: 98%;
    margin: 0.5rem;
  }
  
  .header {
    padding: 0.875rem 1rem 0.625rem;
  }
  
  .title {
    font-size: 0.875rem;
    gap: 0.5rem;
  }
  
  .spinner {
    width: 1rem;
    height: 1rem;
  }
  
  .content {
    padding: 1rem;
    gap: 0.875rem;
  }
  
  .stageName {
    font-size: 1rem;
  }
  
  .currentOperation {
    font-size: 0.75rem;
  }
  
  .progressContainer {
    gap: 0.5rem;
  }
  
  .progressBar {
    height: 1rem;
  }
  
  .progressText {
    font-size: 0.75rem;
    min-width: 2rem;
  }
  
  .timeRemaining {
    font-size: 0.75rem;
  }
  
  .hint {
    font-size: 0.625rem;
  }
  
  .footer {
    padding: 0.625rem 1rem 0.875rem;
  }
  
  .tips {
    font-size: 0.625rem;
    gap: 0.375rem;
  }
}

/* Анимация появления модального окна */
.overlay {
  animation: fadeIn 0.3s ease-out;
}

.modal {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}