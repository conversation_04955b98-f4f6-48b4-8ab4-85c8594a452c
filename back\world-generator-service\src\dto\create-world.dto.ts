import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, IsObject, ValidateNested, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class WorldSettingsDto {
  @ApiProperty({ example: 'easy', description: 'Уровень сложности мира' })
  @IsString()
  difficulty: 'easy' | 'normal' | 'hard';

  @ApiProperty({ example: 200, description: 'Размер мира' })
  @IsNumber()
  @Min(10)
  @Max(200)
  worldSize: number;

  @ApiProperty({ example: '1bb7yesm72k', description: 'Сид для генерации мира' })
  @IsString()
  seed: string;

  @ApiProperty({ example: 'everytime', description: 'Режим автосохранения' })
  @IsString()
  autosave: 'everytime' | 'when_rest' | 'on_exit' | 'forbidden';

  @ApiProperty({ example: 'de', description: 'Язык мира' })
  @IsString()
  language: string;

  @ApiProperty({ example: 1, description: 'Масштаб времени' })
  @IsNumber()
  @Min(0.1)
  @Max(10)
  timeScale: number;
}

export class CreateWorldDto {
  @ApiProperty({ example: '754', description: 'Название мира' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'укен', description: 'Описание мира' })
  @IsString()
  description: string;

  @ApiProperty({ example: 'cee5aa76-c7d6-4f5a-a0d6-fad6819a641e', description: 'ID пользователя' })
  @IsUUID()
  userId: string;

  @ApiProperty({ type: WorldSettingsDto, description: 'Настройки мира' })
  @IsObject()
  @ValidateNested()
  @Type(() => WorldSettingsDto)
  settings: WorldSettingsDto;
}
