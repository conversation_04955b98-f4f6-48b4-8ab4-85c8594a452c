

.systemMenu {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.menuButton {
  background: rgba(33, 66, 5, 0.1);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 50px;
  height: 50px;
}

.menuButton:hover,
.menuButtonActive {
  background: var(--primary-dark);
  box-shadow: 0 0 20px var(--shadow-primary);
  transform: translateY(-2px);
}

.menuButton:active {
  transform: translateY(0);
}

.dropdown {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 10px;
  z-index: 1000;
  animation: dropdownOpen 0.3s ease-out;
}

.dropdownContent {
  background: var(--bg-overlay);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
  min-width: 180px;
  overflow: hidden;
}

.menuOption {
  background: transparent;
  border: none;
  color: var(--text-primary);
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  width: 100%;
  text-align: left;
  transition: all 0.3s ease;
}

.menuOption:hover {
  background: rgba(33, 66, 5, 0.2);
  color: var(--primary-color);
  text-shadow: 0 0 5px var(--shadow-primary);
}

.menuOption svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.menuOption span {
  flex: 1;
}

.exitOption {
  color: #ff6b6b;
}

.exitOption:hover {
  background: rgba(255, 107, 107, 0.1);
  color: #ff8e8e;
}

.separator {
  height: 1px;
  background: var(--border-color);
  margin: 4px 0;
}

@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .menuButton {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }
  
  .dropdownContent {
    min-width: 160px;
  }
  
  .menuOption {
    padding: 10px 14px;
    font-size: 13px;
  }
}
