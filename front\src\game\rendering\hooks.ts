import { useState, useEffect } from 'react'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT, FRAME_DURATION } from '../utils/constants/rendering'
import { terrainTextureManager, decorationTextureManager } from './isometric/renderUtils'
import { createContextMenuHandler } from '../systems/interaction/eventHandlers'
import { gameTimeManager } from '../utils/time/gameTimeManager'
import { GameTime } from '../utils/time/gameTime'

/**
 * Хук для адаптивного размера экрана с максимальными ограничениями.
 * Возвращает { width, height } для canvas.
 */
export function useAdaptiveScreenSize(maxWidth: number = IMCOMP_MAX_WIDTH, maxHeight: number = IMCOMP_MAX_HEIGHT) {
  const [screenSize, setScreenSize] = useState({
    width: Math.min(window.innerWidth, maxWidth),
    height: Math.min(window.innerHeight, maxHeight)
  })

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: Math.min(window.innerWidth, maxWidth),
        height: Math.min(window.innerHeight, maxHeight)
      })
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [maxWidth, maxHeight])

  return screenSize
}

/**
 * Хук для хранения выбранной клетки карты
 * Возвращает выбранную клетку и функцию для её обновления
 */
export function useCellTarget() {
  const [cellTarget, setCellTarget] = useState<{
    isoX: number;
    isoY: number;
    tileData: any;
  } | null>(null)
  return [cellTarget, setCellTarget] as const
}

/**
 * Хук для предзагрузки текстур местности
 */
export const useTerrainTextures = () => {
  const [texturesLoaded, setTexturesLoaded] = useState(false)

  useEffect(() => {
    const preloadTextures = async () => {
      try {
        await terrainTextureManager.preloadAllTextures()
        setTexturesLoaded(true)
      } catch (error) {
      }
    }

    preloadTextures()
  }, [])

  return { texturesLoaded, terrainTextureManager }
}

/**
 * Хук для предзагрузки текстур декораций
 */
export const useDecorationTextures = () => {
  const [texturesLoaded, setTexturesLoaded] = useState(false)

  useEffect(() => {
    const preloadTextures = async () => {
      try {
        await decorationTextureManager.preloadAllTextures()
        setTexturesLoaded(true)
      } catch (error) {
      }
    }

    preloadTextures()
  }, [])

  return { texturesLoaded, decorationTextureManager }
}

/**
 * Хук для циклической отрисовки - основной рендер-цикл
 */
export const useRenderLoop = (draw: () => void) => {
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0
    let frameCount = 0

    const renderLoop = (time: number) => {
      frameCount++

      // Рисуем каждый кадр для плавности (можно изменить на каждый 5й кадр если нужно)
      if (time - lastRenderTime >= FRAME_DURATION) {
        draw()
        lastRenderTime = time
      }

      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [draw]) // Только draw в зависимостях
}

/**
 * Хук для отключения контекстного меню
 */
export const useContextMenuDisable = (canvasRef: React.RefObject<HTMLCanvasElement>) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const handler = createContextMenuHandler()
    canvas.addEventListener('contextmenu', handler)
    return () => canvas.removeEventListener('contextmenu', handler)
  }, [canvasRef])
}

/**
 * Хук для начальной отрисовки
 */
export const useInitialDraw = (draw: () => void) => {
  useEffect(() => {
    draw()
  }, [draw])
}

/**
 * Хук для работы с игровым временем
 */
export const useGameTime = () => {
  const [gameTime, setGameTime] = useState<GameTime>(() => gameTimeManager.getCurrentTime())

  useEffect(() => {
    const unsubscribe = gameTimeManager.addTimeUpdateCallback(setGameTime)
    return unsubscribe
  }, [])

  return gameTime
}

/**
 * Хук для управления игровым временем
 */
export const useGameTimeControl = () => {
  const gameTime = useGameTime()

  const getCurrentTime = () => gameTimeManager.getCurrentTime()
  const fastForward = (minutes: number) => gameTimeManager.fastForward(minutes)
  const setTime = (time: GameTime) => gameTimeManager.setTime(time)

  return {
    gameTime,
    getCurrentTime,
    fastForward,
    setTime
  }
}