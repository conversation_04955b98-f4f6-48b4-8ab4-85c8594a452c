import React from 'react'
import { useAuthStore } from '../store/authStore'
import styles from './AuthStatus.module.css'

interface AuthStatusProps {
  showToken?: boolean
}

export const AuthStatus: React.FC<AuthStatusProps> = ({ showToken = false }) => {
  const { user, token, isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className={styles.authStatus}>
        <span className={styles.loading}>🔄 Проверка авторизации...</span>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className={styles.authStatus}>
        <span className={styles.notAuthenticated}>🔒 Не авторизован</span>
      </div>
    )
  }

  return (
    <div className={styles.authStatus}>
      <div className={styles.userInfo}>
        <span className={styles.authenticated}>✅ Авторизован</span>
        <div className={styles.userDetails}>
          <span className={styles.email}>{user?.email}</span>
          {user?.username && (
            <span className={styles.username}>@{user.username}</span>
          )}
          {user?.role && (
            <span className={styles.role}>{user.role}</span>
          )}
        </div>
        {showToken && token && (
          <div className={styles.tokenInfo}>
            <span className={styles.tokenLabel}>Токен:</span>
            <code className={styles.token}>
              {token.substring(0, 20)}...{token.substring(token.length - 10)}
            </code>
          </div>
        )}
      </div>
    </div>
  )
}
