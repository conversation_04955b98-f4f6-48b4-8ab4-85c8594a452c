

import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
import { LocationDecorations } from '../../shared/enums';
import { OpenClose } from '../../shared/types/OpenClose';

type LegendMap = Record<string, string>;

// Простая реализация: выбираем случайный пресет из папки buildings (small/medium/big),
// вычисляем позицию по центру + случайный оффсет в пределах anchor и наносим токены.
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap

): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	// Presets application temporarily disabled.
	// We keep the function and its signature so callers don't need changes.
	// Reason: switching to a random generator and new ordering — will implement later.
	return;

	// Поддерживаемые наборы пресетов (пока статично)
	// Каждый файл экспортирует `locationConfigs: Record<string, PresetLocationMap>`
	// Используем require чтобы избежать сложностей с tsconfig/esm
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	const small = require('./presets/buildings/small').locationConfigs as Record<string, any>;
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	const medium = require('./presets/buildings/medium').locationConfigs as Record<string, any>;
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	const big = require('./presets/buildings/big').locationConfigs as Record<string, any>;

	const allPresets = { ...small, ...medium, ...big };
	const presetKeys = Object.keys(allPresets);
	if (presetKeys.length === 0) return;

	// Фильтр по размеру пресета (если он больше локации, пропускаем)
	const locSizeX = location.locationSize?.[0] ?? 0;
	const locSizeY = location.locationSize?.[1] ?? 0;

	const candidates = presetKeys
		.map(k => ({ key: k, preset: allPresets[k] }))
		.filter(p => {
			const tokenMap = p.preset.tokenMap as string[][];
			const pw = tokenMap[0]?.length ?? p.preset.width ?? 0;
			const ph = tokenMap.length ?? p.preset.height ?? 0;
			return pw <= locSizeX && ph <= locSizeY;
		});

	const pool = candidates.length ? candidates : presetKeys.map(k => ({ key: k, preset: allPresets[k] }));

	// Выбор случайного пресета (учтём weight, если задан)
	const totalWeight = pool.reduce((s, p) => s + (p.preset.weight ?? 1), 0);
	let r = rng() * totalWeight;
	let chosen = pool[0];
	for (const p of pool) {
		r -= (p.preset.weight ?? 1);
		if (r <= 0) { chosen = p; break; }
	}

	const preset = chosen.preset;
	const tokenMap = preset.tokenMap as string[][];
	const presetH = tokenMap.length;
	const presetW = tokenMap[0]?.length ?? 0;

	// Центр локации
	const centerX = Math.floor((locSizeX) / 2);
	const centerY = Math.floor((locSizeY) / 2);

	const anchorX = preset.anchor?.x ?? 0;
	const anchorY = preset.anchor?.y ?? 0;

	// Случайный оффсет в пределах anchor
	const offsetX = Math.floor((rng() * 2 - 1) * anchorX);
	const offsetY = Math.floor((rng() * 2 - 1) * anchorY);

	let topLeftX = centerX - Math.floor(presetW / 2) + offsetX;
	let topLeftY = centerY - Math.floor(presetH / 2) + offsetY;

	// Клэмп, чтобы не выйти за границы локации
	if (topLeftX < 0) topLeftX = 0;
	if (topLeftY < 0) topLeftY = 0;
	if (topLeftX + presetW > locSizeX) topLeftX = Math.max(0, locSizeX - presetW);
	if (topLeftY + presetH > locSizeY) topLeftY = Math.max(0, locSizeY - presetH);

	// occupancy set для простого избежания наложений
	const occupied = new Set<string>();
	if (location.decorations) {
		for (const key of Object.keys(location.decorations)) {
			const arr = (location.decorations as any)[key] as Array<[number, number]> | undefined;
			if (!arr) continue;
			for (const [x, y] of arr) occupied.add(`${x},${y}`);
		}
	} else {
		location.decorations = {};
	}

	// Реверс легенды: символ -> имя
	const reverseLegend: Record<string, string> = {};
	for (const k of Object.keys(legend || {})) {
		const v = legend[k];
		reverseLegend[v] = k;
	}

	// Наносим токены
	for (let py = 0; py < presetH; py++) {
		for (let px = 0; px < presetW; px++) {
			const token = tokenMap[py][px];
			const worldX = topLeftX + px;
			const worldY = topLeftY + py;
			// вне границ
			if (worldX < 0 || worldY < 0 || worldX >= locSizeX || worldY >= locSizeY) continue;

			const key = `${worldX},${worldY}`;

			// Если токен пустой ('0' или пробел) — ОЧИСТИТЬ любую существующую декорацию/interactive
			if (!token || token === '0' || token === ' ') {
				// удалить декорации
				if (location.decorations) {
					for (const dk of Object.keys(location.decorations)) {
						const arr = (location.decorations as any)[dk] as Array<[number, number]> | undefined;
						if (!arr) continue;
						(location.decorations as any)[dk] = arr.filter(([x, y]) => !(x === worldX && y === worldY));
						if ((location.decorations as any)[dk].length === 0) delete (location.decorations as any)[dk];
					}
				}
				// удалить interactive
				if (location.interactive && Array.isArray(location.interactive)) {
					location.interactive = location.interactive.filter((inter: any) => {
						const pos = inter.position;
						if (!pos) return true;
						if (Array.isArray(pos)) return !(pos[0] === worldX && pos[1] === worldY);
						return !(pos.x === worldX && pos.y === worldY);
					});
					if ((location.interactive as any).length === 0) delete location.interactive;
				}
				// пометим как занято, чтобы другие пресеты не накладывались сюда
				occupied.add(key);
				continue;
			}

			// ранее мы пропускали наложения (occupied), но теперь при наложении
			// пресета декорация должна заменять существующую.

			const decoName = reverseLegend[token] ?? token;

			// Если токен - дверь, добавляем интерактив OpenClose (isOpen: false), но
			// сохраняем декоратор на той же клетке.
			if (decoName === LocationDecorations.DOOR || decoName === 'door' || decoName === 'Door') {
				if (!location.interactive) location.interactive = [];
				// избегаем дублирования интерактива в одной клетке
				const exists = Array.isArray(location.interactive) && (location.interactive as any).some((inter: any) => {
					const pos = inter.position;
					if (!pos) return false;
					if (Array.isArray(pos)) return pos[0] === worldX && pos[1] === worldY;
					return pos.x === worldX && pos.y === worldY;
				});
				if (!exists) {
					(location.interactive as any).push({
						id: `preset_door_${worldX}_${worldY}`,
						type: { isOpen: false } as OpenClose,
						name: 'Door',
						position: { x: worldX, y: worldY }
					});
				}
			}

			// Перед добавлением новой декорации удалим любые существующие декорации
			// на этой клетке (чтобы выполнить "замену").
			for (const dk of Object.keys(location.decorations)) {
				const arr = (location.decorations as any)[dk] as Array<[number, number]> | undefined;
				if (!arr) continue;
				(location.decorations as any)[dk] = arr.filter(([x, y]) => !(x === worldX && y === worldY));
				if ((location.decorations as any)[dk].length === 0) delete (location.decorations as any)[dk];
			}

			// Добавляем декорацию в соответствующий список
			if (!location.decorations[decoName as any]) location.decorations[decoName as any] = [];
			(location.decorations as any)[decoName].push([worldX, worldY]);
			occupied.add(key);
		}
	}

	// пока простой вариант — одна вставка; позже можно вставлять несколько с учётом config.buildings
	return;
}