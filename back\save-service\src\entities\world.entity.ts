import { Entity, Column, PrimaryColumn } from 'typeorm';
import { WorldSettings, WorldWeatherState, WorldMapCell } from '../shared/types/World';
import { Player } from 'src/shared';

@Entity('worlds')
export class WorldEntity {
  @PrimaryColumn({ type: 'varchar', length: 64 })
  id: string;

  @Column({ type: 'varchar', length: 64 })
  userId: string;

  @Column({ type: 'varchar', length: 128 })
  name: string;

  @Column({ type: 'jsonb' })
  parameters: WorldWeatherState;

  @Column({ type: 'varchar', length: 256 })
  description: string;

  @Column({ type: 'jsonb' })
  settings: WorldSettings;

  @Column({ type: 'jsonb' })
  worldMap: Record<string, WorldMapCell>;

  @Column({ type: 'jsonb', nullable: true })
  player?: Player;

  @Column({ type: 'timestamp' })
  createdAt: Date;

  @Column({ type: 'timestamp' })
  updatedAt: Date;
}
