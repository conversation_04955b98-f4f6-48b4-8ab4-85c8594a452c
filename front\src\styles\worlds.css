/* World Manager Styles */
.world-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.world-manager-header {
  margin-bottom: 30px;
}

.world-manager-header h1 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.world-manager-tabs {
  display: flex;
  gap: 10px;
  border-bottom: 2px solid #ecf0f1;
}

.tab {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  font-size: 16px;
}

.tab:hover {
  background-color: #f8f9fa;
}

.tab.active {
  border-bottom-color: #3498db;
  color: #3498db;
  font-weight: 600;
}

/* World Creator Styles */
.world-creator {
  max-width: 800px;
}

.world-form {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-section {
  margin-bottom: 30px;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #34495e;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 2px solid #ecf0f1;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.form-group input[type="range"] {
  padding: 0;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

/* Progress Styles */
.world-creator-progress {
  text-align: center;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #ecf0f1;
  border-radius: 10px;
  overflow: hidden;
  margin: 20px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.current-task {
  margin-top: 20px;
  text-align: left;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

/* Success Styles */
.world-creator-success {
  text-align: center;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.world-stats {
  margin: 20px 0;
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 4px;
}

.world-stats ul {
  list-style: none;
  padding: 0;
}

.world-stats li {
  padding: 5px 0;
  border-bottom: 1px solid #ecf0f1;
}

/* Worlds List Styles */
.worlds-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.worlds-list-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.status-filter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.worlds-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.world-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.world-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.world-card.selected {
  border-color: #3498db;
}

.world-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.world-name {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.world-status {
  display: flex;
  gap: 5px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #d5f4e6;
  color: #27ae60;
}

.status-badge.archived {
  background-color: #fdeaa7;
  color: #f39c12;
}

.public-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background-color: #dbeafe;
  color: #3b82f6;
}

.world-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.4;
}

.world-info {
  margin-bottom: 15px;
}

.world-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.world-info-item .label {
  font-weight: 600;
  color: #34495e;
}

.seed {
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.world-stats {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
}

.world-meta {
  margin-bottom: 15px;
  font-size: 12px;
  color: #7f8c8d;
}

.world-meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
}

.world-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.tag {
  padding: 2px 8px;
  background-color: #ecf0f1;
  border-radius: 12px;
  font-size: 11px;
  color: #34495e;
}

.world-card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ecf0f1;
}

.world-actions-menu {
  display: flex;
  gap: 5px;
}

/* Pagination */
.worlds-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.pagination-info {
  color: #7f8c8d;
  font-size: 14px;
}

/* Stats Tab */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card .stat-value {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.stat-card .stat-label {
  font-size: 14px;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Sidebar */
.world-details-sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 350px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  padding: 20px;
  overflow-y: auto;
  z-index: 1000;
}

.world-details {
  margin-top: 20px;
}

.world-detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ecf0f1;
}

.world-detail-item .label {
  font-weight: 600;
  color: #34495e;
}

.world-actions {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
}

/* Error and Loading States */
.error-message {
  background-color: #fadbd8;
  color: #c0392b;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.worlds-list-loading,
.stats-loading,
.world-details-loading {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.worlds-list-empty {
  text-align: center;
  padding: 60px;
  color: #7f8c8d;
}

/* Responsive */
@media (max-width: 768px) {
  .world-manager {
    padding: 10px;
  }
  
  .worlds-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .world-details-sidebar {
    width: 100%;
    position: relative;
    height: auto;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}
