/**
 * Функции для генерации сообщений терминала
 */

import { WorldMapDecorations } from '../../../../shared/enums'
import { WorldMapCell } from '../../../../shared/types/World'

/**
 * Получает описание местности для терминала
 */
const getDecorationTerminalDescription = (decoration: WorldMapDecorations): string => {
  switch (decoration) {
    case WorldMapDecorations.MOUNTAINS:
      return 'горная местность';
    case WorldMapDecorations.FOREST:
      return 'лес';
    case WorldMapDecorations.RIVER:
      return 'река';
    case WorldMapDecorations.ROAD:
      return 'дорога';
    case WorldMapDecorations.SWAMP:
      return 'болото';
    case WorldMapDecorations.CITY:
      return 'городские руины';
    case WorldMapDecorations.RUINS:
      return 'руины';
    case WorldMapDecorations.LAKE:
      return 'озеро';
    case WorldMapDecorations.RUBBLE:
      return 'завалы';
    case WorldMapDecorations.BRIDGE:
      return 'мост';
    case WorldMapDecorations.BUSHES:
      return 'кустарник';
    case WorldMapDecorations.VILLAGE:
      return 'деревня';
    case WorldMapDecorations.NONE:
      return 'пустая местность';
    default:
      return 'неизвестная местность';
  }
}

/**
 * Получает стоимость перемещения по местности
 */
const getMovementCost = (decoration: WorldMapDecorations): number => {
  switch (decoration) {
    case WorldMapDecorations.ROAD:
      return 1; // Дорога - быстро
    case WorldMapDecorations.FOREST:
    case WorldMapDecorations.BUSHES:
      return 2; // Лес/кустарник - нормально
    case WorldMapDecorations.SWAMP:
      return 5; // Болото - крайне медленно
    case WorldMapDecorations.MOUNTAINS:
      return 6; // Горы - очень тяжело
    case WorldMapDecorations.RIVER:
    case WorldMapDecorations.LAKE:
      return 10; // Вода - почти невозможно
    case WorldMapDecorations.CITY:
    case WorldMapDecorations.RUINS:
    case WorldMapDecorations.RUBBLE:
      return 4; // Руины/завалы - сложно
    case WorldMapDecorations.VILLAGE:
      return 2; // Деревня - нормально
    case WorldMapDecorations.NONE:
      return 3; // Пустая местность - средне
    case WorldMapDecorations.BRIDGE:
      return 1; // Мост - быстро
    default:
      return 3;
  }
}

/**
 * Генерирует сообщение осмотра тайла
 */
export const generateInspectMessage = (tileData: { isoX: number; isoY: number; tileData: WorldMapCell }): string => {
  const { tileData: tile } = tileData;
  const decor = tile.decoration as WorldMapDecorations;
  const decorName = getDecorationTerminalDescription(decor);

  const fogResponses = [
    '... хмм, ничего не видно, даже если сильно захотеть',
    '... пустота, как в голове у местного торговца',
    '... туман, как после вчерашнего',
    '... если здесь что-то и есть, то оно хорошо прячется',
    '... видимость нулевая, как у надежды на светлое будущее',
    '... только тьма и неизвестность',
    '... кажется, тут даже радиация не решилась остаться',
    '... если бы у вас был Пип-бой, он бы показал: "ничего не найдено"',
    '... вы смотрите, а в ответ — тишина',
    '... хмм, ничего вы не видите, даже прищурившись',
  ];

  if (tile.fogOfWar) {
    return `> ${fogResponses[Math.floor(Math.random() * fogResponses.length)]}`;
  }

  let message = `> Вы видите ${decorName}`;

  // Дополнительные детали в зависимости от типа декорации
  switch (decor) {
    case WorldMapDecorations.FOREST:
      message += '\nГустой лес. Здесь можно найти древесину и, возможно, ягоды.';
      break;
    case WorldMapDecorations.MOUNTAINS:
      message += '\nКаменистые склоны уходят вверх. Возможно, есть пещеры или укрытия.';
      break;
    case WorldMapDecorations.RIVER:
      message += '\nБыстрое течение. Переход затруднен, но можно попытаться порыбачить.';
      break;
    case WorldMapDecorations.ROAD:
      message += '\nСтарая дорога, частично разрушенная. Путешествие будет быстрее.';
      break;
    case WorldMapDecorations.SWAMP:
      message += '\nБолотистая жижа булькает под ногами. Много насекомых.';
      break;
    case WorldMapDecorations.CITY:
      message += '\nРуины города. Могут быть ценные ресурсы, но и опасности.';
      break;
    case WorldMapDecorations.RUINS:
      message += '\nДревние руины. Кто знает, что здесь можно найти?';
      break;
    case WorldMapDecorations.LAKE:
      message += '\nВода выглядит мутной. Лучше не пить без очистки.';
      break;
    case WorldMapDecorations.RUBBLE:
      message += '\nЗавалы и обломки. Придется обходить.';
      break;
    case WorldMapDecorations.BRIDGE:
      message += '\nСтарый мост. Может быть опасен, но ускоряет переход.';
      break;
    case WorldMapDecorations.BUSHES:
      message += '\nКустарник. Можно найти ягоды или укрыться.';
      break;
    case WorldMapDecorations.VILLAGE:
      message += '\nЗаброшенная деревня. Возможно, остались припасы.';
      break;
    case WorldMapDecorations.NONE:
      message += '\nПустая местность. Ничего интересного.';
      break;
  }

  if (tile.blocked) {
    message += '\n\nПРОХОД ЗАБЛОКИРОВАН';
    message += '\nПохоже, тут не пройти. Препятствие блокирует путь.';
  }

  if (tile.location) {
    message += `. Здесь есть ${tile.location.name || 'что-то интересное'}`;
  }

  return message;
}

/**
 * Генерирует сообщение о стоимости перемещения
 */
export const generateMovementCostMessage = (tileData: { isoX: number; isoY: number; tileData: WorldMapCell }): string => {
  const { isoX, isoY, tileData: tile } = tileData;
  const decor = tile.decoration as WorldMapDecorations;
  const decorName = getDecorationTerminalDescription(decor);
  const cost = getMovementCost(decor);

  let message = `> АНАЛИЗ ПЕРЕМЕЩЕНИЯ [${isoX}, ${isoY}]\n`;
  message += `\nМестность: ${decorName}`;
  message += `\nБазовая стоимость: ${cost} ОД (очков действия)`;

  // Описание сложности
  if (cost <= 2) {
    message += '\nЛегкое перемещение - минимальные затраты энергии';
  } else if (cost <= 4) {
    message += '\nУмеренная сложность - потребует больше времени';
  } else if (cost <= 6) {
    message += '\nСложное перемещение - высокие затраты энергии';
  } else {
    message += '\nКрайне сложно - почти невозможно пройти';
  }

  // Дополнительные факторы
  if (tile.blocked) {
    message += '\n\nПРОХОД НЕВОЗМОЖЕН';
    message += '\nПуть заблокирован препятствием.';
  } else {
    // Влияние на характеристики
    switch (decor) {
      case WorldMapDecorations.SWAMP:
        message += '\nРиск заболеваний';
        break;
      case WorldMapDecorations.MOUNTAINS:
        message += '\nПовышенная усталость';
        break;
      case WorldMapDecorations.ROAD:
      case WorldMapDecorations.BRIDGE:
        message += '\nУскоренное перемещение';
        break;
      case WorldMapDecorations.RIVER:
      case WorldMapDecorations.LAKE:
        message += '\nПотребуется переправа или обход';
        break;
      case WorldMapDecorations.CITY:
      case WorldMapDecorations.RUINS:
        message += '\nВозможные опасности и ресурсы';
        break;
    }
  }

  return message;
}