.mainMenu {
  min-height: 100vh;
  background-image: url('/MainManuBG.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 1rem;
}

.mainPanel {
  text-align: center;
}

.titleSection {
  margin-bottom: 3rem;
}

.title {
  font-size: 4.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 10px 20px rgba(0, 0, 0, 0.8);
}

.subtitle {
  font-size: 1.25rem;
  color: #e5e5e5;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
  max-width: 512px;
  margin: 0 auto;
}

.menuButtons {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.menuButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  max-width: 384px;
  padding: 1.5rem 2rem;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  transform: scale(1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.8);
  border: none;
  cursor: pointer;
}

.menuButton:hover {
  transform: scale(1.05);
}

.playButton {
  background: linear-gradient(to right, #16a34a, #15803d);
}

.playButton:hover {
  background: linear-gradient(to right, #22c55e, #16a34a);
}

.settingsButton {
  background: linear-gradient(to right, #4b5563, #374151);
}

.settingsButton:hover {
  background: linear-gradient(to right, #6b7280, #4b5563);
}

.exitButton {
  background: linear-gradient(to right, #dc2626, #b91c1c);
}

.exitButton:hover {
  background: linear-gradient(to right, #ef4444, #dc2626);
}

.menuButton svg {
  width: 2rem;
  height: 2rem;
  transition: transform 0.3s ease;
}

.playButton:hover svg,
.exitButton:hover svg {
  transform: scale(1.1);
}

.settingsButton:hover svg {
  transform: rotate(90deg);
}

.panel {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 1rem;
  padding: 2rem;
  max-width: 1024px;
  margin: 0 auto;
}

.panelSmall {
  max-width: 512px;
}

.panelHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.panelTitle {
  font-size: 1.875rem;
  font-weight: bold;
  color: white;
}

.closeButton {
  padding: 0.5rem;
  color: #9ca3af;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
}

.closeButton:hover {
  color: white;
}

.closeButton svg {
  width: 2rem;
  height: 2rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 3rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .menuButton {
    font-size: 1.25rem;
    padding: 1rem 1.5rem;
  }
  
  .panel {
    padding: 1.5rem;
    margin: 0 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2.5rem;
  }
  
  .menuButton {
    font-size: 1.125rem;
    padding: 0.875rem 1.25rem;
  }
  
  .menuButton svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
