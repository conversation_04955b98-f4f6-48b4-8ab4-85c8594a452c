import { WorldMapCell } from '../../shared/types/World';
import { WorldMapDecorations } from '../../shared/enums';
import { 
  ProgressTracker, 
  CancellationToken 
} from '../../utils/asyncUtils';

interface Position {
  x: number;
  y: number;
}

/**
 * Асинхронно очищает карту от изолированных рек и дорог
 * 1) Реки: если река одна и в радиусе 2х клеток нет других рек, удаляем её
 *    Если есть одна река - соединяем с ней
 * 2) Дороги: если дорога одна и в радиусе 2х клеток нет других дорог, 
 *    то с шансом 50/50 удаляем или продлеваем на 3 клетки
 *    Если есть одна дорога - соединяем с ней
 */
export async function cleanDecorationMapAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number,
  rng: () => number,
  progressTracker?: ProgressTracker,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  const updateInterval = Math.max(1000, Math.floor(totalCells / 100));
  
  // Сначала обрабатываем реки
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (!cell) {
        processedCells++;
        continue;
      }
      
      // Обрабатываем изолированные реки
      if (cell.decoration === WorldMapDecorations.RIVER) {
        const nearbyRivers = findNearbyDecorations(
          grid, x, y, worldSize, 
          [WorldMapDecorations.RIVER, WorldMapDecorations.BRIDGE], 
          2
        );
        
        if (nearbyRivers.length === 0) {
          // Нет рек поблизости - удаляем
          cell.decoration = WorldMapDecorations.NONE;
        } else if (nearbyRivers.length === 1) {
          // Есть одна река поблизости - соединяем только если она не по диагонали
          const target = nearbyRivers[0];
          if (isOrthogonalConnection(x, y, target.x, target.y)) {
            await connectToNearestDecorationAsync(grid, x, y, target, worldSize, WorldMapDecorations.RIVER);
          }
        }
        // Если рек больше одной - ничего не делаем, река уже соединена
      }
      
      processedCells++;
      
      // Обновляем прогресс и освобождаем event loop
      if (processedCells % updateInterval === 0) {
        if (progressTracker) {
          const progress = (processedCells / totalCells) * 50; // 50% для рек
          progressTracker.updateStageProgress(
            progress, 
            `Очистка рек: ${processedCells}/${totalCells}`
          );
        }
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }
  
  // Затем обрабатываем дороги
  processedCells = 0;
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (!cell) {
        processedCells++;
        continue;
      }
      
      // Обрабатываем изолированные дороги
      if (cell.decoration === WorldMapDecorations.ROAD) {
        const nearbyRoads = findNearbyDecorations(
          grid, x, y, worldSize, 
          [WorldMapDecorations.ROAD, WorldMapDecorations.BRIDGE], 
          2
        );
        
        if (nearbyRoads.length === 0) {
          // Нет дорог поблизости - 50/50 шанс удалить или продлить
          if (rng() < 0.5) {
            // Удаляем дорогу
            cell.decoration = WorldMapDecorations.NONE;
          } else {
            // Продлеваем дорогу на 3 клетки
            await extendRoadAsync(grid, x, y, worldSize, rng, 3);
          }
        } else if (nearbyRoads.length === 1) {
          // Есть одна дорога поблизости - соединяем только если она не по диагонали
          const target = nearbyRoads[0];
          if (isOrthogonalConnection(x, y, target.x, target.y)) {
            await connectToNearestDecorationAsync(grid, x, y, target, worldSize, WorldMapDecorations.ROAD);
          }
        }
        // Если дорог больше одной - ничего не делаем, дорога уже соединена
      }
      
      processedCells++;
      
      // Обновляем прогресс и освобождаем event loop
      if (processedCells % updateInterval === 0) {
        if (progressTracker) {
          const progress = 50 + (processedCells / totalCells) * 50; // 50-100% для дорог
          progressTracker.updateStageProgress(
            progress, 
            `Очистка дорог: ${processedCells}/${totalCells}`
          );
        }
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }
  
  // Постобработка: удаляем изолированные декорации с decorationBorder [1, 2, 3, 4]
  processedCells = 0;
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (!cell) {
        processedCells++;
        continue;
      }
      
      // Проверяем дороги и реки на изолированность
      if (cell.decoration === WorldMapDecorations.ROAD || 
          cell.decoration === WorldMapDecorations.RIVER) {
        
        const decorationBorder = getDecorationBorder(grid, x, y, worldSize, cell.decoration);
        
        // Если decorationBorder содерж��т все 4 направления [1, 2, 3, 4]
        if (decorationBorder.length === 4 && 
            decorationBorder.includes(1) && decorationBorder.includes(2) && 
            decorationBorder.includes(3) && decorationBorder.includes(4)) {
          
          // 80% шанс удалить такую изолированную декорацию
          if (rng() < 0.8) {
            cell.decoration = WorldMapDecorations.NONE;
          }
        }
      }
      
      processedCells++;
      
      // Обновляем прогресс и освобождаем event loop
      if (processedCells % updateInterval === 0) {
        if (progressTracker) {
          const progress = (processedCells / totalCells) * 100;
          progressTracker.updateStageProgress(
            progress, 
            `Постобработка: ${processedCells}/${totalCells}`
          );
        }
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }
  
  if (progressTracker) {
    progressTracker.updateStageProgress(100, 'Очистка декораций завершена');
  }
}

/**
 * Находит позиции указанных декораций в радиусе от заданной позиции
 * Исключает саму проверяемую клетку из поиска
 */
function findNearbyDecorations(
  grid: Record<string, WorldMapCell>,
  centerX: number,
  centerY: number,
  worldSize: number,
  decorations: WorldMapDecorations[],
  radius: number
): Position[] {
  const found: Position[] = [];
  
  for (let x = centerX - radius; x <= centerX + radius; x++) {
    for (let y = centerY - radius; y <= centerY + radius; y++) {
      // Пропускаем саму центральную клетку
      if (x === centerX && y === centerY) {
        continue;
      }
      
      // Проверяем границы карты
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) {
        continue;
      }
      
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (cell && decorations.includes(cell.decoration)) {
        found.push({ x, y });
      }
    }
  }
  
  return found;
}

/**
 * Соединяет текущую позицию с ближайшей декорацией (асинхронно)
 * Прокладывает путь, избегая озер и локаций
 */
async function connectToNearestDecorationAsync(
  grid: Record<string, WorldMapCell>,
  startX: number,
  startY: number,
  target: Position,
  worldSize: number,
  decorationType: WorldMapDecorations
): Promise<void> {
  const path = findPath(startX, startY, target.x, target.y);
  
  let processedSteps = 0;
  
  for (const pos of path) {
    // Пропускаем стартовую и конечную точки
    if ((pos.x === startX && pos.y === startY) || 
        (pos.x === target.x && pos.y === target.y)) {
      continue;
    }
    
    // Проверяем границы карты
    if (pos.x < 0 || pos.x >= worldSize || pos.y < 0 || pos.y >= worldSize) {
      continue;
    }
    
    const cellKey = `${pos.x},${pos.y}`;
    const cell = grid[cellKey];
    
    if (!cell) {
      continue;
    }
    
    // Проверяем, можно ли разместить декорацию в этой клетке
    if (canPlaceDecoration(cell, decorationType)) {
      if (decorationType === WorldMapDecorations.RIVER) {
        // Для рек проверяем, нужен ли мост
        if (cell.decoration === WorldMapDecorations.ROAD) {
          cell.decoration = WorldMapDecorations.BRIDGE;
        } else {
          cell.decoration = WorldMapDecorations.RIVER;
        }
      } else if (decorationType === WorldMapDecorations.ROAD) {
        // Для дорог проверяем, нужен ли мост
        if (cell.decoration === WorldMapDecorations.RIVER) {
          cell.decoration = WorldMapDecorations.BRIDGE;
        } else {
          cell.decoration = WorldMapDecorations.ROAD;
        }
      }
    }
    
    processedSteps++;
    
    // Освобождаем event loop каждые 10 шагов без задержки
    if (processedSteps % 10 === 0) {
      await new Promise(resolve => setImmediate(resolve));
    }
  }
}

/**
 * Находит ортогональный путь между двумя точками (только горизонтально и вертикально)
 * Исключает диагональные движения, так как нет диагональных текстур дорог/рек
 */
function findPath(startX: number, startY: number, endX: number, endY: number): Position[] {
  const path: Position[] = [];
  
  let currentX = startX;
  let currentY = startY;
  
  // Сначала двигаемся строго по горизонтали (по X)
  while (currentX !== endX) {
    if (currentX < endX) {
      currentX++;
    } else {
      currentX--;
    }
    path.push({ x: currentX, y: currentY });
  }
  
  // Затем двигаемся строго по вертикали (по Y)
  while (currentY !== endY) {
    if (currentY < endY) {
      currentY++;
    } else {
      currentY--;
    }
    path.push({ x: currentX, y: currentY });
  }
  
  return path;
}

/**
 * Проверяет, можно ли разместить декорацию в данной клетке
 */
function canPlaceDecoration(cell: WorldMapCell, decorationType: WorldMapDecorations): boolean {
  // Нельзя размещать через озера
  if (cell.decoration === WorldMapDecorations.LAKE) {
    return false;
  }
  
  // Нельзя размещать через локации
  if (cell.location) {
    return false;
  }
  
  // Для рек
  if (decorationType === WorldMapDecorations.RIVER) {
    // Можно размещать на пустых клетках, кустах, мусоре или дорогах (мост)
    return cell.decoration === WorldMapDecorations.NONE ||
           cell.decoration === WorldMapDecorations.BUSHES ||
           cell.decoration === WorldMapDecorations.RUBBLE ||
           cell.decoration === WorldMapDecorations.ROAD;
  }
  
  // Для дорог
  if (decorationType === WorldMapDecorations.ROAD) {
    // Можно размещать на пустых клетках, кустах, мусоре или реках (мост)
    return cell.decoration === WorldMapDecorations.NONE ||
           cell.decoration === WorldMapDecorations.BUSHES ||
           cell.decoration === WorldMapDecorations.RUBBLE ||
           cell.decoration === WorldMapDecorations.RIVER;
  }
  
  return false;
}

/**
 * Продлевает дорогу змейкой на указанное количество клеток (асинхронно)
 * Избегает рек, озер и локаций
 */
async function extendRoadAsync(
  grid: Record<string, WorldMapCell>,
  startX: number,
  startY: number,
  worldSize: number,
  rng: () => number,
  length: number
): Promise<void> {
  const directions = [
    { dx: 0, dy: -1 }, // север
    { dx: 1, dy: 0 },  // восток
    { dx: 0, dy: 1 },  // юг
    { dx: -1, dy: 0 }  // запад
  ];
  
  let currentX = startX;
  let currentY = startY;
  let placedSegments = 0;
  let attempts = 0;
  const maxAttempts = length * 10; // Ограничиваем количество попыток
  
  while (placedSegments < length && attempts < maxAttempts) {
    attempts++;
    
    // Выбираем случайное направление
    const direction = directions[Math.floor(rng() * directions.length)];
    const newX = currentX + direction.dx;
    const newY = currentY + direction.dy;
    
    // Проверяем границы карты
    if (newX < 0 || newX >= worldSize || newY < 0 || newY >= worldSize) {
      continue;
    }
    
    const cellKey = `${newX},${newY}`;
    const cell = grid[cellKey];
    
    if (!cell) {
      continue;
    }
    
    // Проверяем, можно ли разместить дорогу в этой клетке
    if (canPlaceDecoration(cell, WorldMapDecorations.ROAD)) {
      if (cell.decoration === WorldMapDecorations.RIVER) {
        cell.decoration = WorldMapDecorations.BRIDGE;
      } else {
        cell.decoration = WorldMapDecorations.ROAD;
      }
      currentX = newX;
      currentY = newY;
      placedSegments++;
      
      // Освобождаем event loop каждые 5 сегментов без задержки
      if (placedSegments % 5 === 0) {
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }
}

/**
 * Проверяет, является ли соединение ортогональным (не диагональным)
 * Возвращает true только если точки находятся на одной горизонтали или вертикали
 */
function isOrthogonalConnection(x1: number, y1: number, x2: number, y2: number): boolean {
  // Соединение ортогонально, если точки находятся на одной линии (по X или по Y)
  return (x1 === x2) || (y1 === y2);
}

/**
 * Получает массив направлений, где нет стыка с такой же декорацией
 * Направления: 1 - север, 2 - восток, 3 - юг, 4 - запад
 */
function getDecorationBorder(
  grid: Record<string, WorldMapCell>,
  x: number,
  y: number,
  worldSize: number,
  decorationType: WorldMapDecorations
): number[] {
  const borders: number[] = [];
  
  // Направления: север, восток, юг, запад
  const directions = [
    { dx: 0, dy: -1, id: 1 }, // север
    { dx: 1, dy: 0, id: 2 },  // восток
    { dx: 0, dy: 1, id: 3 },  // юг
    { dx: -1, dy: 0, id: 4 }  // запад
  ];
  
  for (const direction of directions) {
    const newX = x + direction.dx;
    const newY = y + direction.dy;
    
    // Проверяем границы карты
    if (newX < 0 || newX >= worldSize || newY < 0 || newY >= worldSize) {
      borders.push(direction.id);
      continue;
    }
    
    const cellKey = `${newX},${newY}`;
    const cell = grid[cellKey];
    
    // Если клетки нет или декорация не совпадает - добавляем направление в borders
    if (!cell || cell.decoration !== decorationType) {
      borders.push(direction.id);
    }
  }
  
  return borders;
}