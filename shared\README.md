# Shared - NuclearStory

> **❗ ВНИМАНИЕ: НИКОГДА не импортируйте напрямую из корневого shared модуля! ❗**  
> **Всегда используйте локальную копию shared в src/shared вашего проекта.**
> 
> Например: `import { Type } from '../shared/types'` вместо `import { Type } from '@shared/types'`
>
> *Если вы попытаетесь импортировать из корневого модуля, это приведет к ошибкам в контейнерах Docker!*

Common models, types, and utilities shared between frontend and backend services.

## Contents

### Types
- `types/` - TypeScript type definitions
  - `user.types.ts` - User and authentication types
  - `game.types.ts` - Game state and mechanics types
  - `story.types.ts` - Story and narrative types
  - `api.types.ts` - API request/response types

### Models
- `models/` - Data models and interfaces
  - `User.ts` - User entity model
  - `GameState.ts` - Game state model
  - `Quest.ts` - Quest and event models
  - `Save.ts` - Save data model

### Utils
- `utils/` - Utility functions
  - `validation.ts` - Data validation helpers
  - `constants.ts` - Application constants
  - `helpers.ts` - Common helper functions

### Enums
- `enums/` - Shared enumerations
  - `GameStatus.ts` - Game status values
  - `UserRole.ts` - User role definitions
  - `EventType.ts` - Game event types

## Usage

### ВАЖНО: Модуль shared копируется в каждый сервис
Никогда не пытайтесь импортировать типы из корневого каталога shared!
Всегда используйте локальный модуль shared в директории src/shared вашего проекта.

### Frontend
```typescript
// ПРАВИЛЬНО - используйте локальный модуль shared
import { User, GameState } from '../shared/types';
import { validateEmail } from '../shared/utils';

// НЕПРАВИЛЬНО - никогда не импортируйте из корневого модуля
// import { User } from '@shared/types'; // НЕ ДЕЛАЙТЕ ТАК!
```

### Backend
```typescript
// ПРАВИЛЬНО - используйте локальный модуль shared
import { UserRole } from '../shared/enums';
import { CreateUserDto } from '../shared/models';

// НЕПРАВИЛЬНО - никогда не импортируйте из корневого модуля
// import { UserRole } from '@shared/enums'; // НЕ ДЕЛАЙТЕ ТАК!
```

## Development

### 🚨 ВАЖНО: ДОКЕР И КОПИРОВАНИЕ SHARED МОДУЛЕЙ 🚨

Модуль shared копируется в каждый сервис в процессе сборки Docker. 
Никогда не пытайтесь импортировать типы напрямую из корневого модуля shared!

Директории, куда копируется модуль shared:
```
back/auth-service/src/shared/
back/game-engine-service/src/shared/
back/save-service/src/shared/
back/world-generator-service/src/shared/
front/src/shared/
```

Всегда используйте локальную копию модуля из директории src/shared вашего проекта.

Любые изменения в shared должны быть совместимы как с frontend, так и с backend сервисами.

### Building
```bash
npm run build    # Compile TypeScript
npm run watch    # Watch mode for development
```
