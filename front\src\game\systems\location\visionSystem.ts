/**
 * Система видимости в локациях на основе времени дня и характеристик персонажа
 */

import { Position } from '../../../shared/types/Common';
import { Location } from '../../../shared/types/Location';
import { LocationDecorations } from '../../../shared/enums';
import { GameTime, isDaytime, getLightLevel } from '../../utils/time/gameTime';
import { gameTimeManager } from '../../utils/time/gameTimeManager';
import { VISION_CONSTANTS, DECORATION_LIGHT_CONSTANTS } from '../../utils/constants/timeLight';

// Кеш источников света для оптимизации
let lightSourcesCache: Map<string, Array<{ position: Position; decoration: LocationDecorations }>> = new Map();
let cacheLocationId: string | null = null;

/**
 * Получает радиус видимости персонажа в зависимости от времени дня и характеристик
 * @param perception - характеристика восприятия персонажа (parameters.Perception)
 * @param gameTime - текущее игровое время
 * @returns радиус видимости в клетках
 */
export function getVisionRadius(perception: number, gameTime?: GameTime): number {
  const currentTime = gameTime || gameTimeManager.getCurrentTime();
  const lightLevel = getLightLevel(currentTime);
  
  const baseNightRadius = Math.max(VISION_CONSTANTS.MIN_VISION_RADIUS, perception * VISION_CONSTANTS.NIGHT_VISION_MULTIPLIER + -2);
  
  const maxDayRadius = VISION_CONSTANTS.MAX_DAY_VISION_RADIUS;
  
  // Плавная интерполяция между ночным и дневным радиусом
  if (lightLevel >= VISION_CONSTANTS.LIGHT_TRANSITION_MIN + VISION_CONSTANTS.LIGHT_TRANSITION_RANGE) {
    return maxDayRadius; // Полная дневная видимость
  }
  
  // Плавный переход от ночного к дневному радиусу
  const transitionProgress = Math.max(0, (lightLevel - VISION_CONSTANTS.LIGHT_TRANSITION_MIN) / VISION_CONSTANTS.LIGHT_TRANSITION_RANGE);
  return baseNightRadius + (maxDayRadius - baseNightRadius) * transitionProgress;
}

/**
 * Проверяет, видима ли клетка для игрока в текущих условиях освещения
 * @param playerPosition - позиция игрока
 * @param targetPosition - проверяемая позиция
 * @param visionRadius - радиус видимости
 * @returns true если клетка видима
 */
export function isPositionVisible(
  playerPosition: Position,
  targetPosition: Position,
  visionRadius: number
): boolean {
  const distance = Math.sqrt(
    Math.pow(targetPosition.x - playerPosition.x, 2) + 
    Math.pow(targetPosition.y - playerPosition.y, 2)
  );
  
  return distance <= visionRadius;
}

/**
 * Строит кеш источников света для локации
 * @param location - данные локации
 * @param locationId - уникальный ID локации
 */
function buildLightSourcesCache(location: any, locationId: string): void {
  if (cacheLocationId === locationId && lightSourcesCache.size > 0) {
    return; // Кеш уже построен для этой локации
  }
  
  lightSourcesCache.clear();
  cacheLocationId = locationId;
  
  if (!location?.locationMap) {
    return;
  }
  
  // Проходим по всем тайлам и находим источники света
  for (const [tileKey, tile] of Object.entries(location.locationMap)) {
    if ((tile as any)?.decoration && isLightSource((tile as any).decoration)) {
      const [x, y] = tileKey.split(',').map(Number);
      const position = { x, y };
      const decoration = (tile as any).decoration;
      
      // Добавляем источник света в кеш по регионам для быстрого поиска
      const regionX = Math.floor(x / 10);
      const regionY = Math.floor(y / 10);
      const regionKey = `${regionX},${regionY}`;
      
      if (!lightSourcesCache.has(regionKey)) {
        lightSourcesCache.set(regionKey, []);
      }
      
      lightSourcesCache.get(regionKey)!.push({ position, decoration });
    }
  }
}

/**
 * Получает источники света в радиусе от позиции (с использованием кеша)
 * @param targetPosition - целевая позиция
 * @param searchRadius - радиус поиска
 * @returns массив источников света
 */
function getNearbyLightSources(targetPosition: Position, searchRadius: number): Array<{ position: Position; decoration: LocationDecorations }> {
  const nearbyLights: Array<{ position: Position; decoration: LocationDecorations }> = [];
  
  // Определяем регионы для поиска
  const minRegionX = Math.floor((targetPosition.x - searchRadius) / 10);
  const maxRegionX = Math.floor((targetPosition.x + searchRadius) / 10);
  const minRegionY = Math.floor((targetPosition.y - searchRadius) / 10);
  const maxRegionY = Math.floor((targetPosition.y + searchRadius) / 10);
  
  // Ищем в соответствующих регионах
  for (let regionX = minRegionX; regionX <= maxRegionX; regionX++) {
    for (let regionY = minRegionY; regionY <= maxRegionY; regionY++) {
      const regionKey = `${regionX},${regionY}`;
      const regionLights = lightSourcesCache.get(regionKey);
      
      if (regionLights) {
        for (const light of regionLights) {
          const distance = Math.sqrt(
            Math.pow(targetPosition.x - light.position.x, 2) + 
            Math.pow(targetPosition.y - light.position.y, 2)
          );
          
          if (distance <= searchRadius) {
            nearbyLights.push(light);
          }
        }
      }
    }
  }
  
  return nearbyLights;
}

/**
 * Получает уровень освещения из статической карты освещения
 * @param position - позиция для проверки
 * @param staticLightMap - статическая карта освещения
 * @returns уровень освещения (0-1)
 */
function getLightLevelFromStaticMap(position: Position, staticLightMap?: Array<[number, number, number]>): number {
  if (!staticLightMap || staticLightMap.length === 0) {
    return 0;
  }
  
  // Быстрый поиск точки в массиве
  for (const [x, y, lightLevel] of staticLightMap) {
    if (x === position.x && y === position.y) {
      return lightLevel;
    }
  }
  
  return 0;
}

/**
 * Получает все видимые позиции вокруг игрока
 * @param playerPosition - позиция игрока
 * @param location - текущая локация
 * @param perception - характеристика восприятия
 * @param gameTime - текущее игровое время (опционально)
 * @returns массив видимых позиций
 */
export function getVisiblePositions(
  playerPosition: Position,
  location: Location,
  perception: number,
  gameTime?: GameTime
): Position[] {
  const currentTime = gameTime || gameTimeManager.getCurrentTime();
  const visionRadius = getVisionRadius(perception, currentTime);
  const visiblePositions: Position[] = [];
  
  const locationSize = location.locationSize || { x: 10, y: 10 };
  
  // Проходим по всем клеткам в радиусе видимости
  for (let x = 0; x < locationSize.x; x++) {
    for (let y = 0; y < locationSize.y; y++) {
      const position = { x, y };
      
      if (isPositionVisible(playerPosition, position, visionRadius)) {
        visiblePositions.push(position);
      }
    }
  }
  
  return visiblePositions;
}

/**
 * Получает уровень затемнения для клетки (от 0 до 1, где 1 = полная тьма)
 * @param playerPosition - позиция игрока
 * @param targetPosition - проверяемая позиция
 * @param perception - характеристика восприятия
 * @param gameTime - текущее игровое время (опционально)
 * @param location - данные локации для поиска источников света (опционально)
 * @returns уровень затемнения от 0 до 1
 */
export function getDarknessLevel(
  playerPosition: Position,
  targetPosition: Position,
  perception: number,
  gameTime?: GameTime,
  location?: any
): number {
  const currentTime = gameTime || gameTimeManager.getCurrentTime();
  
  // Днем нет затемнения
  if (isDaytime(currentTime)) {
    return 0;
  }
  
  const visionRadius = getVisionRadius(perception, currentTime);
  const distance = Math.sqrt(
    Math.pow(targetPosition.x - playerPosition.x, 2) + 
    Math.pow(targetPosition.y - playerPosition.y, 2)
  );
  
  // Если в пределах видимости - нет затемнения
  if (distance <= visionRadius) {
    return 0;
  }
  
  const lightLevel = getLightLevel(currentTime);
  let baseDarkness = 1 - lightLevel; // Базовое затемнение зависит от времени суток
  
  // Проверяем освещение от декораций через статическую карту (ОПТИМИЗИРОВАННО!)
  if (location?.staticLightMap) {
    const decorationLightLevel = getLightLevelFromStaticMap(targetPosition, location.staticLightMap);
    
    // Уменьшаем затемнение пропорционально уровню освещения от декораций
    baseDarkness = Math.max(0, baseDarkness - decorationLightLevel);
  }
  

  
  // Плавный переход в ореоле
  if (distance <= visionRadius + VISION_CONSTANTS.DARKNESS_HALO_RADIUS) {
    const haloProgress = (distance - visionRadius) / VISION_CONSTANTS.DARKNESS_HALO_RADIUS;
    return haloProgress * VISION_CONSTANTS.HALO_DARKNESS_LEVEL * baseDarkness;
  }
  
  // Полное затемнение за пределами ореола
  return VISION_CONSTANTS.MAX_DARKNESS_LEVEL * baseDarkness;
}

/**
 * Определяет, является ли декорация источником света
 * @param decoration - тип декорации
 * @returns true если декорация светится
 */
export function isLightSource(decoration: LocationDecorations): boolean {
  return decoration === LocationDecorations.STREETLIGHT ||
         decoration === LocationDecorations.EXTERIORLIGHT ||
         decoration === LocationDecorations.INTERIORLIGHT;
}

/**
 * Получает параметры освещения для декорации
 * @param decoration - тип декорации
 * @returns объект с радиусом и интенсивностью освещения или null
 */
export function getLightParameters(decoration: LocationDecorations): { radius: number; intensity: number; color: { r: number; g: number; b: number } } | null {
  switch (decoration) {
    case LocationDecorations.STREETLIGHT:
      return {
        radius: DECORATION_LIGHT_CONSTANTS.STREETLIGHT_RADIUS,
        intensity: DECORATION_LIGHT_CONSTANTS.STREETLIGHT_INTENSITY,
        color: DECORATION_LIGHT_CONSTANTS.STREETLIGHT_COLOR
      };
    case LocationDecorations.EXTERIORLIGHT:
      return {
        radius: DECORATION_LIGHT_CONSTANTS.EXTERIOR_LIGHT_RADIUS,
        intensity: DECORATION_LIGHT_CONSTANTS.EXTERIOR_LIGHT_INTENSITY,
        color: DECORATION_LIGHT_CONSTANTS.EXTERIOR_LIGHT_COLOR
      };
    case LocationDecorations.INTERIORLIGHT:
      return {
        radius: DECORATION_LIGHT_CONSTANTS.INTERIOR_LIGHT_RADIUS,
        intensity: DECORATION_LIGHT_CONSTANTS.INTERIOR_LIGHT_INTENSITY,
        color: DECORATION_LIGHT_CONSTANTS.INTERIOR_LIGHT_COLOR
      };
    default:
      return null;
  }
}

/**
 * Вычисляет уровень освещения от источника света на определенном расстоянии
 * @param distance - расстояние до источника света
 * @param lightRadius - радиус действия света
 * @param lightIntensity - интенсивность света (0-1)
 * @returns уровень освещения (0-1)
 */
export function calculateLightLevel(distance: number, lightRadius: number, lightIntensity: number): number {
  if (distance > lightRadius) {
    return 0;
  }
  
  // Квадратичное падение света с расстоянием
  const falloff = Math.pow(1 - (distance / lightRadius), DECORATION_LIGHT_CONSTANTS.LIGHT_FALLOFF_POWER);
  return Math.max(
    DECORATION_LIGHT_CONSTANTS.MIN_LIGHT_LEVEL,
    Math.min(DECORATION_LIGHT_CONSTANTS.MAX_LIGHT_LEVEL, falloff * lightIntensity)
  );
}
