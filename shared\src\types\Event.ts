import { EventType } from '../enums'

export interface EventChoice {
  id: string
  text: string
  description?: string
  
  // Requirements to show this choice
  requirements?: {
    skills?: Record<string, number>
    items?: string[] // Item IDs
    reputation?: Record<string, number> // Faction ID -> min reputation
    stats?: Record<string, number> // SPECIAL stats
    questCompleted?: string[] // Quest IDs
    level?: number
  }
  
  // Consequences of choosing this option
  consequences: {
    // Items
    addItems?: string[] // Item IDs
    removeItems?: string[] // Item IDs
    
    // Stats and health
    healthChange?: number
    radiationChange?: number
    statChanges?: Record<string, number> // SPECIAL stat changes
    
    // Reputation and relationships
    reputationChanges?: Record<string, number> // Faction ID -> change
    
    // Quests
    startQuest?: string // Quest ID
    completeQuest?: string // Quest ID
    failQuest?: string // Quest ID
    
    // Location and movement
    teleportTo?: string // Location ID
    unlockLocation?: string // Location ID
    
    // NPCs and spawning
    spawnNPCs?: string[] // NPC IDs
    killNPCs?: string[] // NPC IDs
    
    // Currency and resources
    currencyChange?: number
    
    // Experience and progression
    experienceGain?: number
    skillExperience?: Record<string, number> // Skill -> XP
    
    // Follow-up events
    triggerEvent?: string // Event ID
    preventEvent?: string // Event ID
    
    // Text and narrative
    resultText?: string
    narrativeFlag?: string // Set a story flag
  }
  
  // Probability and randomness
  successChance?: number // 0-100, for skill checks
  skillCheck?: {
    skill: string
    difficulty: number
    criticalSuccess?: number // Additional bonus on high roll
    criticalFailure?: number // Additional penalty on low roll
  }
}

export interface EventCondition {
  type: 'time' | 'location' | 'quest' | 'item' | 'reputation' | 'level' | 'random' | 'flag'
  
  // Time conditions
  timeOfDay?: 'day' | 'night' | 'dawn' | 'dusk'
  dayOfWeek?: number // 0-6
  gameTime?: {
    after?: Date
    before?: Date
  }
  
  // Location conditions
  locationId?: string
  locationType?: string
  
  // Quest conditions
  questActive?: string
  questCompleted?: string
  questFailed?: string
  
  // Item conditions
  hasItem?: string
  itemQuantity?: number
  
  // Reputation conditions
  factionReputation?: {
    factionId: string
    minimum: number
    maximum?: number
  }
  
  // Level and stats
  playerLevel?: {
    minimum?: number
    maximum?: number
  }
  playerStats?: Record<string, number>
  
  // Random chance
  probability?: number // 0-100
  
  // Story flags
  narrativeFlag?: {
    flag: string
    value: boolean
  }
}

export interface Event {
  id: string
  name: string
  description: string
  type: EventType
  
  // Content
  title: string
  text: string
  flavorText?: string
  
  // Choices and interaction
  choices: EventChoice[]
  hasChoices: boolean
  autoResolve: boolean // Automatically picks first valid choice
  
  // Conditions for triggering
  triggerConditions: EventCondition[]
  
  // Frequency and repetition
  isRepeatable: boolean
  cooldownTime?: number // minutes before can trigger again
  maxOccurrences?: number
  currentOccurrences: number
  
  // Priority and timing
  priority: number // Higher priority events trigger first
  duration?: number // How long event lasts (for ongoing events)
  
  // Location and scope
  locationId?: string // Specific location, or null for global
  radius?: number // Area of effect around location
  
  // Requirements to trigger
  requirements?: {
    playerLevel?: number
    questsCompleted?: string[]
    itemsRequired?: string[]
    factionsRequired?: Record<string, number> // Faction -> min reputation
  }
  
  // Visual and audio
  image?: string
  sound?: string
  music?: string
  
  // Narrative impact
  storyWeight: number // How important this event is to the story
  consequenceLevel: 'minor' | 'moderate' | 'major' | 'critical'
  
  // Metadata
  tags: string[] // For categorization and filtering
  category: string
  
  // State tracking
  isTriggered: boolean
  isCompleted: boolean
  isActive: boolean
  
  // Player history
  playerChoices: string[] // Choice IDs player has made
  lastTriggeredAt?: Date
  firstTriggeredAt?: Date
  
  // Timestamps
  createdAt: Date
  lastUpdatedAt: Date
}
