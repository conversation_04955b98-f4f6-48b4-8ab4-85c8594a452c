# Улучшения тумана войны в локациях

## Проблема
Туман войны в локациях открывался только в начале и конце движения персонажа, а не на каждом шаге. Также использовался фиксированный радиус 10 клеток вместо расчета на основе характеристики Восприятие (P).

## Решение

### 1. Исправлены константы тумана войны (`rendering.ts`)
```typescript
// Было:
export const LOCATION_FOG_SETTINGS = 20;

// Стало:
export const LOCATION_FOG_SETTINGS = {
  MIN_VISION_RADIUS: 1,        // Минимальный радиус видимости
  MAX_VISION_RADIUS: 6,        // Максимальный радиус видимости
  PERCEPTION_MULTIPLIER: 1.5,  // Множитель восприятия (P * 1.5)
  DEFAULT_RADIUS: 2           // Радиус по умолчанию
};
```

### 2. Добавлен новый метод в GameStore (`gameStore.tsx`)
```typescript
updatePlayerPositionInLocation: (newPosition: Position) => void
```

Этот метод:
- Вычисляет радиус видимости как `player.special.P * 1.5`
- Ограничивает радиус от 1 до 6 клеток
- Открывает туман войны на новой позиции
- Обновляет позицию игрока в сторе

### 3. Упрощен код движения в локации (`locationMovement.ts`)
- Убрана дублированная логика открытия тумана войны
- Теперь используется единый метод `updatePlayerPositionInLocation` 
- Туман войны открывается на **каждом шаге** движения

## Формула радиуса видимости
```
visionRadius = Math.max(1, Math.min(6, Math.floor(P * 1.5)))
```

Где:
- P - характеристика Восприятие игрока
- Минимум: 1 клетка
- Максимум: 6 клеток
- Множитель: 1.5

## Результат
Теперь туман войны открывается плавно по ходу движения персонажа, с правильным радиусом видимости, основанным на характеристике Восприятие.
