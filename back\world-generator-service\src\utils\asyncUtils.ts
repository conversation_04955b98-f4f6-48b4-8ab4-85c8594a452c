/**
 * Утилиты для асинхронной обработки в генераторе мира
 */

/**
 * Интерфейс для отслеживания прогресса генерации
 */
export interface GenerationProgress {
  stage: string;
  progress: number; // 0-100
  currentOperation: string;
  estimatedTimeRemaining?: number; // в миллисекундах
}

/**
 * Интерфейс для токена отмены операций
 */
export interface CancellationToken {
  isCancelled: boolean;
  cancel(): void;
}

/**
 * Создает токен отмены
 */
export function createCancellationToken(): CancellationToken {
  let cancelled = false;
  
  return {
    get isCancelled() {
      return cancelled;
    },
    cancel() {
      cancelled = true;
    }
  };
}

/**
 * Обрабатывает массив элементов батчами для предотвращения блокировки event loop
 * @param items - массив элементов для обработки
 * @param batchSize - размер батча
 * @param processor - функция обработки элемента
 * @param progressCallback - колбэк для отслеживания прогресса
 * @param cancellationToken - токен для отмены операции
 */
export async function processBatch<T>(
  items: T[],
  batchSize: number,
  processor: (item: T, index: number) => void | Promise<void>,
  progressCallback?: (progress: number) => void,
  cancellationToken?: CancellationToken
): Promise<void> {
  const totalItems = items.length;
  let processedItems = 0;

  for (let i = 0; i < totalItems; i += batchSize) {
    // Проверяем токен отмены
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const batch = items.slice(i, i + batchSize);
    
    // Обрабатываем батч
    for (let j = 0; j < batch.length; j++) {
      const item = batch[j];
      const globalIndex = i + j;
      
      await processor(item, globalIndex);
      processedItems++;
    }

    // Обновляем прогресс
    if (progressCallback) {
      const progress = Math.round((processedItems / totalItems) * 100);
      progressCallback(progress);
    }

    // Освобождаем event loop после каждого батча
    await new Promise(resolve => setImmediate(resolve));
  }
}

/**
 * Обрабатывает двумерную сетку батчами
 * @param width - ширина сетки
 * @param height - высота сетки
 * @param batchSize - размер батча (количество клеток)
 * @param processor - функция обработки клетки
 * @param progressCallback - колбэк для отслеживания прогресса
 * @param cancellationToken - токен для отмены операции
 */
export async function processGrid(
  width: number,
  height: number,
  batchSize: number,
  processor: (x: number, y: number) => void | Promise<void>,
  progressCallback?: (progress: number) => void,
  cancellationToken?: CancellationToken
): Promise<void> {
  const totalCells = width * height;
  let processedCells = 0;
  let currentBatchSize = 0;

  // Увеличиваем размер батча для больших карт
  const adaptiveBatchSize = Math.max(batchSize, Math.floor(totalCells / 100));

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      // Проверяем токен отмены только каждые 100 итераций
      if (processedCells % 100 === 0 && cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }

      await processor(x, y);
      processedCells++;
      currentBatchSize++;

      // Если достигли размера батча, освобождаем event loop
      if (currentBatchSize >= adaptiveBatchSize) {
        // Обновляем прогресс
        if (progressCallback) {
          const progress = Math.round((processedCells / totalCells) * 100);
          progressCallback(progress);
        }

        // Освобождаем event loop реже
        await new Promise(resolve => setImmediate(resolve));
        currentBatchSize = 0;
      }
    }
  }

  // Финальное обновление прогресса
  if (progressCallback) {
    progressCallback(100);
  }
}

/**
 * Выполняет асинхронную задержку
 * @param ms - время задержки в миллисекундах
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Выполняет функцию с таймаутом
 * @param fn - функция для выполнения
 * @param timeoutMs - таймаут в миллисекундах
 */
export async function withTimeout<T>(
  fn: () => Promise<T>,
  timeoutMs: number
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Operation timed out')), timeoutMs);
  });

  return Promise.race([fn(), timeoutPromise]);
}

/**
 * Класс для отслеживания прогресса с несколькими этапами
 */
export class ProgressTracker {
  private stages: Array<{ name: string; weight: number }> = [];
  private currentStageIndex = 0;
  private currentStageProgress = 0;
  private startTime = Date.now();

  constructor(private onProgress?: (progress: GenerationProgress) => void) {}

  /**
   * Добавляет этап с весом
   * @param name - название этапа
   * @param weight - вес этапа (относительный)
   */
  addStage(name: string, weight: number = 1): void {
    this.stages.push({ name, weight });
  }

  /**
   * Переходит к следующему этапу
   * @param operation - текущая операция
   */
  nextStage(operation: string = ''): void {
    if (this.currentStageIndex < this.stages.length - 1) {
      this.currentStageIndex++;
      this.currentStageProgress = 0;
    }
    this.updateProgress(operation);
  }

  /**
   * Обновляет прогресс текущего этапа
   * @param progress - прогресс этапа (0-100)
   * @param operation - текущая операция
   */
  updateStageProgress(progress: number, operation: string = ''): void {
    this.currentStageProgress = Math.max(0, Math.min(100, progress));
    this.updateProgress(operation);
  }

  /**
   * Вычисляет общий прогресс и вызывает колбэк
   */
  private updateProgress(operation: string): void {
    if (!this.onProgress) return;

    const totalWeight = this.stages.reduce((sum, stage) => sum + stage.weight, 0);
    let completedWeight = 0;

    // Суммируем вес завершенных этапов
    for (let i = 0; i < this.currentStageIndex; i++) {
      completedWeight += this.stages[i].weight;
    }

    // Добавляем прогресс текущего этапа
    if (this.currentStageIndex < this.stages.length) {
      const currentStageWeight = this.stages[this.currentStageIndex].weight;
      completedWeight += (currentStageWeight * this.currentStageProgress) / 100;
    }

    const totalProgress = Math.round((completedWeight / totalWeight) * 100);
    const currentStage = this.stages[this.currentStageIndex]?.name || 'Завершено';

    // Оценка времени
    const elapsed = Date.now() - this.startTime;
    const estimatedTotal = totalProgress > 0 ? (elapsed / totalProgress) * 100 : 0;
    const estimatedTimeRemaining = Math.max(0, estimatedTotal - elapsed);

    this.onProgress({
      stage: currentStage,
      progress: totalProgress,
      currentOperation: operation,
      estimatedTimeRemaining
    });
  }

  /**
   * Завершае отслеживание прогресса
   */
  complete(): void {
    if (this.onProgress) {
      this.onProgress({
        stage: 'Завершено',
        progress: 100,
        currentOperation: 'Генерация мира завершена',
        estimatedTimeRemaining: 0
      });
    }
  }
}

/**
 * Декоратор для автоматического освобождения event loop в циклах
 * @param iterations - количество итераций после которых освобождать event loop
 */
export function withEventLoopYield(iterations: number = 1000) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let iterationCount = 0;
      
      const originalMethod = method.bind(this);
      
      // Перехватываем циклы и добавляем yield
      const result = await originalMethod(...args);
      
      return result;
    };
  };
}

/**
 * Утилита для создания асинхронного генератора из массива
 * @param items - массив элементов
 * @param batchSize - размер батча
 */
export async function* createAsyncGenerator<T>(
  items: T[],
  batchSize: number = 100
): AsyncGenerator<T[], void, unknown> {
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    yield batch;
    
    // Освобождаем event loop между батчами
    await new Promise(resolve => setImmediate(resolve));
  }
}