/**
 * Настройки клавиш для управления игрой
 * Все клавиши вынесены в один файл для удобной настройки
 */

export const KEY_BINDS = {
  // Движение игрока
  MOVE_PLAYER: 'Enter',           // Запуск движения по пути
  
  // Локации
  ENTER_LOCATION: ' ',            // Вход в локацию (пробел)
  EXIT_LOCATION: ' ',        // Выход из локации
  
  // Управление камерой
  CAMERA_UP: 'ArrowUp',           // Камера вверх
  CAMERA_DOWN: 'ArrowDown',       // Камера вниз
  CAMERA_LEFT: 'ArrowLeft',       // Камера влево
  CAMERA_RIGHT: 'ArrowRight',     // Камера вправо
  
  // Мышь
  CAMERA_DRAG_BUTTON: 2,          // Правая кнопка мыши для перетаскивания камеры (0=левая, 1=средняя, 2=права��)
  CONTEXT_MENU_BUTTON: 2,         // Правая кнопка мыши для контекстного меню
  
  // Настройки управления камерой
  CAMERA_CONTROL_MODE: 'drag',    // 'drag' - перетаскивание мышью, 'cursor' - следование за курсором
  CAMERA_CURSOR_SPEED: 25,         // Скорость движения камеры при следовании за курсором
  CAMERA_CURSOR_EDGE_SIZE: 50,    // Размер области у края экрана для движения камеры (пиксели)
  
  // Настройки контекстного меню
  CONTEXT_MENU_DELAY: 1000,        // Задержка в мс для показа контекстного меню при зажатии
  CONTEXT_MENU_MOVE_TOLERANCE: 5, // Допустимое движение мыши в пикселях для контекстного меню
  
  // Задержки
  CAMERA_CENTER_DELAY: 20,       // Задержка перед центрированием камеры при входе/выходе из локации (мс)
} as const;

/**
 * Проверяет, соответствует ли нажатая клавиша заданной клавише
 */
export function isKeyPressed(event: KeyboardEvent, keyBind: string): boolean {
  return event.key === keyBind;
}

/**
 * Проверяет, соответствует ли нажатая кнопка мыши заданной кнопке
 */
export function isMouseButtonPressed(event: MouseEvent | React.MouseEvent, button: number): boolean {
  return event.button === button;
}

/**
 * Проверяет, зажата ли кнопка мыши
 */
export function isMouseButtonHeld(event: MouseEvent | React.MouseEvent, button: number): boolean {
  return (event.buttons & (1 << button)) !== 0;
}

/**
 * Описания клавиш для UI
 */
export const KEY_DESCRIPTIONS = {
  [KEY_BINDS.MOVE_PLAYER]: 'Запустить движение по пути',
  [KEY_BINDS.ENTER_LOCATION]: 'Войти в локацию',
  [KEY_BINDS.EXIT_LOCATION]: 'Выйти из локации',
  [KEY_BINDS.CAMERA_UP]: 'Камера вверх',
  [KEY_BINDS.CAMERA_DOWN]: 'Камера вниз',
  [KEY_BINDS.CAMERA_LEFT]: 'Камера влево',
  [KEY_BINDS.CAMERA_RIGHT]: 'Камера вправо',
} as const;

/**
 * Названия кнопок мыши для UI
 */
export const MOUSE_BUTTON_NAMES = {
  0: 'Левая кнопка мыши',
  1: 'Средняя кнопка мыши',
  2: 'Правая кнопка мыши',
} as const;