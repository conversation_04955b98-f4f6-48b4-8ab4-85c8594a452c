import { WorldSettings, WorldSummary, WorldMap } from './World'

// Специфичные типы для API запросов и ответов

export interface CreateWorldRequest {
  name: string
  description: string
  userId: string
  settings?: WorldSettings
}

export interface CreateWorldResponse {
  success: boolean
  world: WorldMap
}

export interface GetWorldsResponse {
  worlds: WorldSummary[]
}

export interface GetWorldResponse {
  success: boolean
  world: WorldMap
}

export interface UpdateWorldResponse {
  success: boolean
  error?: string
}

export interface SaveWorldResponse {
  success: boolean
  worldId?: string
  error?: string
}

export interface DeleteWorldResponse {
  success: boolean
}