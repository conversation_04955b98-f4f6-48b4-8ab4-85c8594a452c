/**
 * Типы для системы шаблонов декораций
 */

// Значения в шаблонах
export enum PatternValue {
  EMPTY = 0,        // Ничего (не перезаписывать)
  MOUNTAINS = 1,    // Горы
  FOREST = 2,       // Лес
  BUSHES = 3,       // Кусты
  LAKE = 4,         // Озеро
  RIVER = 5,        // Река
  RUINS = 6,        // Руины
  RUBBLE = 7,       // Мусор
  SWAMP = 8,        // Болото
  CITY = 9,         // Город
  ROAD = 10         // Дорога
}

// Матрица шаблона (2D массив чисел)
export type PatternMatrix = number[][];

// Отдельный шаблон
export interface DecorationPattern {
  id: string;
  name: string;
  matrix: PatternMatrix;
  width: number;
  height: number;
  centerX: number;  // Координата центра для размещения на маркере
  centerY: number;
  description?: string;
}

// Коллекция шаблонов одного типа
export interface PatternCollection {
  type: string;
  patterns: Record<string, PatternMatrix>;
}

// Результат применения шаблона
export interface PatternApplicationResult {
  success: boolean;
  appliedCells: number;
  skippedCells: number;
  outOfBounds: number;
  pattern: DecorationPattern;
  position: { x: number; y: number };
  rotation?: number;
}