import * as React from 'react'
import { useRef, useState, useCallback, useEffect, useMemo } from 'react'
import styles from './IsometricWorldMap.module.css'
import { WorldMap } from '../../../../shared/types/World'
import { useGameStore } from '@/game/store/gameStore'
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  MIN_ZOOM,
  MAX_ZOOM,
  ZOOM_STEP,
  IMCOMP_MAX_WIDTH,
  IMCOMP_MAX_HEIGHT
} from '../../../utils/constants/rendering'
import { useContextMenuDisable, useTerrainTextures, useDecorationTextures } from '../../hooks'
import {
  useCameraCenter,
  useCameraUI,
  useKeyboardControls,
  useCameraToPlayer
} from '../../../systems/camera/hooks'
import {
  useRenderLoop,
  useInitialDraw,
  useAdaptiveScreenSize,
  useCellTarget
} from '../../hooks'
import { getCameraDisplayCoordinates } from '../../../utils/coordinates/isometric'
import {
  createMouseMoveHandler,
  createZoom<PERSON>hangeHandler,
  createClickHandler,
  createRightClickHandlers,
  CameraRef
} from '../../../systems/interaction/eventHandlers'

import { createDrawFunction } from '../drawEngine'
import { createLocationDrawFunction } from '../../IsometricLocations/drawEngineLocInterior'
import { InfoTerminal } from '../../../ui/panels/InfoTerminal'
import { ContextMenu } from '../../../ui/controls/ContextMenu'
import {
  getCurrentPath,
  clearPath,
  revealFogOfWar,
  convertSpeedToMs
} from '../../../systems/movement/playerMovement'
import {
  generateInspectMessage,
  generateMovementCostMessage
} from '../../../ui/panels/InfoTerminal/messages'
import { useGameTime } from '../../hooks'
import { InfoPanel } from '../../../ui/panels/InfoPanel'
import { usePlayerPathMovement } from '@/game/systems/movement/hooks'
import { createLocationClickHandler, createLocationRightClickHandlers, createLocationKeyDownHandler } from '@/game/systems/location/eventHandlersLocation'
import { TimeControls } from '../../../ui/time/TimeControls'
import { SystemMenu } from '../../../ui/system/SystemMenu'
import { Camera, Search } from 'lucide-react'
import { isoToScreen, getTileCenterOnScreen } from '@/game/utils/coordinates/isometric';
import { BASE_TILE_HEIGHT_LOCATION, BASE_TILE_WIDTH_LOCATION } from '@/game/utils/constants/renderingLocation'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
  onSave?: () => void
  onLoad?: () => void
  onSettings?: () => void
  onExit?: () => void
  onToggleMute?: () => void
  isMuted?: boolean
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  currentWorld,
  onSave = () => {},
  onLoad = () => {},
  onSettings = () => {},
  onExit = () => {},
  onToggleMute = () => {},
  isMuted = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const cameraRef = useRef<CameraRef>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState({ x: 0, y: 0 })
  const { width, height } = useAdaptiveScreenSize(IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT)
  const [cellTarget, setCellTarget] = useCellTarget()
  const { currentWorld: storeCurrentWorld, setCurrentWorld, playerLocationPresent, setPlayerLocationPresent } = useGameStore()
  const gameTime = useGameTime()

  // Состояние для контекстного меню
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    tileData: { isoX: number; isoY: number; tileData: any } | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    tileData: null
  })

  // Функция для добавления сообщений в терминал
  const [addTerminalMessage, setAddTerminalMessage] = useState<((message: string) => void) | null>(null)

  // Простой зум через canvas scale - размеры тайлов остаются базовыми
  // Если персонаж в локации — используем размеры локации
  const { tileWidth, tileHeight } = playerLocationPresent
    ? { tileWidth: BASE_TILE_WIDTH_LOCATION, tileHeight: BASE_TILE_HEIGHT_LOCATION }
    : { tileWidth: BASE_TILE_WIDTH, tileHeight: BASE_TILE_HEIGHT }

  // Предзагружаем текстуры местности и декораций
  useTerrainTextures()
  useDecorationTextures()

  // Туман войны теперь реализован через простую прозрачность - как в Fallout!

  // Создаем функцию отрисовки - теперь с зумом через canvas scale
  const draw = useCallback(() => {
    const drawFn = createDrawFunction(
      canvasRef,
      cameraRef,
      currentWorld,
      tileWidth,
      tileHeight,
      width,
      height,
      cellTarget,
      zoom,
      gameTime
    );
    drawFn();
  }, [currentWorld, tileWidth, tileHeight, width, height, cellTarget, zoom, gameTime])

  // Создаем функцию отрисовки локации
  const drawLocation = useCallback(() => {
    if (playerLocationPresent) {
      const currentLocation = useGameStore.getState().currentLocation;
      const locationDrawFn = createLocationDrawFunction(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        cellTarget,
        zoom,
        currentLocation,
        gameTime
      );
      locationDrawFn();
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, cellTarget, zoom, playerLocationPresent, gameTime])

  // Создаем объединенную функцию рендера
  const renderFrame = useCallback(() => {
    // Всегда вызываем world map рендер (он сам проверяет playerLocationPresent и может не рендерить)
    draw();
    // И также вызываем location рендер (он сам проверяет playerLocationPresent)
    drawLocation();
  }, [draw, drawLocation])

  // Хук для выбранной клетки

  // Создаем обработчики событий
  const handleZoomChange = createZoomChangeHandler(setZoom)
  const handleMouseMove = useCallback(() => {
    return createMouseMoveHandler(
      cameraRef,
      canvasRef
    );
  }, [cameraRef, canvasRef])
  
  const handleClick = useCallback(() => {
    // Используем разные обработчики кликов в зависимости от того, в локации ли игрок
    if (playerLocationPresent) {
      return createLocationClickHandler(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        setCellTarget,
        cellTarget,
        zoom
      );
    } else {
      return createClickHandler(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        setCellTarget,
        cellTarget,
        zoom
      );
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, setCellTarget, cellTarget, zoom, playerLocationPresent])

  // Обработчики правого клика с отслеживанием времени зажатия
  const rightClickHandlers = useMemo(() => {
    // Используем разные обработчики правого клика в зависимости от того, в локации ли игрок
    if (playerLocationPresent) {
      return createLocationRightClickHandlers(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        zoom,
        (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => {
          setContextMenu({
            visible: true,
            x,
            y,
            tileData
          })
        }
      );
    } else {
      return createRightClickHandlers(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        zoom,
        (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => {
          setContextMenu({
            visible: true,
            x,
            y,
            tileData
          })
        }
      );
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, zoom, playerLocationPresent])

  // Обработчики контекстного меню
  const handleContextMenuClose = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }))
  }, [])

  const handleInspect = useCallback(() => {
    if (contextMenu.tileData && addTerminalMessage) {
      const message = generateInspectMessage(contextMenu.tileData)
      addTerminalMessage(message)
    }
    handleContextMenuClose()
  }, [contextMenu.tileData, addTerminalMessage, handleContextMenuClose])

  const handleMovementCost = useCallback(() => {
    if (contextMenu.tileData && addTerminalMessage) {
      const message = generateMovementCostMessage(contextMenu.tileData)
      addTerminalMessage(message)
    }
    handleContextMenuClose()
  }, [contextMenu.tileData, addTerminalMessage, handleContextMenuClose])

  // Колбэки для движения игрока
  // Хук для движения игрока по пути
  const currentPath = getCurrentPath()
  const playerPosition = currentWorld?.player?.position || { x: 0, y: 0 }
  // Получаем скорость из настроек мира и конвертируем в миллисекунды
  const movementSpeed = convertSpeedToMs(currentWorld?.settings?.worldMapPlayerSpeed || 3)
  
  usePlayerPathMovement(playerPosition, currentPath, movementSpeed, currentWorld)

  // Видео тумана войны теперь рендерится в канвасе

  // Подключаем хуки
  useCameraCenter(currentWorld, cameraRef, tileWidth, tileHeight)
  useRenderLoop(renderFrame)
  useCameraUI(cameraRef, setCameraUI)
  useKeyboardControls(cameraRef)
  
  // Хук для перемещения камеры на игрока
  const centerCameraOnPlayer = useCameraToPlayer(currentWorld, cameraRef, tileWidth, tileHeight)
  
  // Слушатель для автоматического перемещения камеры на игрока при входе/выходе из локации
  useEffect(() => {
    const handleCenterCamera = () => {
      centerCameraOnPlayer();
    };
    
    window.addEventListener('centerCameraOnPlayer', handleCenterCamera);
    
    return () => {
      window.removeEventListener('centerCameraOnPlayer', handleCenterCamera);
    };
  }, [centerCameraOnPlayer])
  
  // Обработчик клавиш для локаций (вход и выход)
  useEffect(() => {
    const handleLocationKeyDown = createLocationKeyDownHandler(currentWorld);
    window.addEventListener('keydown', handleLocationKeyDown);
    return () => {
      window.removeEventListener('keydown', handleLocationKeyDown);
    };
  }, [currentWorld])
  
  // Простой обработчик колеса мыши для зума
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      const zoomDelta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
      const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoom + zoomDelta));
      setZoom(newZoom);
    };

    canvas.addEventListener('wheel', handleWheel, { passive: false });
    return () => canvas.removeEventListener('wheel', handleWheel);
  }, [zoom])
  useContextMenuDisable(canvasRef)
  useInitialDraw(renderFrame)

  // Обновляем позицию контекстного меню при движении камеры или изменении зума
  useEffect(() => {
    if (!contextMenu.visible || !contextMenu.tileData) return;
    
    const { isoX, isoY } = contextMenu.tileData;
    if (isoX == null || isoY == null) return;
    
    const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
    const { centerX, centerY } = getTileCenterOnScreen(
      screenX,
      screenY,
      width,
      height,
      cameraUI.x,
      cameraUI.y
    );
    
    // Обновляем позицию только если она изменилась
    if (contextMenu.x !== centerX || contextMenu.y !== centerY) {
      setContextMenu((prev) => ({ 
        ...prev, 
        x: centerX, 
        y: centerY 
      }));
    }
  }, [contextMenu.visible, contextMenu.tileData, contextMenu.x, contextMenu.y, tileWidth, tileHeight, width, height, cameraUI.x, cameraUI.y, zoom])

  return (
    <div className={styles.mapContainer}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove()}
        onClick={handleClick()}
        onMouseDown={rightClickHandlers.handleMouseDown}
        onMouseUp={rightClickHandlers.handleMouseUp}
        onContextMenu={rightClickHandlers.handleContextMenu}
        style={{ cursor: 'default' }}
      />
      <InfoPanel 
        displayCoordinates={getCameraDisplayCoordinates(
          cameraUI.x,
          cameraUI.y,
          currentWorld?.settings?.worldSize || 20,
          tileWidth,
          tileHeight
        )}
        zoom={zoom}
        onZoomChange={handleZoomChange}
        onCenterOnPlayer={centerCameraOnPlayer}
      />
      <InfoTerminal 
        cellTarget={cellTarget} 
        onAddMessage={(fn) => setAddTerminalMessage(() => fn)}
      />
      <ContextMenu
        x={contextMenu.x}
        y={contextMenu.y}
        visible={contextMenu.visible}
        onClose={handleContextMenuClose}
        onInspect={handleInspect}
        onMovementCost={handleMovementCost}
      />
      
      {/* Встроенные HUD компоненты */}
      <div style={{ 
        position: 'absolute', 
        top: '20px', 
        left: '20px', 
        zIndex: 100 
      }}>
        <TimeControls />
      </div>
      <div style={{ 
        position: 'absolute', 
        bottom: '120px', 
        right: '20px', 
        zIndex: 100,
        cursor: 'pointer',
        border: '1px solid var(--primary-color)' ,
        padding: '8px 10px 5px ',
        borderRadius: '8px',
      }}  onClick ={useCameraToPlayer(currentWorld, cameraRef, tileWidth, tileHeight)}>
        <Search size={28} />
      </div>
      <div style={{ 
        position: 'absolute', 
        bottom: '40px', 
        right: '20px', 
        zIndex: 100 
      }}>
        <SystemMenu
          onSave={onSave}
          onLoad={onLoad}
          onSettings={onSettings}
          onExit={onExit}
          onToggleMute={onToggleMute}
          isMuted={isMuted}
        />
      </div>
    </div>
  )
}

export default IsometricMap