import { Navigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'

interface ProtectedRouteProps {
  children: React.ReactNode
}

/**
 * Компонент защищенного маршрута
 * Перенаправляет на страницу входа, если пользователь не авторизован
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated } = useAuthStore()
  const location = useLocation()

  if (!isAuthenticated) {
    // Перенаправляем на /login и сохраняем информацию о том, 
    // куда пользователь хотел попасть изначально
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
}
