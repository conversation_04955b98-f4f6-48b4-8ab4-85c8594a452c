/**
 * Система отрисовки паттерновых текстур для мировой карты
 * Базовые классы и общие функции для всех типов паттерновых текстур
 */

import { WorldMapCell } from '../../../../shared/types/World';
import { WorldMapDecorations } from '../../../../shared/enums';
import { DECORATION_TEXTURE_SETTINGS } from '../../../utils/constants/rendering';
import { lakeTextureManager } from './textureLake';
import { riverTextureManager } from './textureRiver';
import { roadsTextureManager } from './textureRoads';

/**
 * Универсальная функция отрисовки текстуры с настройками
 */
export function drawTextureWithSettings(
  ctx: CanvasRenderingContext2D,
  texture: HTMLImageElement,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decorationType: string
): void {
  // Сохраняем состояние контекста
  ctx.save();

  // Создаем ромбовидную область отсечения
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();
  ctx.clip();

  // Вычисляем размеры с учетом настроек
  let drawW: number;
  let drawH: number;

  if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
    // Получаем множитель масштаба
    const scaleMultiplier = (DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS as any)[decorationType] || 1.0;
    
    // Применяем базовые размеры и множитель
    drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;
    
    // Сохраняем пропорции если нужно
    if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        // Широкое изображение - подгоняем высоту
        drawH = drawW / aspectRatio;
      } else {
        // Высокое изображение - подгоняем ширину
        drawW = drawH * aspectRatio;
      }
    }
  } else {
    // Старый способ - покрытие ромба
    const tileWidth = halfTileW * 2;
    const tileHeight = halfTileH * 2;
    const imgRatio = texture.width / texture.height;
    const tileRatio = tileWidth / tileHeight;

    drawW = tileWidth;
    drawH = tileHeight;

    if (imgRatio > tileRatio) {
      // Изображение шире ромба - увеличиваем высоту
      drawH = tileWidth / imgRatio;
      if (drawH < tileHeight) {
        drawH = tileHeight;
        drawW = drawH * imgRatio;
      }
    } else {
      // Изображение выше ромба - увеличиваем ширину
      drawW = tileHeight * imgRatio;
      if (drawW < tileWidth) {
        drawW = tileWidth;
        drawH = drawW / imgRatio;
      }
    }
  }

  // Вычисляем смещение позиции если включено
  let offsetX = 0;
  let offsetY = 0;
  
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    offsetX = (DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET as any)[decorationType] || 0;
    offsetY = (DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET as any)[decorationType] || 0;
  }

  // Отрисовываем текстуру с вычисленными размерами и смещением
  ctx.drawImage(
    texture,
    centerX - drawW / 2 + offsetX,
    centerY - drawH / 2 + offsetY,
    drawW,
    drawH
  );

  // Восстанавливаем состояние контекста
  ctx.restore();
}

/**
 * Отрисовывает паттерновую текстуру озера на основе данных тайла
 */
export const drawLakePatternTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell
): void => {
  // Проверяем, что это озеро с паттерновыми данными
  if (
    tileData.decoration !== WorldMapDecorations.LAKE ||
    !tileData.decorationBorder ||
    !Array.isArray(tileData.decorationBorder) ||
    tileData.decorationBorder.length === 0 ||
    !tileData.imgDirection
  ) {
    return;
  }

  // Получаем загруженную текстуру
  const texture = lakeTextureManager.getLoadedPatternTexture(
    tileData.decorationBorder,
    tileData.imgDirection
  );

  if (!texture || !texture.complete) {
    // Если текстура не загружена, пытаемся загрузить асинхронно
    lakeTextureManager.getLakePatternTexture(
      tileData.decorationBorder,
      tileData.imgDirection
    ).catch(error => {
      console.warn('Failed to load lake pattern texture:', error);
    });
    return;
  }

  // Отрисовываем текстуру с использованием общи�� функций
  drawTextureWithSettings(
    ctx,
    texture,
    centerX,
    centerY,
    halfTileW,
    halfTileH,
    'LAKE'
  );
};

/**
 * Отрисовывает паттерновую текстуру реки на основе данных тайла
 */
export const drawRiverPatternTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell
): void => {
  // Проверяем, что это река с паттерновыми данными
  if (
    tileData.decoration !== WorldMapDecorations.RIVER ||
    !tileData.decorationBorder ||
    !tileData.imgDirection
  ) {
    return;
  }

  // Получаем загруженную текстуру реки
  const texture = riverTextureManager.getLoadedRiverTexture(
    tileData.decorationBorder,
    tileData.imgDirection
  );

  if (!texture || !texture.complete) {
    // Если текстура не загружена, пытаемся загрузить асинхронно
    riverTextureManager.getRiverPatternTexture(
      tileData.decorationBorder,
      tileData.imgDirection
    ).catch(error => {
      console.warn('Failed to load river pattern texture:', error);
    });
    return;
  }

  // Отрисовываем текстуру с использо��анием общих функций
  drawTextureWithSettings(
    ctx,
    texture,
    centerX,
    centerY,
    halfTileW,
    halfTileH,
    'RIVER'
  );
};

/**
 * Отрисовывает паттерновую текстуру дороги на основе данных тайла
 */
export const drawRoadPatternTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell
): void => {
  // Проверяем, что это дорога с паттерновыми данными
  if (
    tileData.decoration !== WorldMapDecorations.ROAD ||
    !tileData.decorationBorder ||
    !tileData.imgDirection
  ) {
    return;
  }

  // Получаем загруженную текстуру дороги
  const texture = roadsTextureManager.getLoadedRoadTexture(
    tileData.decorationBorder,
    tileData.imgDirection
  );

  if (!texture || !texture.complete) {
    // Если текстура не загружена, пытаемся загрузить асинхронно
    roadsTextureManager.getRoadPatternTexture(
      tileData.decorationBorder,
      tileData.imgDirection
    ).catch(error => {
      console.warn('Failed to load road pattern texture:', error);
    });
    return;
  }

  // Отрисовываем текстуру с использованием общих функций
  drawTextureWithSettings(
    ctx,
    texture,
    centerX,
    centerY,
    halfTileW,
    halfTileH,
    'ROAD'
  );
};

/**
 * Отрисовывает углы озера поверх основной текстуры
 */
export const drawLakeCorners = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell
): void => {
  // Проверяем, что это озеро с данными углов
  if (
    tileData.decoration !== WorldMapDecorations.LAKE ||
    !tileData.decorationBorder ||
    !tileData.imgDirection
  ) {
    return;
  }

  // Находим углы в массиве (значения 5-8)
  const corners = tileData.decorationBorder.filter(border => border >= 5 && border <= 8);
  
  if (corners.length === 0) {
    return;
  }

  // Отрисовываем каждый угол
  for (const cornerType of corners) {
    const cornerTexture = lakeTextureManager.getLoadedCornerTexture(cornerType, tileData.imgDirection);
    
    if (!cornerTexture || !cornerTexture.complete) {
      // Если текстура угла не загружена, пытаемся загрузить асинхронно
      lakeTextureManager.getLakeCornerTexture(cornerType, tileData.imgDirection).catch(error => {
        console.warn(error);
      });
      continue;
    }

    // Отрисовываем текстуру угла с использованием общих функций
    drawTextureWithSettings(
      ctx,
      cornerTexture,
      centerX,
      centerY,
      halfTileW,
      halfTileH,
      'LAKE'
    );
  }
};

/**
 * Универсальная функция для отрисовки паттерновых текстур
 * Может быть расширена для других типов декораций с паттернами
 */
export const drawPatternTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell
): void => {
  switch (tileData.decoration) {
    case WorldMapDecorations.LAKE:
      // Сначала рисуем основную текстуру озера
      drawLakePatternTexture(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
      // Затем рисуем углы поверх
      drawLakeCorners(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
      break;
    case WorldMapDecorations.RIVER:
      // Для реки рисуем только основную тек��туру (без углов и наложений)
      drawRiverPatternTexture(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
      break;
    case WorldMapDecorations.ROAD:
      // Для дорог рисуем основную текстуру с нормализацией направлений
      drawRoadPatternTexture(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
      break;
    // Здесь можно добавить другие типы паттерновых декораций
    default:
      // Для неподдерживаемых типов ничего не делаем
      break;
  }
};

/**
 * Проверяет, поддерживает ли декорация паттерновые текстуры
 */
export const supportsPatternTextures = (tileData: WorldMapCell): boolean => {
  // Проверяем базовые требования
  if (!Array.isArray(tileData.decorationBorder) ||
      typeof tileData.imgDirection !== 'number' ||
      tileData.imgDirection < 1 ||
      tileData.imgDirection > 4) {
    return false;
  }

  // Для озер и рек требуется непустой массив
  if (tileData.decoration === WorldMapDecorations.LAKE || 
      tileData.decoration === WorldMapDecorations.RIVER) {
    return tileData.decorationBorder.length > 0;
  }

  // Для дорог массив может быть пустым (перекресток)
  if (tileData.decoration === WorldMapDecorations.ROAD) {
    return true; // массив может быть пустым или содержать любые направления
  }

  return false;
};

/**
 * Получает информацию о паттерне для отладки
 */
export const getPatternInfo = (tileData: WorldMapCell): string => {
  if (!supportsPatternTextures(tileData)) {
    return 'No pattern support';
  }

  const borderKey = tileData.decorationBorder!.sort((a, b) => a - b).join('_');
  return `Pattern: ${borderKey}, Direction: ${tileData.imgDirection}`;
};