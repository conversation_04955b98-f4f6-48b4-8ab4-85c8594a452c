import React from 'react';
import styles from '../game/GamePage.module.css';

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: 'success' | 'error';
}

export const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'success'
}) => {
  if (!isOpen) return null;

  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.dialogHeader}>
          <h3 style={{ color: type === 'error' ? '#ff4444' : '#44ff44' }}>
            {title}
          </h3>
        </div>
        
        <div className={styles.dialogBody}>
          <p>{message}</p>
        </div>
        
        <div className={styles.dialogActions}>
          <button
            className={styles.confirmButton}
            onClick={onClose}
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};