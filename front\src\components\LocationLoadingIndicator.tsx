/**
 * Индикатор загрузки локации
 */
import React from 'react';
import './LocationLoadingIndicator.module.css';
import { useGameStore } from '@/game/store/gameStore';

const LocationLoadingIndicator: React.FC = () => {
  const locationLoading = useGameStore((state) => state.locationLoading);

  if (!locationLoading) {
    return null;
  }

  return (
    <div className="location-loading-overlay">
      <div className="location-loading-container">
        <div className="location-loading-spinner"></div>
        <div className="location-loading-text">
          Загрузка локации...
        </div>
      </div>
    </div>
  );
};

export default LocationLoadingIndicator;
