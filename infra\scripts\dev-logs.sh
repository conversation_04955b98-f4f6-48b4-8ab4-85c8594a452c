#!/bin/bash

# NuclearStory - Development Logs Script
echo "📊 NuclearStory Development Logs"
echo "================================"

if [ -z "$1" ]; then
    echo "📋 Available services:"
    echo "   - all (show all logs)"
    echo "   - frontend"
    echo "   - auth-service"
    echo "   - game-engine-service"
    echo "   - world-generator-service"
    echo "   - save-service"
    echo "   - ai-service"
    echo "   - postgres-auth"
    echo "   - postgres-saves"
    echo "   - redis"
    echo ""
    echo "Usage: $0 [service-name]"
    echo "Example: $0 frontend"
    echo "Example: $0 all"
    exit 1
fi

if [ "$1" = "all" ]; then
    echo "📊 Showing logs for all services (Ctrl+C to exit)..."
    docker compose -f docker-compose.dev.yml logs -f
else
    echo "📊 Showing logs for $1 (Ctrl+C to exit)..."
    docker compose -f docker-compose.dev.yml logs -f "$1"
fi
