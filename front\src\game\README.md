# Структура игрового модуля

## Описание
2D изометрическая игра в стиле Fallout. Мир генерируется на бэкенде, фронт отвечает за рендеринг, логику игры и взаимодействие.

## Структура папок

### `/core` - Основная логика игры
- `GameEngine.ts` - Главный игровой движок
- `GameState.ts` - Управление состоянием игры
- `GameLoop.ts` - Игровой цикл
- `InputManager.ts` - Управление вводом

### `/rendering` - Система рендеринга
- `/isometric` - Изометрический рендер глобальной карты
- `/location` - Рендер локаций (комнат)
- `/animations` - Система анимаций
- `/effects` - Визуальные эффекты
- `/textures` - Управление текстурами

### `/entities` - Игровые сущности (клиентская логика)
- `/player` - Логика игрока
- `/characters` - НПС и монстры
- `/objects` - Объекты (контейнеры, стены и т.д.)

### `/systems` - Игровые системы
- `/movement` - Система движения
- `/interaction` - Система взаимодействий
- `/camera` - Система камеры
- `/fog-of-war` - Туман войны

### `/ui` - Пользовательский интерфейс
- `/hud` - HUD элементы
- `/menus` - Игровые меню
- `/dialogs` - Диалоговые окна
- `/panels` - Информационные панели
- `/controls` - Элементы управления

### `/world` - Работа с миром
- `/global-map` - Компоненты глобальной карты
- `/locations` - Компоненты локаций
- `/data` - Типы данных мира

### `/utils` - Утилиты
- `/math` - Математические функции
- `/coordinates` - Работа с координатами
- `/helpers` - Вспомогательные функции
- `/constants` - Константы

### `/types` - Типы TypeScript
- Определения типо для всех игровых сущностей