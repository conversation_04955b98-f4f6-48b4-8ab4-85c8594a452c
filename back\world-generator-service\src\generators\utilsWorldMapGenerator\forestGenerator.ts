import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { WorldMapDecorations, TerrainType } from 'src/shared';
import { CancellationToken, delay } from '../../utils/asyncUtils';

// Асинхронная генерация леса с разными паттернами
export async function generateForestAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  if (cancellationToken?.isCancelled) {
    throw new Error('Operation was cancelled');
  }

  const patterns = [
    () => generateCircularForestAsync(grid, center, worldSize, rng, cancellationToken),
    () => generateStripForestAsync(grid, center, worldSize, rng, cancellationToken),
    () => generateScatteredForestAsync(grid, center, worldSize, rng, cancellationToken)
  ];
  
  // Выбираем случайный паттерн
  const patternIndex = Math.floor(rng() * patterns.length);
  const selectedPattern = patterns[patternIndex];
  
  // Проверяем, что selectedPattern является функцией
  if (typeof selectedPattern !== 'function') {
    throw new Error(`selectedPattern is not a function. Type: ${typeof selectedPattern}, Index: ${patternIndex}`);
  }
  
  await selectedPattern();
  
  // Добавляем кусты вокруг леса (ореол)
  await generateForestHaloAsync(grid, center, worldSize, rng, cancellationToken);
}

// Алиас для обратной совместимости
export const generateForest = generateForestAsync;

// Асинхронная генерация ореола кустов вокруг леса с шансом 30%
async function generateForestHaloAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.FOREST;
  let processedCells = 0;
  
  // Ограничиваем область поиска более строго
  const minX = Math.max(0, center.x - config.MAX_RADIUS - 2);
  const maxX = Math.min(worldSize - 1, center.x + config.MAX_RADIUS + 2);
  const minY = Math.max(0, center.y - config.MAX_RADIUS - 2);
  const maxY = Math.min(worldSize - 1, center.y + config.MAX_RADIUS + 2);
  
  for (let x = minX; x <= maxX; x++) {
    for (let y = minY; y <= maxY; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      const cellKey = `${x},${y}`;
      
      // Дополнительные проверки безопасности
      if (!grid[cellKey]) {
        console.warn(`Ячейка ${cellKey} не существует при генерации кустов`);
        continue;
      }
      
      // Кусты появляются только вне леса, но рядом с ним, с шансом 30%
      // И только если ячейка пустая
      if (distance > config.MAX_RADIUS && 
          distance <= config.MAX_RADIUS + 2 && 
          grid[cellKey].decoration === WorldMapDecorations.NONE &&
          rng() * 100 < 30) {
        grid[cellKey].decoration = WorldMapDecorations.BUSHES;
        grid[cellKey].terrain = TerrainType.WASTELAND;
      }
      
      processedCells++;
      // Освобождаем event loop каждые 100 ячеек 
      if (processedCells % 100 === 0) {
        await delay(0);
      }
    }
  }
}

// Асинхронный паттерн 1: Круглый лес (классический)
async function generateCircularForestAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.FOREST;
  let processedCells = 0;
  
  for (let radius = 0; radius <= config.MAX_RADIUS; radius++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }
    
    const chance = config.CHANCES[radius];
    if (!chance) continue;
    
    for (let x = center.x - radius; x <= center.x + radius; x++) {
      for (let y = center.y - radius; y <= center.y + radius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
        if (distance <= radius && rng() * 100 < chance) {
          const cellKey = `${x},${y}`;
          if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
            grid[cellKey].decoration = WorldMapDecorations.FOREST;
            grid[cellKey].terrain = TerrainType.WASTELAND;
          }
        }
        
        processedCells++;
        // Освобождаем event loop каждые 100 ячеек
        if (processedCells % 1000 === 0) {
          await delay(0);
        }
      }
    }
  }
}

// Асинхронный паттерн 2: Полосовой лес (вытянутый)
async function generateStripForestAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.FOREST;
  const directions = [
    { x: 1, y: 0 },   // Горизонтальная полоса
    { x: 0, y: 1 },   // Вертикальная полоса
    { x: 1, y: 1 },   // Диагональная полоса
    { x: 1, y: -1 },  // Обратная диагональная полоса
  ];
  
  const direction = directions[Math.floor(rng() * directions.length)];
  const stripLength = config.MAX_RADIUS * 2;
  const stripWidth = 2;
  let processedCells = 0;
  
  for (let i = -stripLength; i <= stripLength; i++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }
    
    for (let w = -stripWidth; w <= stripWidth; w++) {
      let x, y;
      
      if (direction.x === 1 && direction.y === 0) {
        x = center.x + i;
        y = center.y + w;
      } else if (direction.x === 0 && direction.y === 1) {
        // Вертикальная полоса
        x = center.x + w;
        y = center.y + i;
      } else {
        // Диагональные полосы
        x = center.x + i;
        y = center.y + i * direction.y + w;
      }
      
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
      
      const distance = Math.abs(i) / stripLength;
      const chance = config.CHANCES[0] * (1 - distance * 0.5); // Уменьшаем шанс к краям
      
      if (rng() * 100 < chance) {
        const cellKey = `${x},${y}`;
        if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
          grid[cellKey].decoration = WorldMapDecorations.FOREST;
          grid[cellKey].terrain = TerrainType.WASTELAND;
        }
      }
      
      processedCells++;
      // Освобождаем event loop каждые 50 ячеек
      if (processedCells % 500 === 0) {
        await delay(0);
      }
    }
  }
}

// Асинхронный паттерн 3: Разбросанный лес (группы деревьев)
async function generateScatteredForestAsync(
  grid: Record<string, WorldMapCell>, 
  center: Position, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  const config = DECORATION_CONFIG.FOREST;
  const clusterCount = 3 + Math.floor(rng() * 3); // 3-5 кластеров
  const maxRadius = config.MAX_RADIUS + 1;
  
  for (let cluster = 0; cluster < clusterCount; cluster++) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }
    
    // Случайное смещение для каждого кластера
    const offsetX = Math.floor((rng() - 0.5) * maxRadius * 2);
    const offsetY = Math.floor((rng() - 0.5) * maxRadius * 2);
    const clusterCenter = {
      x: center.x + offsetX,
      y: center.y + offsetY
    };
    
    const clusterRadius = 1 + Math.floor(rng() * 2); // Радиус кластера 1-2
    let processedCells = 0;
    
    for (let x = clusterCenter.x - clusterRadius; x <= clusterCenter.x + clusterRadius; x++) {
      for (let y = clusterCenter.y - clusterRadius; y <= clusterCenter.y + clusterRadius; y++) {
        if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) continue;
        
        const distance = Math.sqrt((x - clusterCenter.x) ** 2 + (y - clusterCenter.y) ** 2);
        if (distance <= clusterRadius) {
          const chance = config.CHANCES[0] * 0.7; // Немного меньший шанс для разбросанного леса
          
          if (rng() * 100 < chance) {
            const cellKey = `${x},${y}`;
            if (grid[cellKey] && grid[cellKey].decoration === WorldMapDecorations.NONE) {
              grid[cellKey].decoration = WorldMapDecorations.FOREST;
              grid[cellKey].terrain = TerrainType.WASTELAND;
            }
          }
        }
        
        processedCells++;
        // Освобождаем event loop каждые 25 ячеек (кластеры меньше)
        if (processedCells % 250 === 0) {
          await delay(0);
        }
      }
    }
    
    // Небольшая задержка между кластерами
    await delay(0);
  }
}