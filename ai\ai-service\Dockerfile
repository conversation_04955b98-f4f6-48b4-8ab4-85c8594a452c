# Development Dockerfile with hot reload
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create non-root user (but don't switch yet for development)
RUN addgroup --gid 1001 --system python && \
    adduser --uid 1001 --system --group python

# Expose port
EXPOSE 3005

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3005/health || exit 1

# Start with hot reload for development
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3005", "--reload"]
