#!/bin/bash

# NuclearStory - Development Rebuild Script
echo "🔨 Rebuilding NuclearStory development environment..."

if [ -z "$1" ]; then
    echo "🔧 Rebuilding all services..."
    docker compose -f docker-compose.dev.yml build --no-cache
    docker compose -f docker-compose.dev.yml up -d
else
    echo "🔧 Rebuilding $1..."
    docker compose -f docker-compose.dev.yml build --no-cache "$1"
    docker compose -f docker-compose.dev.yml up -d "$1"
fi

echo "✅ Rebuild completed!"
echo "📊 Check status: ./scripts/status.sh"
