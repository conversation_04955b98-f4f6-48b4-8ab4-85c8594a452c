{"name": "world-generator-service", "version": "0.0.1", "description": "World generation microservice for NuclearStory", "author": "NuclearStory Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "decode:png": "node ./src/utils/pngDecoder/decodePngToTs.js", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/axios": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.20", "@nestjs/swagger": "^7.1.16", "@nestjs/websockets": "^10.4.20", "axios": "^1.6.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "openai": "^4.20.1", "pngjs": "^7.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@types/node": "^20.3.1", "@types/socket.io": "^3.0.2", "typescript": "^5.1.3"}}