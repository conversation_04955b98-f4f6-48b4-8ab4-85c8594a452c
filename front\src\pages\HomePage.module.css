.homePage {
  padding: 4rem 1rem;
}

.container {
  max-width: 1024px;
  margin: 0 auto;
  text-align: center;
}

.title {
  font-size: 3.75rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: var(--nuclear-green);
}

.subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.featureCard {
  background-color: var(--dark-card);
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--dark-border);
  transition: transform 0.3s ease, border-color 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-2px);
  border-color: var(--nuclear-green);
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--nuclear-green);
}

.featureDescription {
  color: var(--text-muted);
}

.buttonGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

.button {
  display: inline-block;
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transform: scale(1);
}

.button:hover {
  transform: scale(1.05);
}

.primaryButton {
  background: linear-gradient(135deg, var(--nuclear-green), #00cc33);
  color: var(--dark-bg);
}

.primaryButton:hover {
  background: linear-gradient(135deg, #00cc33, var(--nuclear-green));
  box-shadow: 0 4px 15px rgba(0, 255, 65, 0.3);
}

.secondaryButton {
  background: linear-gradient(135deg, var(--nuclear-orange), #ff8800);
  color: white;
}

.secondaryButton:hover {
  background: linear-gradient(135deg, #ff8800, var(--nuclear-orange));
  box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
}

.tertiaryButton {
  background: linear-gradient(135deg, #4b5563, #374151);
  color: white;
}

.tertiaryButton:hover {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  box-shadow: 0 4px 15px rgba(75, 85, 99, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .homePage {
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .buttonGroup {
    flex-direction: column;
    align-items: stretch;
  }
  
  .button {
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }
  
  .featureCard {
    padding: 1rem;
  }
  
  .featureTitle {
    font-size: 1.125rem;
  }
}
