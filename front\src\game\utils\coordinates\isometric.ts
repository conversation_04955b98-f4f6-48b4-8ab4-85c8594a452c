/**
 * Утилиты для работы с изометрическими координатами
 */

/**
 * Функция преобразования изометрических координат в экранные
 */
export const isoToScreen = (isoX: number, isoY: number, tileWidth: number, tileHeight: number) => {
  const screenX = (isoX - isoY) * (tileWidth / 2)
  const screenY = (isoX + isoY) * (tileHeight / 2)
  return { x: screenX, y: screenY }
}

/**
 * Функция преобразования экранных координат в изометрические
 */
export const screenToIso = (screenX: number, screenY: number, tileWidth: number, tileHeight: number) => {
  const isoX = (screenX / (tileWidth / 2) + screenY / (tileHeight / 2)) / 2
  const isoY = (screenY / (tileHeight / 2) - screenX / (tileWidth / 2)) / 2
  return { x: Math.floor(isoX), y: Math.floor(isoY) }
}

/**
 * Проверяет, находится ли тайл в видимой области
 */
export const isTileVisible = (
  centerX: number, 
  centerY: number, 
  tileWidth: number, 
  tileHeight: number, 
  canvasWidth: number, 
  canvasHeight: number
) => {
  return !(
    centerX < -tileWidth || 
    centerX > canvasWidth + tileWidth ||
    centerY < -tileHeight || 
    centerY > canvasHeight + tileHeight
  )
}

/**
 * Вычисляет центральные координаты тайла на экране
 */
export const getTileCenterOnScreen = (
  screenX: number,
  screenY: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number
) => {
  const centerX = screenX + canvasWidth / 2 - cameraX
  const centerY = screenY + canvasHeight / 2 - cameraY
  return { centerX, centerY }
}

/**
 * Проверяет, находится ли точка внутри ромбовидного тайла
 */
export const isPointInDiamond = (
  pointX: number,
  pointY: number,
  centerX: number,
  centerY: number,
  halfWidth: number,
  halfHeight: number
): boolean => {
  // Нормализуем координаты относительно центра ромба
  const dx = Math.abs(pointX - centerX)
  const dy = Math.abs(pointY - centerY)

  // Проверяем, находится ли точка внутри ромба
  // Формула для ромба: |x|/halfWidth + |y|/halfHeight <= 1
  return (dx / halfWidth + dy / halfHeight) <= 1
}

/**
 * Преобразует координаты камеры в понятные пользователю координаты
 * Возвращает координаты относительно центра карты (0,0 в центре)
 */
export const getCameraDisplayCoordinates = (
  cameraX: number,
  cameraY: number,
  mapSize: number,
  tileWidth: number,
  tileHeight: number
): { x: number; y: number } => {
  // Вычисляем центр карты
  const centerTile = (mapSize - 1) / 2
  const centerScreen = isoToScreen(centerTile, centerTile, tileWidth, tileHeight)

  // Вычисляем смещение от центра
  const offsetX = cameraX - centerScreen.x
  const offsetY = cameraY - centerScreen.y

  // Возвращаем округленные координаты
  return {
    x: Math.round(offsetX),
    y: Math.round(offsetY)
  }
}