/**
 * Модальное окно ожидания времени (как в Skyrim)
 */

import React, { useState } from 'react';
import styles from './TimeWaitModal.module.css';
import { useGameTimeControl } from '@/game/utils/time/useGameTime';
import { addMinutes, formatGameTime } from '@/game/utils/time/gameTime';

interface TimeWaitModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TimeWaitModal: React.FC<TimeWaitModalProps> = ({ isOpen, onClose }) => {
  const [hoursToWait, setHoursToWait] = useState(1);
  const [isWaiting, setIsWaiting] = useState(false);
  const { getCurrentTime, fastForward } = useGameTimeControl();
  
  if (!isOpen) return null;
  
  const currentTime = getCurrentTime();
  const targetTime = addMinutes(currentTime, hoursToWait * 60);
  
  const handleWait = () => {
    setIsWaiting(true);
    
    // Эффект ожидания с анимацией
    setTimeout(() => {
      fastForward(hoursToWait * 60);
      setIsWaiting(false);
      onClose();
    }, 1000); // 1 секунда анимации
  };
  
  const handleCancel = () => {
    onClose();
  };
  
  return (
    <div className={styles.overlay} onClick={handleCancel}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <h2 className={styles.title}>⏰ Ожидание</h2>
          <button className={styles.closeButton} onClick={handleCancel}>
            ✕
          </button>
        </div>
        
        <div className={styles.content}>
          <div className={styles.timeInfo}>
            <div className={styles.currentTime}>
              <span className={styles.label}>Текущее время:</span>
              <span className={styles.time}>{formatGameTime(currentTime)}</span>
            </div>
            
            <div className={styles.targetTime}>
              <span className={styles.label}>Время после ожидания:</span>
              <span className={styles.time}>{formatGameTime(targetTime)}</span>
            </div>
          </div>
          
          <div className={styles.controls}>
            <div className={styles.sliderContainer}>
              <label className={styles.sliderLabel}>
                Ждать: {hoursToWait} {hoursToWait === 1 ? 'час' : hoursToWait < 5 ? 'часа' : 'часов'}
              </label>
              <input
                type="range"
                min="1"
                max="24"
                value={hoursToWait}
                onChange={(e) => setHoursToWait(parseInt(e.target.value))}
                className={styles.slider}
                disabled={isWaiting}
              />
              <div className={styles.sliderMarks}>
                <span>1ч</span>
                <span>6ч</span>
                <span>12ч</span>
                <span>18ч</span>
                <span>24ч</span>
              </div>
            </div>
            
            <div className={styles.quickButtons}>
              <button 
                onClick={() => setHoursToWait(1)} 
                className={`${styles.quickButton} ${hoursToWait === 1 ? styles.active : ''}`}
                disabled={isWaiting}
              >
                1 час
              </button>
              <button 
                onClick={() => setHoursToWait(3)} 
                className={`${styles.quickButton} ${hoursToWait === 3 ? styles.active : ''}`}
                disabled={isWaiting}
              >
                3 часа
              </button>
              <button 
                onClick={() => setHoursToWait(6)} 
                className={`${styles.quickButton} ${hoursToWait === 6 ? styles.active : ''}`}
                disabled={isWaiting}
              >
                6 часов
              </button>
              <button 
                onClick={() => setHoursToWait(12)} 
                className={`${styles.quickButton} ${hoursToWait === 12 ? styles.active : ''}`}
                disabled={isWaiting}
              >
                12 часов
              </button>
            </div>
          </div>
        </div>
        
        <div className={styles.footer}>
          <button 
            className={styles.waitButton} 
            onClick={handleWait}
            disabled={isWaiting}
          >
            {isWaiting ? '⏳ Ожидание...' : '✓ Ждать'}
          </button>
          <button 
            className={styles.cancelButton} 
            onClick={handleCancel}
            disabled={isWaiting}
          >
            ✕ Отмена
          </button>
        </div>
        
        {isWaiting && (
          <div className={styles.waitingOverlay}>
            <div className={styles.waitingSpinner}></div>
            <p className={styles.waitingText}>Время идёт...</p>
          </div>
        )}
      </div>
    </div>
  );
};
