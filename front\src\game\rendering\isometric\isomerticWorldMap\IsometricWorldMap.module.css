.mapContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  overflow: hidden;
  z-index: 1;
}

.mapCanvas {
  cursor: default;
  background: var(--bg-primary);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  user-select: none;
  -webkit-user-drag: none;
  transform-origin: center center;
  transition: transform 0.1s ease-out;
}

.instructions {
  color: var(--text-muted);
  font-size: 11px;
  max-width: 200px;
}
