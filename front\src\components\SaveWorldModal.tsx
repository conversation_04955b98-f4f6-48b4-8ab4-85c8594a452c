import React, { useState } from 'react';
import styles from '../game/GamePage.module.css';

interface SaveWorldModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, isNewSave: boolean) => void;
  currentWorldName?: string;
}

export const SaveWorldModal: React.FC<SaveWorldModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentWorldName = ''
}) => {
  const [worldName, setWorldName] = useState(currentWorldName);
  const [saveType, setSaveType] = useState<'overwrite' | 'new'>('overwrite');

  if (!isOpen) return null;

  const handleSave = () => {
    if (saveType === 'new' && !worldName.trim()) {
      alert('Введите название для нового мира');
      return;
    }
    onSave(worldName.trim() || currentWorldName, saveType === 'new');
    onClose();
  };

  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.dialogHeader}>
          <h3>Сохранить мир</h3>
        </div>
        
        <div className={styles.dialogBody}>
          <div style={{ marginBottom: '20px' }}>
            <label>
              <input
                type="radio"
                name="saveType"
                value="overwrite"
                checked={saveType === 'overwrite'}
                onChange={() => setSaveType('overwrite')}
                style={{ marginRight: '8px' }}
              />
              Перезаписать текущий мир
            </label>
          </div>
          
          <div style={{ marginBottom: '20px' }}>
            <label>
              <input
                type="radio"
                name="saveType"
                value="new"
                checked={saveType === 'new'}
                onChange={() => setSaveType('new')}
                style={{ marginRight: '8px' }}
              />
              Сохранить как новый мир
            </label>
          </div>
          
          {saveType === 'new' && (
            <div style={{ marginTop: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px' }}>
                Название нового мира:
              </label>
              <input
                type="text"
                value={worldName}
                onChange={(e) => setWorldName(e.target.value)}
                placeholder="Введите название мира"
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
                autoFocus
              />
            </div>
          )}
        </div>
        
        <div className={styles.dialogActions}>
          <button
            className={styles.confirmButton}
            onClick={handleSave}
          >
            Сохранить
          </button>
          <button
            className={styles.cancelButton}
            onClick={onClose}
          >
            Отмена
          </button>
        </div>
      </div>
    </div>
  );
};