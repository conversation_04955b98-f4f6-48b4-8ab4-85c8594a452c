/**
 * Обработчики событий для изометрической карты
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { isoToScreen, getTileCenterOnScreen, isPointInDiamond } from '../../utils/coordinates/isometric';
import { MIN_ZOOM, MAX_ZOOM, ZOOM_STEP, TILE_GAP } from '../../utils/constants/rendering';
import { CAMERA_MOVE_SPEED } from '../../utils/constants/movement';
import { calculateShortestPath, clearPath } from '../movement/playerMovement';
import { movePlayerByPath } from '../movement/playerMovement';
import { Position } from '../../../shared/types/Common';
import { generateInspectMessage } from '../../ui/panels/InfoTerminal/messages';
import { KEY_BINDS, isKeyPressed, isMouseButtonPressed, isMouseButtonHeld } from '../../utils/constants/KeyBinds';

/**
 * Создает обработчик нажатия Enter для запуска движения игрока по пути
 * и пробела для входа в локацию
 */
export function createPlayerPathKeyDownHandler(
  playerPosition: Position,
  path: Position[],
  speed?: number,
  currentWorld?: WorldMap | null
) {
  return function handleKeyDown(e: KeyboardEvent) {
    // Получаем актуальный путь из системы движения
    if (isKeyPressed(e, KEY_BINDS.MOVE_PLAYER)) {
      import('../movement/playerMovement').then(({ getCurrentPath }) => {
        const currentPath = getCurrentPath();
        if (currentPath.length > 0) {
          // Проверяем, находится ли игрок в локации
          import('../../store/gameStore').then(({ useGameStore }) => {
            const { playerLocationPresent } = useGameStore.getState();
            
            if (playerLocationPresent) {
              // Игрок в локации - используем движение для локаций
              import('../location/locationMovement').then(({ movePlayerByPathInLocation }) => {
                // Получаем актуальную позицию игрока в локации
                const { currentLocation } = useGameStore.getState();
                const locationPlayerPosition = currentLocation?.playerPosition || playerPosition;
                movePlayerByPathInLocation(locationPlayerPosition, currentPath, speed);
              });
            } else {
              // Игрок в мире - используем обычное движение
              movePlayerByPath(playerPosition, currentPath, speed);
            }
          });
        }
      });
    }
    
    // Логика локаций перенесена в eventHandlersLocation.ts
  };
}

/**
 * Находит позицию спавна в локации (клетка с spawnZone: true)
 */
function findLocationSpawnPosition(location: any): Position | null {
  if (!location?.locationMap) return null;
  
  for (const [key, cell] of Object.entries(location.locationMap)) {
    if ((cell as any).spawnZone) {
      return (cell as any).pos;
    }
  }
  
  // Если нет специальной зоны спавна, возвращаем центр локации
  const locationSize = location.locationSize || { x: 10, y: 10 };
  return {
    x: Math.floor(locationSize.x / 2),
    y: Math.floor(locationSize.y / 2)
  };
}

export interface CameraRef {
  x: number;
  y: number;
}

/**
 * Создает обработчик движения мыши для перемещения камеры
 */
export const createMouseMoveHandler = (
  cameraRef: React.RefObject<CameraRef>,
  canvasRef: React.RefObject<HTMLCanvasElement>
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!cameraRef.current) return;

    // Режим перетаскивания мышью
    if (KEY_BINDS.CAMERA_CONTROL_MODE === 'drag') {
      if (isMouseButtonHeld(e, KEY_BINDS.CAMERA_DRAG_BUTTON)) {
        cameraRef.current.x -= e.movementX;
        cameraRef.current.y -= e.movementY;
      }
    }
    // Режим следования за курсором
    else if (KEY_BINDS.CAMERA_CONTROL_MODE === 'cursor') {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      const canvasWidth = rect.width;
      const canvasHeight = rect.height;
      const edgeSize = KEY_BINDS.CAMERA_CURSOR_EDGE_SIZE;
      const speed = KEY_BINDS.CAMERA_CURSOR_SPEED;

      // Проверяем, находится ли курсор у края экрана
      let deltaX = 0;
      let deltaY = 0;

      // Левый край
      if (mouseX < edgeSize) {
        deltaX = -(edgeSize - mouseX) * speed / edgeSize;
      }
      // Правый край
      else if (mouseX > canvasWidth - edgeSize) {
        deltaX = (mouseX - (canvasWidth - edgeSize)) * speed / edgeSize;
      }

      // Верхний край
      if (mouseY < edgeSize) {
        deltaY = -(edgeSize - mouseY) * speed / edgeSize;
      }
      // Нижний край
      else if (mouseY > canvasHeight - edgeSize) {
        deltaY = (mouseY - (canvasHeight - edgeSize)) * speed / edgeSize;
      }

      // Применяем движение камеры
      if (deltaX !== 0 || deltaY !== 0) {
        cameraRef.current.x += deltaX;
        cameraRef.current.y += deltaY;
      }
    }
  };
};

/**
 * Создает обработчик изменения зума
 */
export const createZoomChangeHandler = (setZoom: (zoom: number) => void) => {
  return (newZoom: number) => {
    setZoom(Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, newZoom)));
  };
};

/**
 * Создает обработчик колеса мыши для зума с сохранением позиции
 * @deprecated Используйте ZoomManager вместо этого
 */
export const createWheelHandler = (
  zoom: number,
  handleZoomChange: (newZoom: number) => void,
  cameraRef: React.RefObject<CameraRef>,
  canvasRef: React.RefObject<HTMLCanvasElement>
) => {
  return (e: WheelEvent) => {
    e.preventDefault();

    if (!cameraRef.current || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // Вычисляем точку зума относительно мыши
    const canvasWidth = canvasRef.current.width;
    const canvasHeight = canvasRef.current.height;

    // Позиция мыши относительно центра канваса
    const mouseCenterX = mouseX - canvasWidth / 2;
    const mouseCenterY = mouseY - canvasHeight / 2;

    // Сохраняем старую позицию камеры
    const oldCameraX = cameraRef.current.x;
    const oldCameraY = cameraRef.current.y;

    const zoomDelta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
    const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoom + zoomDelta));

    if (newZoom !== zoom) {
      // Вычисляем коэффициент изменения зума
      const zoomRatio = newZoom / zoom;

      // Корректируем позицию камеры для зума в точку мыши
      cameraRef.current.x = oldCameraX + mouseCenterX * (1 - zoomRatio);
      cameraRef.current.y = oldCameraY + mouseCenterY * (1 - zoomRatio);

      // Простая коррекция Y: при увеличении зума камера вверх, при уменьшении вниз
      const CORRECTION_Y = 100; // подберите значение под свой канвас
      if (newZoom > zoom) {
        cameraRef.current.y -= CORRECTION_Y;
      } else if (newZoom < zoom) {
        cameraRef.current.y += CORRECTION_Y;
      }

      handleZoomChange(newZoom);
    }
  };
};

/**
 * Создает обработчик клика для получения координат
 */
export const createClickHandler = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  setCellTarget: (cell: { isoX: number; isoY: number; tileData: any } | null) => void,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  zoom: number = 1.0
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    // Получаем координаты клика и учитываем зум canvas scale
    const rawClickX = e.clientX - rect.left;
    const rawClickY = e.clientY - rect.top;

    // Деmasштабируем координаты клика чтобы они соответствовали базовым размерам тайлов
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const clickX = centerX + (rawClickX - centerX) / zoom;
    const clickY = centerY + (rawClickY - centerY) / zoom;

    const mapSize = currentWorld?.settings?.worldSize || 20;
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        // Получаем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const tileKey = `${isoX},${isoY}`;
          const tileData = currentWorld?.worldMap?.[tileKey];
          foundTile = { isoX, isoY, tileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Выводим в консоль информацию о тайле (объекте) на WorldMap
      console.log('[WorldMap] Tile info:', {
        isoX: foundTile.isoX,
        isoY: foundTile.isoY,
        tileData: foundTile.tileData
         
      });
       console.log('location', 
        
        foundTile.tileData.location?.type,
        
        foundTile.tileData.location?.subtype,
        
        foundTile.tileData.location?.textureMaterial


        
      );      // Проверяем, кликнули ли мы на уже выбранный тайл
      if (cellTarget && cellTarget.isoX === foundTile.isoX && cellTarget.isoY === foundTile.isoY) {
        // Отменяем выбор
        setCellTarget(null);
        clearPath();
      } else {
        // Выбираем новый тайл
        setCellTarget(foundTile);
        // Вычисляем путь от текущей позиции игрока до выбранной точки
        const playerPosition = currentWorld?.player?.position || { x: Math.floor(mapSize / 2), y: Math.floor(mapSize / 2) };
        const targetPosition = { x: foundTile.isoX, y: foundTile.isoY };
        calculateShortestPath(mapSize, playerPosition, targetPosition, currentWorld?.worldMap);
      }
    }
  };
};

/**
 * Создает обработчик клавиш для управления камерой
 */
export const createKeyDownHandler = (cameraRef: React.RefObject<CameraRef>) => {
  return (e: KeyboardEvent) => {
    if (!cameraRef.current) return;

    if (isKeyPressed(e, KEY_BINDS.CAMERA_UP)) {
      cameraRef.current.y -= CAMERA_MOVE_SPEED;
    } else if (isKeyPressed(e, KEY_BINDS.CAMERA_DOWN)) {
      cameraRef.current.y += CAMERA_MOVE_SPEED;
    } else if (isKeyPressed(e, KEY_BINDS.CAMERA_LEFT)) {
      cameraRef.current.x -= CAMERA_MOVE_SPEED;
    } else if (isKeyPressed(e, KEY_BINDS.CAMERA_RIGHT)) {
      cameraRef.current.x += CAMERA_MOVE_SPEED;
    } else {
      return;
    }
    e.preventDefault();
  };
};

/**
 * Создает обработчик правого клика для контекстного меню
 */
export const createRightClickHandler = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  zoom: number = 1.0,
  onContextMenu: (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => void
) => {
  return (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Отключаем стандартное контекстное меню
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    // Получаем координаты клика и учитваем зум canvas scale
    const rawClickX = e.clientX - rect.left;
    const rawClickY = e.clientY - rect.top;

    // Деmasштабируем координаты клика чтобы они соответствовали базовым размерам тайлов
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const clickX = centerX + (rawClickX - centerX) / zoom;
    const clickY = centerY + (rawClickY - centerY) / zoom;

    const mapSize = currentWorld?.settings?.worldSize || 20;
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        // Получаем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const tileKey = `${isoX},${isoY}`;
          const tileData = currentWorld?.worldMap?.[tileKey];
          foundTile = { isoX, isoY, tileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Показываем контекстное меню в позиции клика
      onContextMenu(e.clientX, e.clientY, foundTile);
    }
  };
};

/**
 * Создает обработчики для правого клика с отслеживанием времени зажатия
 */
export const createRightClickHandlers = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  zoom: number = 1.0,
  onContextMenu: (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => void
) => {
  let rightMouseDownTime: number | null = null;
  let rightMouseDownPosition: { x: number; y: number } | null = null;

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (isMouseButtonPressed(e, KEY_BINDS.CONTEXT_MENU_BUTTON)) {
      rightMouseDownTime = Date.now();
      rightMouseDownPosition = { x: e.clientX, y: e.clientY };
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (isMouseButtonPressed(e, KEY_BINDS.CONTEXT_MENU_BUTTON) && rightMouseDownTime && rightMouseDownPosition) {
      const holdDuration = Date.now() - rightMouseDownTime;
      
      // Проверяем, что кнопка была зажата меньше установленного времени
      if (holdDuration < KEY_BINDS.CONTEXT_MENU_DELAY) {
        // Проверяем, что мышь не сильно сдвинулась
        const deltaX = Math.abs(e.clientX - rightMouseDownPosition.x);
        const deltaY = Math.abs(e.clientY - rightMouseDownPosition.y);
        
        if (deltaX < KEY_BINDS.CONTEXT_MENU_MOVE_TOLERANCE && deltaY < KEY_BINDS.CONTEXT_MENU_MOVE_TOLERANCE) {
          // Это быстрый клик без перетаскивания - показываем контекстное меню
          showContextMenu(e);
        }
      }
      
      // Сбрасываем состояние
      rightMouseDownTime = null;
      rightMouseDownPosition = null;
    }
  };

  const showContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect || !cameraRef.current) return;

    // Получаем координаты клика и учитываем зум canvas scale
    const rawClickX = e.clientX - rect.left;
    const rawClickY = e.clientY - rect.top;

    // Деmasштабируем координаты клика чтобы они соответствовали базовым размерам тайлов
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const clickX = centerX + (rawClickX - centerX) / zoom;
    const clickY = centerY + (rawClickY - centerY) / zoom;

    const mapSize = currentWorld?.settings?.worldSize || 20;
    let foundTile: { isoX: number; isoY: number; tileData: any } | null = null;

    // Проверяем все тайлы, чтобы найти тот, в ромб которого попал клик
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        // Получаем экранные координаты тайла
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const { centerX, centerY } = getTileCenterOnScreen(
          screenX,
          screenY,
          canvasWidth,
          canvasHeight,
          cameraRef.current.x,
          cameraRef.current.y
        );

        const halfTileW = tileWidth / 2 - TILE_GAP;
        const halfTileH = tileHeight / 2 - TILE_GAP;

        // Проверяем, попал ли клик в ромб этого тайла
        if (isPointInDiamond(clickX, clickY, centerX, centerY, halfTileW, halfTileH)) {
          const tileKey = `${isoX},${isoY}`;
          const tileData = currentWorld?.worldMap?.[tileKey];
          foundTile = { isoX, isoY, tileData };
          break;
        }
      }
      if (foundTile) break;
    }

    if (foundTile) {
      // Показываем контекстное меню в позиции клика
      onContextMenu(e.clientX, e.clientY, foundTile);
    }
  };

  const handleContextMenu = (e: React.MouseEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Всегда отключаем стандартное контекстное меню
  };

  return {
    handleMouseDown,
    handleMouseUp,
    handleContextMenu
  };
};

/**
 * Создает обработчик для отключения контекстного меню
 */
export const createContextMenuHandler = () => {
  return (e: MouseEvent) => e.preventDefault();
};