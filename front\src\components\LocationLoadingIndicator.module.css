/* Стили для индикатора загрузки локации */

.location-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  pointer-events: all;
}

.location-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(20, 20, 20, 0.9);
  padding: 30px;
  border-radius: 10px;
  border: 2px solid #4CAF50;
  min-width: 200px;
}

.location-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: location-loading-spin 1s linear infinite;
  margin-bottom: 15px;
}

.location-loading-text {
  color: #4CAF50;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

@keyframes location-loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
