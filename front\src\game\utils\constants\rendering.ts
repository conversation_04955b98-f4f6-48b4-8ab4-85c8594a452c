
/**
 * Константы для рендеринга игры
 */

import { useGameStore } from '../../store/gameStore';
// Размеры экрана
export const IMCOMP_MAX_WIDTH = 1920
export const IMCOMP_MAX_HEIGHT = 1080

// Константы для изометрической проекции
export const BASE_TILE_WIDTH = 70
export const BASE_TILE_HEIGHT = 46

// Настройки рендеринга
export const TARGET_FPS = 60
export const FRAME_DURATION = 1000 / TARGET_FPS


// Настройки зума
export const MIN_ZOOM = 1
export const MAX_ZOOM = 2
export const ZOOM_STEP = 0.1

// Настройки тумана войны - простая прозрачность как в Fallout
export const FOG_TILE_OPACITY = 0.2 // Прозрачность клеток с туманом войны

// Настройки тайлов
export const TILE_GAP = -0.2 // размер отступа между тайлами

// Настройки тумана войны в локациях
export const LOCATION_FOG_SETTINGS = {
  MIN_VISION_RADIUS: 1,        // Минимальный радиус видимости
  MAX_VISION_RADIUS: 6,        // Максимальный радиус видимости
  PERCEPTION_MULTIPLIER: 1.5,  // Множитель восприятия (P * 1.5)
  DEFAULT_RADIUS: 2           // Радиус по умолчанию
};

// Настройки UI обновления
export const CAMERA_UI_UPDATE_INTERVAL = 10 // мс

// Настройки размеров текстур игрока
export const getPlayerTextureSize = () => useGameStore.getState().playerLocationPresent ? 200 : 400; // Размер текстур игрока в зависимости от наличия локации
// Настройки размеров текстур декораций
export const DECORATION_TEXTURE_SETTINGS = {
  // Базовые размеры (можно менять для экспериментов)
  DEFAULT_WIDTH: 64,
  DEFAULT_HEIGHT: 64,
  
  // Масштабирование для разных типов декораций
  SCALE_MULTIPLIERS: {
    FOREST: 1,      // Лес чуть больше
    MOUNTAINS: 1.17,   // Горы крупнее
    RIVER: 1.36,       // Река поменьше
    ROAD: 1.1,        // Дорога узкая
    SWAMP: 0.23,       // Болото стандартно
    CITY: 1,        // Город большой
    RUINS: 0.81,       // Руины средние
    LAKE: 1.21,        // Озеро чуть больше
    RUBBLE: 0.6,      // Обломки мелкие
    BRIDGE: 0.9,      // Мост средний
    BUSHES: 0.8,      // Кусты мелкие
    VILLAGE: 1.4,     // Деревня крупная
  },
  
  // Вертикальное смещение текстур (отрицательные значения - выше, положительные - ниже)
  VERTICAL_OFFSET: {
    FOREST: -15,      // Лес поднять выше
    MOUNTAINS: -6,   // Горы сильно поднять
    RIVER: -5,         // Река опустить ниже
    ROAD: -5,          // Дорога по центру
    SWAMP: -2,         // Болото чуть ниже
    CITY: -4,        // Город поднять
    RUINS: -4,        // Руины поднять
    LAKE: -2,          // Озеро чуть ниже
    RUBBLE: 0,        // Обломки по центру
    BRIDGE: -5,       // Мост чуть выше
    BUSHES: -11,        // Кусты ниже
    VILLAGE: -12,     // Деревня поднять
  },
  
  // Горизонтальное смещение текстур (отрицательные - влево, положительные - вправо)
  HORIZONTAL_OFFSET: {
    FOREST: 0,        // Лес по центру
    MOUNTAINS: 0,     // Горы по центру
    RIVER: 0,         // Река по центру
    ROAD: 0,          // Дорога по центру
    SWAMP: 0,         // Болото по центру
    CITY: 0,          // Город по центру
    RUINS: 2,         // Руины по центру
    LAKE: 0,          // Озеро по центру
    RUBBLE: 0,        // Обломки по центру
    BRIDGE: 0,        // Мост по центру
    BUSHES: 0,        // Кусты по центру
    VILLAGE: 0,       // Деревня по центру
  },
  
  // Настройки отрисовки
  ENABLE_SCALING: true,     // Включить/выключить масштабирование
  PRESERVE_ASPECT: true,    // Сохранять пропорции
  CENTER_ON_TILE: true,     // Центрировать на тайле
  ENABLE_OFFSET: true,      // Включить/выключить смещение
}

// Настройки размеров и поворота текстур местности для МИРОВОЙ КАРТЫ (только 3 типа)
export const WORLD_MAP_TERRAIN_TEXTURE_SETTINGS = {
  // Базовые размеры для текстур местности мировой карты
  DEFAULT_WIDTH: 80,
  DEFAULT_HEIGHT: 80,
  
  // Масштабирование ширины для разных типов местности на мировой карте
  WIDTH_SCALE: {
    GRASS: 1.0,        // Трава стандартно
    WATER: 1.0,        // Вода стандартно
    WASTELAND: 1.2,    // Пустошь шире
  },
  
  // Масштабирование высоты для разных типов местности на мировой карте
  HEIGHT_SCALE: {
    GRASS: 1.0,        // Трава стандартно
    WATER: 1.5,        // Вода стандартно
    WASTELAND: 0.6,    // Пустошь ниже (изометрическая)
  },
  
  // Углы поворота для разных типов местности (в радианах)
  ROTATION_ANGLE: {
    GRASS: 0,          // Трава без поворота
    WATER: 0,          // Вода без поворота
    WASTELAND: 0,      // Пустошь без поворота
  },
  
  // Вертикальное смещение текстур местности на мировой карте
  VERTICAL_OFFSET: {
    GRASS: 0,          // Трава по центру
    WATER: 0,          // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Горизонтальное смещение текстур местности на мировой карте
  HORIZONTAL_OFFSET: {
    GRASS: 0,          // Трава по центру
    WATER: 0,          // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Настройки отрисовки для мировой карты
  ENABLE_SCALING: true,      // Включить/выключить масштабирование
  ENABLE_ROTATION: true,     // Включить/выключить поворот
  ENABLE_OFFSET: true,       // Включить/выключить смещение
  CLIP_TO_DIAMOND: true,     // Обрезать по ромбу тайла
  SEPARATE_PROPORTIONS: true, // Использовать отдельные пропорции для ширины и высоты
}